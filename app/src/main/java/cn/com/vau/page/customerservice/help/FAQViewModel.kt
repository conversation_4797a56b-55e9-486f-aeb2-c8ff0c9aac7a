package cn.com.vau.page.customerservice.help

import android.text.TextUtils
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.util.ToastUtil

/**
 * Filename: FAQViewModel
 * Author: GG
 * Date: 2025/3/13
 * Description:
 */
class FAQViewModel : BaseViewModel() {

    var answerMap = hashMapOf<String, String?>()

    /**
     * 获取客服问题列表
     */
    fun getCSQuests() {
        requestNet({
            val params = hashMapOf<String, String>()
            if (!TextUtils.isEmpty(UserDataUtil.accountCd())) {
                params["userToken"] = UserDataUtil.loginToken()
            }
            params["stFaq"] = if (UserDataUtil.isStLogin()) "1" else ""
            baseService.consultQuests(params)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_QUESTION_LIST, it.data))
        }, isShowDialog = true)
    }

    fun consultAnswer(id: String) {
        requestNet({
            val params = hashMapOf<String, String>()
            if (!TextUtils.isEmpty(UserDataUtil.accountCd())) {
                params["userToken"] = UserDataUtil.loginToken()
            }
            params["questId"] = id
            baseService.consultAnswer(params)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            answerMap[id] = it.data?.obj?.answer
            sendEvent(DataEvent(EVENT_ANSWER_ITEM, it.data))
        }, isShowDialog = true)
    }

    companion object {
        const val EVENT_QUESTION_LIST = "EVENT_QUESTION_LIST"
        const val EVENT_ANSWER_ITEM = "EVENT_ANSWER_ITEM"
    }
}