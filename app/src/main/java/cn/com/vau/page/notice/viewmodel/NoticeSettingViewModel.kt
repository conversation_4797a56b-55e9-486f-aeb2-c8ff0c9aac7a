package cn.com.vau.page.notice.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.util.ToastUtil

class NoticeSettingViewModel : BaseViewModel() {

    init {
        msgInAppSettingsApi()
    }

    val disturbLiveData = MutableLiveData<Boolean>()

    fun msgInAppSettingsApi() {
        requestNet({
            baseService.msgInAppSettingsV1Api(UserDataUtil.loginToken())
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 0 是false  1是true
            disturbLiveData.value = it.data?.obj?.find { it.code == Constants.KEY_NO_NOTICE }?.value == KEY_OPEN
        })
    }

    fun msgInAppSettingUpdateApi() {
        requestNet({
            baseService.msgInAppSettingsUpdateV1Api(UserDataUtil.loginToken(), code = Constants.KEY_NO_NOTICE, if (disturbLiveData.value == true) KEY_CLOSE else KEY_OPEN)
        }, onSuccess = { data ->
            if (!data.isSuccess()) {
                ToastUtil.showToast(data.getResponseMsg())
                disturbLiveData.value = disturbLiveData.value
                return@requestNet
            }
            disturbLiveData.value = disturbLiveData.value != true
        })
    }

    companion object {

        const val KEY_OPEN = "1"
        const val KEY_CLOSE = "0"
    }
}