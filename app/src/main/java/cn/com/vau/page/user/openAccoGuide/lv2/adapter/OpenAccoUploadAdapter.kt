package cn.com.vau.page.user.openAccoGuide.lv2.adapter

import android.view.ViewGroup.LayoutParams
import androidx.appcompat.widget.AppCompatImageView
import cn.com.vau.R
import cn.com.vau.page.UploadBean
import cn.com.vau.util.*
import cn.com.vau.util.ImageLoaderUtil
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class OpenAccoUploadAdapter : BaseQuickAdapter<UploadBean, BaseViewHolder>(R.layout.item_grid_upload) {

    init {
        addChildClickViewIds(R.id.ivClose, R.id.clUpload)
    }

    override fun convert(holder: BaseViewHolder, item: UploadBean) {
        if (item.path.isNullOrEmpty() && item.uri == null) {    // Add Photo
            holder.setGone(R.id.ivClose, true)
                .setGone(R.id.ivUploadPreview, true)
                .setVisible(R.id.tvPhotoName, false)
                .setVisible(R.id.ivAddIcon, true)
            // 设置icon大小
            val icon = holder.getView<AppCompatImageView>(R.id.ivAddIcon)
            val lp = icon.layoutParams
            lp.width = 16.dp2px()
            lp.height = 16.dp2px()
            icon.layoutParams = lp
            icon.setImageResource(R.drawable.icon_source2_add)
        } else if (item.type == "pdf" || item.type == "doc" || item.type == "docx") { // 不可预览的
            holder.setGone(R.id.ivClose, false)
                .setGone(R.id.ivUploadPreview, true)
                .setVisible(R.id.tvPhotoName, true)
                .setText(R.id.tvPhotoName, "${context.getString(R.string.photo_name)} ${holder.bindingAdapterPosition + 1}")
                .setVisible(R.id.ivAddIcon, true)
                .setImageResource(R.id.ivAddIcon, R.drawable.img_source_upload)
            // 设置icon大小
            val icon = holder.getView<AppCompatImageView>(R.id.ivAddIcon)
            val lp = icon.layoutParams
            lp.width = LayoutParams.WRAP_CONTENT
            lp.height = 18.dp2px()
            icon.layoutParams = lp
        } else {                                                                      // 可预览的
            holder.setGone(R.id.ivClose, false)
                .setGone(R.id.ivUploadPreview, false)
                .setVisible(R.id.tvPhotoName, true)
                .setVisible(R.id.ivAddIcon, false)
                .setText(R.id.tvPhotoName, "${context.getString(R.string.photo_name)} ${holder.bindingAdapterPosition + 1}")
            val photoView = holder.getView<AppCompatImageView>(R.id.ivUploadPreview)
            val loadEntry = if (item.path.isNullOrEmpty()) item.uri else item.path
            val options = RequestOptions().placeholder(R.drawable.shape_placeholder).error(R.drawable.shape_placeholder)
            ImageLoaderUtil.loadImageWithOption(photoView, loadEntry, photoView, options)
        }
    }
}