package cn.com.vau.page.user.sumsub

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.BaseDataBindingActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.page.user.openAccoGuide.lv2.OpenAccoGuideLv2Activity
import cn.com.vau.page.user.openAccoGuide.lv3.OpenAccoGuideLv3Activity
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2024/11/15
 * desc：判断跳转sumsub还是继续原有逻辑
 */
object SumSubJumpHelper {

    /**
     * 判断是否跳转sumsub的流程
     * 如果账号是需要kyc验证的账号 ，就跳转对应的 kyc验证流程
     * 否则跳转 kyc需求以前的跳转sumsub的逻辑
     */
    fun isJumpSumSub(context: Context?, type: String, bundle: Bundle? = null, endFlow: (() -> Unit)? = null) {
        if (SpManager.isV1V2()) {
            KycVerifyHelper.showKycDialog(
                context,
                mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_KYC, Constants.GoldParam.NEXT_LEVEL to type),
                endFlow
            )
        } else {
            oldSumsubFlow(context, type, bundle, endFlow)
        }
    }

    /**
     * kyc需求以前的 是否跳转sumsub的流程
     * 是否跳转到sumsub
     * @param context
     * @param type 2 poi lv2 开户
     *             3 poa lv3 开户
     * endFlow结束后流程
     */
    private fun oldSumsubFlow(context: Context?, type: String, bundle: Bundle? = null, endFlow: (() -> Unit)? = null) {
        val activity = context as? AppCompatActivity ?: ActivityManagerUtil.getInstance().activityStack.last() as AppCompatActivity
        val viewModel = ViewModelProvider(activity)[SumSubJumpViewModel::class.java]
        showLoading(activity)
        viewModel.isJumpSumSub(activity, type) { isPass ->
            if (isPass) {
                SumsubPromptActivity.openActivity(activity, type = type)
            } else {
                oldFlow(activity, type, bundle)
            }
            hideLoading(activity)
            endFlow?.invoke()
        }
    }

    /**
     * 继续走原有逻辑
     */
    private fun oldFlow(context: Context, type: String, bundle: Bundle? = null) {
        if (type == Constants.SUMSUB_TYPE_POI) {
            jumpToLv2(context, bundle)
        } else { // 直播
            jumpToLv3(context, bundle)

        }
    }

    /**
     * 跳转lv2 开户
     */
    private fun jumpToLv2(context: Context?, bundle: Bundle? = null) {
        val intent = Intent(context, OpenAccoGuideLv2Activity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        bundle?.let {
            intent.putExtras(it)
        }
        context?.startActivity(intent)
    }

    /**
     * 跳转lv3 开户
     */
    private fun jumpToLv3(context: Context?, bundle: Bundle? = null) {
        val intent = Intent(context, OpenAccoGuideLv3Activity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        bundle?.let {
            intent.putExtras(it)
        }
        context?.startActivity(intent)
    }

    private fun showLoading(activity: AppCompatActivity) {
        if (activity is BaseMvvmBindingActivity<*>) {
            activity.showLoadDialog()
        } else if (activity is BaseDataBindingActivity<*>) {
            activity.showLoadDialog()
        } else if (activity is BaseActivity) {
            activity.showNetDialog()
        }
    }

    private fun hideLoading(activity: AppCompatActivity) {
        if (activity is BaseMvvmBindingActivity<*>) {
            activity.hideLoadDialog()
        } else if (activity is BaseDataBindingActivity<*>) {
            activity.hideLoadDialog()
        } else if (activity is BaseActivity) {
            activity.hideNetDialog()
        }
    }

    class SumSubJumpViewModel : BaseViewModel() {
        fun isJumpSumSub(context: Context?, type: String, isPassCallback: (Boolean) -> Unit) {
            if (context == null) return
            noRepeat {
                val map = hashMapOf<String, Any?>()
                map["userToken"] = UserDataUtil.loginToken()
                map["type"] = type
                requestNet({ baseService.sumsubIsJump(map) }, {
                    if (it.isSuccess()) {
                        isPassCallback.invoke(it.data?.obj == true)
                    } else {
                        // 跟鹏哥确认过接口没返回成功，只会返回token过期让重新登录，所以这里不需要再跳转lv2了
                        ToastUtil.showToast(it.getResponseMsg())
                        isPassCallback.invoke(false)
                    }
                }, onError = {
                    // 接口报错继续走原有逻辑，避免流程卡住
                    isPassCallback.invoke(false)
                })
            }
        }
    }
}