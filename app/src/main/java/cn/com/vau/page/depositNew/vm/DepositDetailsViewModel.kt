package cn.com.vau.page.depositNew.vm

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.depositcoupon.ManageFundsDetailsObj
import cn.com.vau.util.ToastUtil

class DepositDetailsViewModel : BaseViewModel() {

    var orderNo: String = ""
    var accountID: String = ""

    var fundDetailLiveData = MutableLiveData<ManageFundsDetailsObj?>()
    var fundRetryLiveData = MutableLiveData<Unit>()

    fun fundMoneyInDetail() {
        val params: HashMap<String, Any> = hashMapOf(
            "userToken" to UserDataUtil.loginToken(),
            "accountId" to accountID,
            "orderNo" to orderNo,
        )
        requestNet({ baseService.fundMoneyInDetailApi(params)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            fundDetailLiveData.postValue(it.data?.obj)
        }, isShowDialog = true)
    }

    fun fundDetailsRetry() {
        val params: HashMap<String, Any> = hashMapOf(
            "userToken" to UserDataUtil.loginToken(),
            "accountId" to accountID,
            "orderNo" to orderNo,
        )
        requestNet({ baseService.fundFundDetailsRetryApi(params)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            fundRetryLiveData.postValue(Unit)
        }, isShowDialog = true)
    }
}