package cn.com.vau.page.login.viewmodel

import androidx.lifecycle.LiveData
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.livedata.UnPeekLiveData
import cn.com.vau.common.storage.SpManager
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2025/03/17
 * desc：
 */
class SendCodeViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null // 注册的实体bean

    // 账号（手机号/邮箱）已存在
    private val _accountExistsLiveData = UnPeekLiveData<String>()
    val accountExistsLiveData: LiveData<String> = _accountExistsLiveData

    // 发送验证码时触发滑块验证
    private val _showCaptchaLiveData = UnPeekLiveData<String>()
    val showCaptchaLiveData: LiveData<String> = _showCaptchaLiveData

    // 验证码发送成功
    private val _sendCodeSuccessLiveData = UnPeekLiveData<String>()
    val sendCodeSuccessLiveData: LiveData<String> = _sendCodeSuccessLiveData

    // 验证邮箱验证码成功
    private val _validateEmailCodeSuccessLiveData = UnPeekLiveData<Boolean>()
    val validateEmailCodeSuccessLiveData: LiveData<Boolean> = _validateEmailCodeSuccessLiveData

    // 验证sms验证码成功
    private val _validateSmsCodeSuccessLiveData = UnPeekLiveData<Boolean>()
    val validateSmsCodeSuccessLiveData: LiveData<Boolean> = _validateSmsCodeSuccessLiveData

    // 接口请求失败（自己处理的loading在接口报错无法关闭时使用这个监听，主要用于非新mvvm框架调用这个viewModel时的处理，全部改为新mvvm框架后可删除）
    private val _requestErrorLiveData = UnPeekLiveData<String>()
    val requestErrorLiveData: LiveData<String> = _requestErrorLiveData

    // 密码校验成功
    private val _checkPwdSuccessLiveData = UnPeekLiveData<String?>()
    val checkPwdSuccessLiveData: LiveData<String?> = _checkPwdSuccessLiveData

    private val sendCodeUtil by lazy { SendCodeUtil() }

    fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener) {
        sendCodeUtil.initData(60, listener)
    }

    fun startSendCodeUtil() {
        if (sendCodeUtil.isAlive() != false) {
            sendCodeUtil.start()
        }
    }

    fun stopSendCodeUtil() {
        sendCodeUtil.cancel()
    }

    /**
     * 获取注册验证码
     */
    fun getTelRegisterSmsApi(validate: String? = null) {
        val phoneCountryCode = signUpRequestBean?.countryCode
        val code = signUpRequestBean?.countryNum
        val mobile = signUpRequestBean?.mobile
        // 判断手机号是否正确
        if (mobile.isNullOrBlank() || (code == "86" && mobile.length != 11) || (code != "86" && mobile.length > 15)) {
            hideLoading()
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_number))
            _requestErrorLiveData.value = ""
            return
        }
        // 是否同意协议
        if (signUpRequestBean?.readingProtocol == false) {
            hideLoading()
            ToastUtil.showToast(StringUtil.getString(R.string.please_read_and_registration_agreement))
            _requestErrorLiveData.value = ""
            return
        }

        val map = hashMapOf<String, Any?>()
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate
            map["smsCodeId"] = SpManager.getSmsCodeId()
        }
        map["userTel"] = mobile.ifNull()
        map["phoneCountryCode"] = phoneCountryCode
        map["code"] = code
        map["smsSendType"] = signUpRequestBean?.sendCodeType?.value
        requestNet({ baseService.getTelRegisterSmsApi(map) }, onSuccess = {
            SpManager.putSmsCodeId("")
            if (it.getResponseCode() == "V10030") { // 手机号存在
                _accountExistsLiveData.value = it.getResponseMsg()
                return@requestNet
            }
            if (it.getResponseCode() == "V10060") { // 网易易盾验证
                SpManager.putSmsCodeId(it.data?.obj?.smsCodeId.ifNull())
                _showCaptchaLiveData.value = ""
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
                return@requestNet
            }
            // 发送注册验证码成功
            ToastUtil.showToast(it.getResponseMsg())
            _sendCodeSuccessLiveData.value = ""
        }, onError = {
            _requestErrorLiveData.value = ""
        }, isShowDialog = true)
    }

    /**
     * 验证注册验证码
     */
    fun smsValidateSmsRegisterCodeApi() {
        val code = signUpRequestBean?.countryNum
        val mobile = signUpRequestBean?.mobile
        // 判断手机号是否正确
        if (mobile.isNullOrBlank() || (code == "86" && mobile.length != 11) || (code != "86" && mobile.length > 15)) {
            hideLoading()
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_number))
            _requestErrorLiveData.value = ""
            return
        }
        // 判断验证码是否输入完整
        val smsCode = signUpRequestBean?.smsCode
        if (smsCode?.length != 6) {
            hideLoading()
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_code))
            _requestErrorLiveData.value = ""
            return
        }
        // 是否同意协议
        if (signUpRequestBean?.readingProtocol == false) {
            hideLoading()
            ToastUtil.showToast(StringUtil.getString(R.string.please_read_and_registration_agreement))
            _requestErrorLiveData.value = ""
            return
        }

        val map = hashMapOf<String, Any?>()
        map["phoneNum"] = mobile
        map["validateCode"] = smsCode
        map["code"] = code
        requestNet({ baseService.smsValidateSmsRegisterCodeApi(map) }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
                return@requestNet
            }
            _validateSmsCodeSuccessLiveData.value = true
        }, onError = {
            _requestErrorLiveData.value = ""
        }, isShowDialog = true)
    }

    /**
     * kyc-发送邮箱验证码（公共接口）
     *
     * bizType :
     * 1 - 修改密码
     * 4 - PC第一次登录发送邮箱OTP
     * 5 - 新增资金密码
     * 6 - 忘记（重置）资金密码
     * 10 - 用户切换设备时的登录验证(邮箱OTP验证)
     * 18 - 修改TOTP（2FA换绑）
     * 25 - 认证邮箱（安全页、个人信息页）
     * 26 - 绑定邮箱（安全页、个人信息页）
     * 27 - 修改邮箱验证老邮箱（安全页、个人信息页）
     * 28 - 修改邮箱验证新邮箱（安全页、个人信息页）
     */
    fun sendEmailCodeApi(type: String, validate: String? = null) {
        val email = signUpRequestBean?.email
        if (!RegexUtil.isEmail(email)) {
            hideLoading()
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_correct_mail))
            _requestErrorLiveData.value = ""
            return
        }

        val map = hashMapOf<String, Any?>()
        if (type != "4" && type != "10") {
            map["token"] = UserDataUtil.loginToken() // 用户token
        }
        map["email"] = email // 邮箱
        map["bizType"] = type // 验证码验证类型
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate
            map["smsCodeId"] = SpManager.getSmsCodeId()
        }
        requestNet({ baseService.emailSendEmailCodeApi(map) }, onSuccess = {
            SpManager.putSmsCodeId("")
            if (it.getResponseCode() == "V10060") { // 网易易盾验证
                SpManager.putSmsCodeId(it.getResponseData()?.obj?.smsCodeId.ifNull())
                _showCaptchaLiveData.value = type
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
                return@requestNet
            }
            ToastUtil.showToast(it.getResponseMsg())
            // 赋值这俩值，方便v层传递参数
            signUpRequestBean?.txId = it.data?.obj?.txId
            _sendCodeSuccessLiveData.value = type
        }, onError = {
            _requestErrorLiveData.value = ""
        }, isShowDialog = true)
    }

    /**
     * kyc-验证邮件验证码（公共接口）
     */
    fun validateEmailCodeApi(type: String, isAutoDismissDialog: Boolean = true) {
        val map = hashMapOf<String, Any?>()
        if (UserDataUtil.isLogin()) {
            map["token"] = UserDataUtil.loginToken() // 用户token
        }
        map["email"] = signUpRequestBean?.email // 邮箱
        map["bizType"] = type
        map["txId"] = signUpRequestBean?.txId // 验证ID
        map["code"] = signUpRequestBean?.smsCode // 验证码
        requestNet({ baseService.emailPreValidateEmailCodeApi(map) }, onSuccess = {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
                return@requestNet
            }
            _validateEmailCodeSuccessLiveData.value = true
        }, onError = {
            _requestErrorLiveData.value = ""
        }, isShowDialog = true, isAutoDismissDialog = isAutoDismissDialog)
    }

    /**
     * 发送 sms 验证码
     *
     * type :
     * 1 - 修改密码
     * 3 - 修改手机号，获取旧手机OTP
     * 4 - 修改手机号，获取新手机OTP
     * 5 - 新增资金密码
     * 6 - 忘记（重置）资金密码
     * 10 - 用户切换设备时的登录验证(手机号OTP验证)
     * 18 - 修改TOTP（2FA换绑）
     * 25 - 认证手机号（安全页、个人信息页）
     * 26 - 绑定手机号（安全页、个人信息页）
     */
    fun sendPhoneCodeApi(type: String, validate: String? = null) {
        val code = signUpRequestBean?.countryNum
        val mobile = signUpRequestBean?.mobile
        // 判断手机号是否正确
        if (mobile.isNullOrBlank() || (code == "86" && mobile.length != 11) || (code != "86" && mobile.length > 15)) {
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_number))
            return
        }

        val map = hashMapOf<String, Any?>()
        map["phoneCountryCode"] = signUpRequestBean?.countryCode
        map["code"] = code
        map["userTel"] = mobile
        map["type"] = type
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate // 滑块验证码
            map["smsCodeId"] = SpManager.getSmsCodeId() // 日志记录ID
        }
        map["smsSendType"] = signUpRequestBean?.sendCodeType?.value
        if (!signUpRequestBean?.pwd.isNullOrBlank()) {
            map["userPassword"] = signUpRequestBean?.pwd
        }
        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({ baseService.getTelSmsApi(aesMap) }, onSuccess = {
            SpManager.putSmsCodeId("")
            if (it.getResponseCode() == "V10060") { // 网易易盾验证
                SpManager.putSmsCodeId(it.getResponseData()?.obj?.smsCodeId.ifNull())
                _showCaptchaLiveData.value = type
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
                return@requestNet
            }
            ToastUtil.showToast(it.getResponseMsg())
            _sendCodeSuccessLiveData.value = type
        }, onError = {
            _requestErrorLiveData.value = ""
        }, isShowDialog = true)
    }

    /**
     * 通用校验 sms 验证码
     */
    fun validateSmsCodeApi(type: String, isAutoDismissDialog: Boolean = true) {
        val smsCode = signUpRequestBean?.smsCode
        val countryNum = signUpRequestBean?.countryNum
        val mobile = signUpRequestBean?.mobile
        requestNet({
            baseService.smsValidateSmsCodeApi(validateCode = smsCode, code = type, nationalCode = countryNum, phoneNum = mobile)
        }, onSuccess = {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
                return@requestNet
            }
            _validateSmsCodeSuccessLiveData.value = true
        }, onError = {
            _requestErrorLiveData.value = ""
        }, isShowDialog = true, isAutoDismissDialog = isAutoDismissDialog)
    }

    /**
     * kyc-校验登录密码是否正确
     */
    fun checkUserPasswordApi(validateCode: String? = null) {
        val map = hashMapOf<String, Any?>()
        map["password"] = AESUtil.encryptAES(signUpRequestBean?.pwd.ifNull(), AESUtil.PWD_AES_KEY)
        requestNet({ baseService.userCheckUserPasswordApi(map) }, {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
                return@requestNet
            }
            if (it.getResponseData()?.obj == true) {
                _checkPwdSuccessLiveData.value = validateCode
            } else {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                _requestErrorLiveData.value = ""
            }
        }, isShowDialog = true, isAutoDismissDialog = false)
    }
}