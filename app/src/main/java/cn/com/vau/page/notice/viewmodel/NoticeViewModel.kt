package cn.com.vau.page.notice.viewmodel

import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.msg.MsgInAppTypeData
import cn.com.vau.util.*
import org.greenrobot.eventbus.EventBus

/**
 * @description:
 * @author: GG
 * @createDate: 2024 12月 17 13:42
 * @updateUser:
 * @updateDate: 2024 12月 17 13:42
 */
class NoticeViewModel : BaseViewModel() {

    init {
        msgInAppTypeApi()
    }

    val titleList by lazy {
        mutableListOf<MsgInAppTypeData.Obj>().apply {
            add(MsgInAppTypeData.Obj(name = StringUtil.getString(R.string.signal_trade), code = TYPE_TRADE))
            if (UserDataUtil.isOpenStAccount())
                add(MsgInAppTypeData.Obj(name = StringUtil.getString(R.string.copy_trading), code = TYPE_COPY_TRADING))
            add(MsgInAppTypeData.Obj(name = StringUtil.getString(R.string.price_alerts), code = TYPE_PRICE_ALERT))
            add(MsgInAppTypeData.Obj(name = StringUtil.getString(R.string.account), code = TYPE_ACCOUNT))
            add(MsgInAppTypeData.Obj(name = StringUtil.getString(R.string.announcements), code = TYPE_ANNOUNCEMENT))
            // asic 和 fca 监管 名字不一样
            if (SpManager.getSuperviseNum() == "1" || SpManager.getSuperviseNum() == "13") {
                add(MsgInAppTypeData.Obj(name = StringUtil.getString(R.string.news), code = TYPE_NEWS_EVENTS))
            } else {
                add(MsgInAppTypeData.Obj(name = StringUtil.getString(R.string.news_events), code = TYPE_NEWS_EVENTS))
            }
        }
    }

    /**
     * 未读消息总计
     */
    var unReadNoticeCount = 0

    /**
     * 消息列表名称和未读消息数量
     */
    fun msgInAppTypeApi() {
        requestNet({
            baseService.msgInAppTypeApi(token = UserDataUtil.loginToken())
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 遍历消息列表，将未读消息数量赋值给titleList
            it.data?.obj?.forEach { data ->
                titleList.find { data?.code == it.code }?.count = data?.count
                if (data?.count.ifNullToInt(0) != 0) {
                    unReadNoticeCount = unReadNoticeCount + data?.count.ifNullToInt(0)
                    sendEvent(DataEvent(tag = EVENT_SHOW, data = data))
                }
            }
            // 有未读通知的话就记录到本地，并发送通知，行情tab和订单tab会收到通知并刷新页面
            if (unReadNoticeCount > 0) {
                SpManager.putRedPointState(true)
                EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_MSG_SHOW)
            } else {
                SpManager.putRedPointState(false)
                EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_MSG_HIDE)
            }
        })
    }

    /**
     * 一键全部已读
     */
    fun msgInAppReadAllApi() {
        requestNet({
            baseService.msgInAppReadAllApi(token = UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            unReadNoticeCount = 0
            // 发送通知 消息已经全部已读
            SpManager.putRedPointState(false)
            EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_MSG_HIDE)
            sendEvent(DataEvent(tag = EVENT_READ_ALL))
        }, isShowDialog = true)
    }

    companion object {
        const val TYPE_TRADE = "TRADE"
        const val TYPE_COPY_TRADING = "COPY_TRADING"
        const val TYPE_PRICE_ALERT = "PRICE_ALERT"
        const val TYPE_ACCOUNT = "ACCOUNT"
        const val TYPE_ANNOUNCEMENT = "ANNOUNCEMENT"
        const val TYPE_NEWS_EVENTS = "NEWS_EVENTS"

        const val EVENT_SHOW = "SHOW"
        const val EVENT_READ = "READ"
        const val EVENT_READ_ALL = "READ_ALL"
    }
}