package cn.com.vau.page.user.openAccoGuide.lv1.adapter

import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.ifNull
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import java.util.Locale

class OpenAccoPlatAdapter :
    BaseQuickAdapter<PlatFormAccountData.Obj, BaseViewHolder>(R.layout.item_open_lv1_acco_plat) {

    var selectIndex = 0

    override fun convert(holder: BaseViewHolder, item: PlatFormAccountData.Obj) {
        ImageLoaderUtil.loadImage(
            context,
            item.listImage?.elementAtOrNull(0)?.imgUrl,
            holder.getView(R.id.iv_plat_icon)
        )
        holder.setText(R.id.tv_name, item.displayPlatFormName)
        when (item.platFormName?.lowercase(Locale.getDefault())) {
            "mt4" ->
                holder.setText(R.id.tv_assets_tip, item.platTypeTitle?.mt4Title.ifNull())
                    .setText(R.id.tv_support_tip, item.platTypeTitle?.mt4Content.ifNull())
                    .setVisible(R.id.tvLabel, false)
                    .setText(R.id.tvDesc, context.getString(R.string.basic_trading_account))

            "mt5" ->
                holder.setText(R.id.tv_assets_tip, item.platTypeTitle?.mt5Title.ifNull())
                    .setText(R.id.tv_support_tip, item.platTypeTitle?.mt5Content.ifNull())
                    .setText(R.id.tvLabel, context.getString(R.string.recommended))
                    .setVisible(R.id.tvLabel, true)
                    .setBackgroundResource(R.id.tvLabel, R.drawable.shape_solid_ce35728_top_r10)
                    .setText(R.id.tvDesc, context.getString(R.string.advanced_trading_account))

            "mts" ->
                holder.setText(R.id.tv_assets_tip, item.platTypeTitle?.mtsTitle.ifNull())
                    .setText(R.id.tv_support_tip, item.platTypeTitle?.mtsContent.ifNull())
                    .setText(R.id.tvLabel, context.getString(R.string.popular))
                    .setVisible(R.id.tvLabel, true)
                    .setBackgroundResource(R.id.tvLabel, R.drawable.shape_solid_c034854_top_r10)
                    .setText(R.id.tvDesc, context.getString(R.string.copy_trading_account))

            // 这里也加上VTS账户的判断----------------------------因为是共用的类 可以与CopyTrading逻辑一起写
            "vts" -> holder.setText(R.id.tv_assets_tip, item.platTypeTitle?.vtsTitle.ifNull())
                .setText(R.id.tv_support_tip, item.platTypeTitle?.vtsContent.ifNull())
                .setText(R.id.tvLabel, context.getString(R.string.self_developed))
                .setVisible(R.id.tvLabel, true)
                .setBackgroundResource(R.id.tvLabel, R.drawable.shape_solid_c034854_top_r10)
                .setText(R.id.tvDesc, context.getString(R.string.vantage_trading_system))

        }
        holder.itemView.background = ContextCompat.getDrawable(
            context,
            if (selectIndex == holder.bindingAdapterPosition)
                R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10
            else
                R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10
        )
    }
}