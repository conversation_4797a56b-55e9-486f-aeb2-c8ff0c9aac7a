package cn.com.vau.page.login.viewmodel

import androidx.lifecycle.*
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.page.login.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.FirebaseManager
import org.greenrobot.eventbus.EventBus

/**
 * author：lvy
 * date：2025/03/29
 * desc：
 */
class LoginViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null

    // 是否绑定了2fa
    private val _isBind2Fa = MutableLiveData<Boolean>()
    val isBind2Fa: LiveData<Boolean> = _isBind2Fa

    // 登录时触发滑块验证
    private val _loginShowCaptchaLiveData = MutableLiveData<Boolean>()
    val loginShowCaptchaLiveData: LiveData<Boolean> = _loginShowCaptchaLiveData

    /**
     * 手机号登录
     */
    fun loginPhoneApi(type: String, validate: String? = null) {
        val map = hashMapOf<String, Any?>()
        map["countryCode"] = signUpRequestBean?.countryCode // 国家代号，如：FR
        map["code"] = signUpRequestBean?.countryNum // 手机区号，如：33
        map["count"] = signUpRequestBean?.mobile // 手机号
        map["userPassword"] = signUpRequestBean?.pwd // 密码
        map["validateCode"] = signUpRequestBean?.smsCode // 验证码
        if (signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
            map["txId"] = signUpRequestBean?.txId // 邮箱验证id
        }
        map["type"] = type // 手机号传10
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate  // 滑块验证码
        }

        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({ baseService.loginNewApi(aesMap) }, {
            dealLoginResult(it)
        }, isShowDialog = true)
    }

    /**
     * 邮箱登录
     */
    fun loginEmailApi(type: String, validate: String? = null) {
        val map = hashMapOf<String, Any?>()
        map["count"] = signUpRequestBean?.email // 邮箱
        map["userPassword"] = signUpRequestBean?.pwd // 密码
        map["validateCode"] = signUpRequestBean?.smsCode // 验证码
        map["txId"] = signUpRequestBean?.txId // 邮箱验证id
        map["type"] = type // 邮箱传18
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate  // 滑块验证码
        }

        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({ baseService.loginNewApi(aesMap) }, {
            dealLoginResult(it)
        }, isShowDialog = true)
    }

    /**
     * 三方登录
     */
    fun thirdLoginApi(type: String, validate: String? = null) {
        val map = hashMapOf<String, Any?>()
        map["validateCode"] = signUpRequestBean?.smsCode // 验证码
        if (signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
            map["txId"] = signUpRequestBean?.txId // 邮箱验证id
        }
        map["type"] = type
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate  // 滑块验证码
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({ baseService.thirdpartyLoginApi(paramMap) }, {
            dealLoginResult(it)
        }, isShowDialog = true)
    }

    private fun dealLoginResult(it: ApiResponse<LoginDataBean>) {
        when (it.getResponseCode()) {
            "V10016", "V10017" -> {
                // 保存用户信息
                saveUserData(it.getResponseCode(), it.getResponseData()?.obj)
                // 判断是否绑定了2fa
                if (signUpRequestBean?.fromPage == FromPageType.FROM_PAGE_2FA) {
                    _isBind2Fa.value = true // 从2FA页面来的，必然绑定了，所以直接返回true
                } else {
                    val isBind2FA = it.data?.obj?.twoFactorUser == true
                    val userid = it.data?.obj?.userId.ifNull()
                    val isBind = SpManager.getUser2faBindEd(userid, false)
                    _isBind2Fa.value = isBind2FA || isBind
                }
            }

            "V10060" -> {
                _loginShowCaptchaLiveData.value = true
            }

            else -> {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }
    }

    private fun saveUserData(resCode: String, obj: LoginObjBean?) {
        // 用户信息缓存
        UserDataUtil.setUserTel(obj?.userTel)
        UserDataUtil.setCountryCode(obj?.countryCode)
        UserDataUtil.setAreaCode(obj?.code)
        UserDataUtil.setUserId(obj?.userId)
        UserDataUtil.setUserType(if (resCode == "V10017") 1 else 0)
        UserDataUtil.setLoginToken(obj?.token)
        UserDataUtil.setXToken(obj?.xtoken)
        UserDataUtil.setFastCloseState(obj?.fastCloseState.ifNull("2"))
        UserDataUtil.setFastStopCopyState(obj?.fastCloseCopyOrder.ifNull("2")) // 快速停止跟单
        UserDataUtil.setOrderConfirmState(obj?.orderConfirmation.ifNull("2"))
        UserDataUtil.setEmail(obj?.email)
        UserDataUtil.setUserNickName(obj?.userNick)
        UserDataUtil.setUserPic(obj?.pic)
        UserDataUtil.setUserPassword(signUpRequestBean?.pwd)

        // sp缓存
        SpManager.putSuperviseNum(obj?.regulator.ifNull("0"))

        // 账号保存到数据库
        if (signUpRequestBean?.handleType == LoginHandleType.EMAIL) {
            if (!obj?.email.isNullOrBlank()) {
                val userEmailHistory = UserEmailHistory()
                userEmailHistory.email = obj.email.ifNull()
                DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
            }
        } else {
            if (!obj?.userTel.isNullOrBlank()) {
                val userPhoneHistory = UserPhoneHistory()
                userPhoneHistory.phoneNumber = obj.userTel.ifNull()
                DbManager.getInstance().saveUserPhoneHistory(userPhoneHistory)
            }
            // sp缓存
            SpManager.putUserTel(obj?.userTel.ifNull())
            SpManager.putCountryCode(obj?.countryCode.ifNull())
            SpManager.putCountryNum(obj?.code.ifNull())
            SpManager.putCountryName(signUpRequestBean?.countryName.ifNull())
        }

        // 埋点相关
        LogEventUtil.mFirebaseAnalytics.setUserId(obj?.code + obj?.userTel)
        // 绑定神策业务ID，场景4、场景2
        SensorsDataUtil.bindBusinessIdForLogin(obj?.userTel, obj?.email, obj?.emailEventID, obj?.crmUserId)

        // 登录firebase
        FirebaseManager.userLogin()

        // Fcm上报推送设备号
        val localFcmToken = SpManager.getTokenFcm()
        FirebaseManager.bindFcmToken(localFcmToken)

        // 三方登录后，刷新个人信息数据，目的是为了刷新顶部 telegram 的图标状态
        if (signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
            EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
        }
    }
}