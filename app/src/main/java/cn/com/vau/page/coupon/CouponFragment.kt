package cn.com.vau.page.coupon

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.page.coupon.couponUse.CouponDetailActivity
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.ui.deal.activity.LossOrderActivity
import cn.com.vau.util.*
import cn.com.vau.util.widget.NoDataView
import cn.com.vau.util.widget.dialog.CenterActionDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 02 17:14
 * @updateUser:
 * @updateDate: 2025 4月 02 17:14
 */
class CouponFragment : BaseMvvmFragment<FragmentRefreshBinding, CouponViewModel>() {

    private val activityViewModel by activityViewModels<CouponManagerViewModel>()

    private val type by lazy { arguments?.getInt(KEY_TYPE) ?: TYPE_ACTIVE }

    private val couponAdapter by lazy {
        CouponAdapter(type).apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_coupon))
            })
            // 添加稳定 ID 防止布局重绘
            setHasStableIds(true)
            // 关闭默认动画
            stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT_WHEN_EMPTY
        }
    }

    private val itemDecoration by lazy {
        DividerItemDecoration(12.dp2px(), lastDividerSize = 20.dp2px(), firstDividerSize = 12.dp2px())
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.type = type
    }

    override fun initView() {
        mBinding.mRefreshLayout.setEnableLoadMore(false)
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.getCouponList()
        }
        mBinding.mRecyclerView.addItemDecoration(itemDecoration)
        mBinding.mRecyclerView.adapter = couponAdapter
        couponAdapter.setNbOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.tvButton -> {
                    val currentCoupon = couponAdapter.data.getOrNull(position) ?: return@setNbOnItemChildClickListener
                    itemClickCheck(currentCoupon, true)
                }

                R.id.tvDetails -> {
                    NewHtmlActivity.openActivity(requireContext(), url = couponAdapter.data.getOrNull(position)?.infoUrl ?: "", getString(R.string.deposit_coupon))
                    mViewModel.sensorsButtonTrack("Terms&Conditions")
                }
            }
        }
        couponAdapter.setNbOnItemClickListener { adapter, view, position ->
            // 非 可使用的列表 item点击无效果
            if (type != TYPE_ACTIVE) {
                return@setNbOnItemClickListener
            }
            val currentCoupon = couponAdapter.data.getOrNull(position) ?: return@setNbOnItemClickListener

            itemClickCheck(currentCoupon)
        }
    }

    override fun initData() {
        super.initData()
        mViewModel.getCouponList()
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, couponAdapter, mBinding.mRefreshLayout, after = {
            // 埋点 第一次需要 网络请求结束才能知道是否有优惠券列表
            mViewModel.isFirst = false
            mViewModel.sensorsTrack(couponAdapter.data.isNotEmpty())
        })
        lifecycleScope.launch {
            activityViewModel.eventFlow.collectLatest { event ->
                if (event !is DataEvent) return@collectLatest
                when (event.tag) {
                    CouponManagerViewModel.EVENT_GET_ACCOUNTTYPEDATA_SUCCESS -> {
                        activityViewModel.accountTypeObj?.let {
                            if (event.data is DepositCouponDetail)
                                itemClick(event.data, it)
                        }
                    }

                    // 切换页面
                    CouponManagerViewModel.EVENT_RESUME_REFRESH,
                        // 优惠券兑换成功
                    CouponManagerViewModel.EVENT_USERCOUPON_EXCHANGE_SUCCESS -> {
                        mViewModel.getCouponList()
                    }
                }
            }
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.collectLatest { event ->
                if (event !is DataEvent) return@collectLatest
                when (event.tag) {
                    CouponViewModel.EVENT_COUPON_ACTIVATE -> {
                        if (event.data is Pair<*, *>) {
                            val data = event.data as Pair<String?, ApiResponse<*>>
                            showActivateDialog(data.first, data.second)
                        }
                    }

                }
            }
        }
    }

    /**
     * 优惠券item 点击事件 ,先检查用户账户状态是否可用
     */
    private fun itemClickCheck(currentCoupon: DepositCouponDetail, isClickButton: Boolean = false) {
        if (currentCoupon.userCouponStatus == "2" && (currentCoupon.couponType == "1" || currentCoupon.couponType == "6" || currentCoupon.couponType == "10")) {
            if (isClickButton) {
                showReleaseDialog(currentCoupon)
                mViewModel.sensorsButtonTrack("Release")
            }
            return
        }
        // 按钮展示activated 此时按钮点击无反应
        if (currentCoupon.userCouponStatus == "4") {
            return
        }
        // 显示进度条的时候 也不可点击
        if (currentCoupon.requiredLots != null && currentCoupon.requiredLots != 0.0) {
            return
        }
        // 该现金券仅在满足活动条件后可以使用，详见T&C
        if ("0" == currentCoupon.userCouponStatus) {
            ToastUtil.showToast(getString(R.string.the_voucher_can_find_tcs))
            return
        }

        if (activityViewModel.accountTypeObj == null) {
            activityViewModel.queryStAccountType(currentCoupon, true, true)
        } else {
            activityViewModel.accountTypeObj?.let {
                itemClick(currentCoupon, it)
                mViewModel.sensorsButtonTrack("Use")
            }
        }
    }

    /**
     * 优惠券item 点击事件
     */
    private fun itemClick(currentCoupon: DepositCouponDetail?, accountTypeObj: MT4AccountTypeObj) {
        val bundle = Bundle()
        bundle.putSerializable("currentCoupon", currentCoupon)

        val accountApplyType = accountTypeObj.applyTpe
        val accountStatus = accountTypeObj.status

        when (accountApplyType) {
            1, 3 -> startOpenAccountActivity(accountTypeObj)
            6 -> {
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
                requireActivity().finish()
            }
            // 已开户
            2, 8, 9 -> startCouponNextActivity(currentCoupon, bundle)
            else -> {
                // && 不是抹亏券
                if (accountStatus == 5 && "5" != currentCoupon?.couponType) {
                    startCouponNextActivity(currentCoupon, bundle)
                } else {
                    startOpenAccountActivity(accountTypeObj)
                }
            }
        }
    }

    private fun showReleaseDialog(currentCoupon: DepositCouponDetail) {
        CenterActionDialog.Builder(requireActivity())
            .setContent(getString(R.string.is_the_release_after_the_deposit_order)) //设置内容
            .setOnEndListener { textView ->
                //默认关闭
                mViewModel.usercouponReleaseCoupon(currentCoupon)
            }
            .build()
            .showDialog()
    }

    /**
     * 弹出开户弹窗，跳转开户页面
     */
    private fun startOpenAccountActivity(accountTypeObj: MT4AccountTypeObj) {
        CenterActionDialog.Builder(requireActivity())
            // 开通真实账户后才可使用优惠券
            .setContent(getString(R.string.your_coupons_will_opened)) //设置内容
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.open_live_account))
            .setOnSingleButtonListener { textView ->
                if (accountTypeObj.applyTpe == 0) {
                    // 账户正在审核
                    ToastUtil.showToast(getString(R.string.your_account_application_processed))
                } else {
                    VAUStartUtil.openAccountGuide(requireContext(), accountTypeObj)
                }
            }
            .build()
            .showDialog()
    }

    /**
     *
     */
    private fun startCouponNextActivity(currentCoupon: DepositCouponDetail?, bundle: Bundle) {
        // 满加满减
        if ("1" == currentCoupon?.couponType || "6" == currentCoupon?.couponType || "10" == currentCoupon?.couponType) {
            NewHtmlActivity.openActivity(context, url = UrlConstants.HTML_FUND_DEPOSIT)
        } else if ("5" == currentCoupon?.couponType) { // 抹亏券
            val intent = Intent(context, LossOrderActivity::class.java)
            startActivityForResult(intent.putExtras(bundle), 5)
        } else if ("9" == currentCoupon?.couponType || "11" == currentCoupon?.couponType || "12" == currentCoupon?.couponType) {
            // 9.抹亏保险券，10.入金返佣券，11.抹手续费券，12.盈利翻倍券
            var strTitle = ""
            var strContent = ""
            when (currentCoupon.couponType) {
                "9" -> {
                    strTitle = getString(R.string.loss_protection)
                    strContent = getString(R.string.coupon_msg_loss_protection)
                }

                "11" -> {
                    strTitle = getString(R.string.commission_fee)
                    strContent = getString(R.string.coupon_msg_commission_fee)
                }

                "12" -> {
                    strTitle = getString(R.string.profit_booster)
                    strContent = getString(R.string.coupon_msg_profit_booster)
                }
            }
            CenterActionDialog.Builder(requireActivity())
                .setTitle(strTitle) //设置则展示标题，否则不展示
                .setContent(strContent) //设置内容
                .setStartText(getString(R.string.cancel))//设置左侧按钮文本
                .setEndText(getString(R.string.confirm))//设置右侧按钮文本
                //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                .setOnEndListener { textView ->
                    //默认关闭
                    mViewModel.usercouponActivateCoupon(currentCoupon)
                }
                .build()
                .showDialog()
        } else {
            openActivity(CouponDetailActivity::class.java, bundle)
        }
    }

    private fun showActivateDialog(couponType: String?, data: ApiResponse<*>) {
        var title = getString(R.string.activation_successful) + "\n"
        var msg = ""
        var btnString = getString(R.string.confirm)
        if (data.isSuccess()) {
            btnString = getString(R.string.close)
            when (couponType) {
                "9" -> {
                    title += getString(R.string.loss_protection)
                    msg = getString(R.string.coupon_success_msg_loss_protection)
                }

                "11" -> {
                    title += getString(R.string.commission_fee)
                    msg = getString(R.string.coupon_success_msg_commission_fee)
                }

                "12" -> {
                    title += getString(R.string.profit_booster)
                    msg = getString(R.string.coupon_success_msg_profit_booster)
                }
            }
        } else {
            title = getString(R.string.activation_failed)
            msg = data.getResponseMsg()
        }
        CenterActionDialog.Builder(requireActivity())
            .setTitle(title) //设置则展示标题，否则不展示
            .setContent(msg) //设置内容
            .setSingleButton(true) //展示一个按钮，默认两个按钮
            .setSingleButtonText(btnString) //设置单个按钮文本
            //如果展示一个按钮，点击监听使用setOnSingleButtonListener
            .setOnSingleButtonListener { textView ->
                //默认关闭
                mViewModel.getCouponList()
            }
            .build()
            .showDialog()
    }

    override fun onResume() {
        super.onResume()
        mViewModel.sensorsTrack(couponAdapter.data.isNotEmpty())
    }

    companion object {
        /**
         * 可使用
         */
        const val TYPE_ACTIVE = 0

        /**
         * 已使用
         */
        const val TYPE_USED = 1

        /**
         * 已过期
         */
        const val TYPE_EXPIRED = 2

        private const val KEY_TYPE = "type"

        fun newInstance(type: Int): CouponFragment {
            val args = Bundle()
            args.putInt(KEY_TYPE, type)
            val fragment = CouponFragment()
            fragment.arguments = args
            return fragment
        }
    }
}