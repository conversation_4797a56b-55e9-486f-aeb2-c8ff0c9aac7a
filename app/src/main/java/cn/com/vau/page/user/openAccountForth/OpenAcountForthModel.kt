package cn.com.vau.page.user.register


/**
 * Created by Haipeng on 2017/10/12.
 * 1
 *
 */
class OpenAcountForthModel
//    : OpenAcountForthContract.Model {
//    override fun getRealInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable {
//        HttpUtils.loadData(RetrofitHelper.getHttpService().getRealInfo(map), baseObserver)
//        return baseObserver.disposable
//    }
//
//    override fun saveRealInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable {
//        HttpUtils.loadData(RetrofitHelper.getHttpService().saveRealInfo(map), baseObserver)
//        return baseObserver.disposable
//    }
//}
