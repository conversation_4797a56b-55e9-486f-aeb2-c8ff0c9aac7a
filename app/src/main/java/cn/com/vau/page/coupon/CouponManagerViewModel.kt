package cn.com.vau.page.coupon

import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 02 14:53
 * @updateUser:
 * @updateDate: 2025 4月 02 14:53
 */
class CouponManagerViewModel : BaseViewModel() {

    var accountTypeObj: MT4AccountTypeObj? = null

    /**
     * 获取优惠券列表，查看是否有过期的优惠券
     */
    fun inviteCoupon() {
        requestNet({
            baseService.inviteCoupon(UserDataUtil.userId())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }

            val dataList = it.data?.obj

            if (dataList.isNullOrEmpty()) return@requestNet

            val dataObj = dataList.firstOrNull { data -> data.type == 1 } ?: dataList.firstOrNull { data -> data.type == 0 } ?: return@requestNet
            sendEvent(DataEvent(EVENT_INVITE_COUPON, dataObj))
        })
    }

    /**
     * 在过期优惠券的弹窗里 点击确定 告诉后台
     */
    fun inviteCouponAgree(type: Int) {
        requestNet({
            baseService.inviteCouponAgree(hashMapOf("userId" to UserDataUtil.userId(), "type" to type))
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
            }
        })
    }

    /**
     * 兑换优惠券
     */
    fun usercouponExchange(exchangeCode: CharSequence) {
        requestNet({
            baseService.usercouponExchange(hashMapOf("userToken" to UserDataUtil.loginToken(), "mt4AccountId" to UserDataUtil.accountCd(), "currency" to UserDataUtil.currencyType(), "exchangeCode" to exchangeCode))
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                sensorsTrackIsSuccess(false,it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_USERCOUPON_EXCHANGE_SUCCESS))
            sensorsTrackIsSuccess(true)
        }, isShowDialog = true)
    }

    /**
     * 获取申请开通mt4账户号类型 , 检查 是否可用优惠券
     */
    fun queryStAccountType(coupon: DepositCouponDetail? = null, isShowToast: Boolean = false, isShowDialog: Boolean = false) {
        requestNet({ baseService.crmGetMt4AccountApplyTypeApi(UserDataUtil.loginToken()) }, {
            if (!it.isSuccess()) {
                if (isShowToast)
                    ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }

            accountTypeObj = it.data?.obj
            coupon?.let {
                sendEvent(DataEvent(EVENT_GET_ACCOUNTTYPEDATA_SUCCESS, data = coupon))
            }
        }, isShowDialog = isShowDialog)
    }

    /**
     * 神策自定义埋点(v3710)
     * 优惠券夹页_Redeem点击
     */
    fun sensorsTrack() {
        val properties = JSONObject()
        properties.put("redeem_entry_page", "CouponMainPage")
        SensorsDataUtil.track(SensorsConstant.V3710.COUPONPAGEREDEEMBUTTON_CLICK, properties)
    }

    /**
     * 神策自定义埋点(v3710)
     * 优惠券夹页_Redeem点击
     */
    fun sensorsTrackIsSuccess(isSuccess: Boolean, failReason: String = "") {
        val properties = JSONObject()
        properties.put("redeem_entry_page", "CouponMainPage")
        properties.put("fail_reason", failReason)
        properties.put("is_success", if (isSuccess) 1 else 0)
        SensorsDataUtil.track(SensorsConstant.V3710.COUPONPAGEREDEEMBUTTON_RESULT, properties)
    }

    companion object {
        const val EVENT_INVITE_COUPON = "EVENT_INVITE_COUPON"

        const val EVENT_RESUME_REFRESH = "EVENT_RESUME_REFRESH"
        const val EVENT_USERCOUPON_EXCHANGE_SUCCESS = "EVENT_USERCOUPON_EXCHANGE_SUCCESS"

        const val EVENT_GET_ACCOUNTTYPEDATA_SUCCESS = "EVENT_GET_ACCOUNTTYPEDATA_SUCCESS"
    }
}