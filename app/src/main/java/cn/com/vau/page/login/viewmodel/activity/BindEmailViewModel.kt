package cn.com.vau.page.login.viewmodel.activity

import androidx.lifecycle.MutableLiveData
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.FirebaseManager
import org.greenrobot.eventbus.EventBus

/**
 * author：lvy
 * date：2025/03/18
 * desc：绑定邮箱，包括三方登录方式绑定
 */
class BindEmailViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null // 只有注册页面跳转过来的才会携带这个参数，其他页面都是携带的email

    val isBind2Fa = MutableLiveData<Boolean>() // 是否绑定了2fa
    val bindEmailSuccessLiveData = MutableLiveData<Boolean>() // 绑定邮箱成功

    /**
     * 注册时邮箱已存在，进行绑定邮箱
     */
    fun bindEmailApi(email: String?, pwd: String?) {
        if (!RegexUtil.isEmail(email)) {
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_correct_mail))
            return
        }

        if (signUpRequestBean != null) { // 不为空，证明是注册页面跳转过来的，需要调用注册绑定邮箱接口
            val map = hashMapOf<String, Any?>()
            map["phoneNum"] = signUpRequestBean?.mobile
            map["phoneCode"] = signUpRequestBean?.countryNum
            map["countryCode"] = signUpRequestBean?.countryCode
            map["password"] = pwd
            map["email"] = email
            val aesMap = hashMapOf<String, Any?>()
            aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
            requestNet({ baseService.emailBindingPhoneApi(aesMap) }, {
                if ("V10017" != it.getResponseCode() && "V10016" != it.getResponseCode()) {
                    ToastUtil.showToast(it.getResponseMsg())
                    return@requestNet
                }
                // 保存注册成功信息
                saveDataSignUp(it.getResponseCode(), it.getResponseData()?.obj, pwd)
                // 判断是否绑定了2fa
                val isBind2FA = it.data?.obj?.twoFactorUser == true
                val userid = it.data?.obj?.userId.ifNull()
                val isBind = SpManager.getUser2faBindEd(userid, false)
                isBind2Fa.value = isBind2FA || isBind
            }, isShowDialog = true)
        } else {
            val map = hashMapOf<String, Any?>()
            map["userId"] = UserDataUtil.userId()
            map["password"] = pwd
            map["email"] = email
            val aesMap = hashMapOf<String, Any?>()
            aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
            requestNet({ baseService.bindEmailUserApi(aesMap) }, {
                if (!it.isSuccess()) {
                    ToastUtil.showToast(it.getResponseMsg())
                    return@requestNet
                }
                saveDataBindEmailSuccess(it.getResponseData()?.obj)
                bindEmailSuccessLiveData.value = true
            }, isShowDialog = true)
        }
    }

    /**
     * 三方登录触发注册时绑定邮箱
     *
     * PS：只有注册页面 SignUpAsicPwdActivity 跳转过来的才存在三方登录情况，才会请求此接口
     */
    fun thirdPartyBindApi(email: String?, pwd: String?) {
        if (!RegexUtil.isEmail(email)) {
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_correct_mail))
            return
        }

        val map = hashMapOf<String, Any?>()
        map["userEmail"] = email
        map["userPassword"] = pwd
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({ baseService.thirdpartyBindApi(aesMap) }, {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            bindEmailApi(email, pwd)
        }, isShowDialog = true, isAutoDismissDialog = false)
    }

    /**
     * 保存注册成功信息
     */
    private fun saveDataSignUp(resCode: String, obj: LoginObjBean?, pwd: String?) {
        // 用户信息缓存
        UserDataUtil.setUserTel(obj?.userTel)
        UserDataUtil.setCountryCode(obj?.countryCode)
        UserDataUtil.setAreaCode(obj?.code)
        UserDataUtil.setUserId(obj?.userId)
        UserDataUtil.setUserType(if (resCode == "V10017") 1 else 0)
        UserDataUtil.setLoginToken(obj?.token)
        UserDataUtil.setXToken(obj?.xtoken)
        UserDataUtil.setFastCloseState(obj?.fastCloseState.ifNull("2"))
        UserDataUtil.setFastStopCopyState(obj?.fastCloseCopyOrder.ifNull("2")) // 快速停止跟单
        UserDataUtil.setOrderConfirmState(obj?.orderConfirmation.ifNull("2"))
        UserDataUtil.setEmail(obj?.email)
        UserDataUtil.setUserNickName(obj?.userNick)
        UserDataUtil.setUserPic(obj?.pic)
        UserDataUtil.setUserPassword(pwd)
        UserDataUtil.setAccountDealType("3")

        // 数据库保存邮箱
        val userEmailHistory = UserEmailHistory()
        userEmailHistory.email = obj?.email
        DbManager.getInstance().saveUserEmailHistory(userEmailHistory)

        // sp缓存
        SpManager.putSuperviseNum(obj?.regulator.ifNull("0"))
        SpManager.putUserTel(obj?.userTel.ifNull())
        SpManager.putCountryCode(obj?.countryCode.ifNull())
        SpManager.putCountryNum(obj?.code.ifNull())

        // 埋点相关
        LogEventUtil.mFirebaseAnalytics.setUserId(obj?.code + obj?.userTel)
        // 绑定神策业务ID，场景6
        val crmUserId = obj?.crmUserId
        if (!crmUserId.isNullOrBlank()) { // crmUserId，神策使用
            SpManager.putCrmUserId(crmUserId)
        }
        SensorsDataUtil.bindBusinessIdForMerge(obj?.emailEventID)

        // 登录firebase
        FirebaseManager.userLogin()

        // 广播
        EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
        EventBus.getDefault().post(NoticeConstants.SUBSCRIBE_TOPIC)
    }

    /**
     * 保存绑定邮箱成功信息
     */
    private fun saveDataBindEmailSuccess(obj: BindEmailObj?){
        // 用户信息缓存
        UserDataUtil.setUserType(if (obj?.userType == "1") 1 else 0)
        UserDataUtil.setUserId(obj?.reserveUserId)
        UserDataUtil.setLoginToken(obj?.reserveUserToken)
        UserDataUtil.setXToken(obj?.xtoken)

        // 绑定神策业务ID，场景6
        val crmUserId = obj?.crmUserId
        if (!crmUserId.isNullOrBlank()) { // crmUserId，神策使用
            SpManager.putCrmUserId(crmUserId)
        }
        SensorsDataUtil.bindBusinessIdForMerge(obj?.emailEventID)

        // 登录firebase
        FirebaseManager.userLogin()
    }
}