package cn.com.vau.page.user.leverage

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.databinding.ActivityLeverageBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.leverage.viewmodel.LeverageViewModel
import cn.com.vau.profile.adapter.*
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionDialog

/**
 * 修改杠杆
 */
class LeverageActivity : BaseMvvmActivity<ActivityLeverageBinding, LeverageViewModel>() {


    private val tradeTypePopup: BottomSelectPopup? by lazy {
        val typeAdapter: SelectAccountAdapter<SelectBean> by lazy { SelectAccountAdapter(isChangeSelectTextColor = false) }
        typeAdapter.setNewInstance(mViewModel.leverageList)
        typeAdapter.selectTitle = typeAdapter.data.getOrNull(mViewModel.selectIndex)?.getShowItemValue()
        typeAdapter.setOnItemClickListener { _, _, position ->

            if (mViewModel.selectIndex == position) return@setOnItemClickListener
            mViewModel.selectIndex = position
            mBinding.tvLeverageChange.text = mViewModel.leverageList.elementAtOrNull(position)?.getShowItemValue() ?: ""
            val selectLeverage = mBinding.tvLeverageChange.text.toString().trim().split(":").elementAtOrNull(0) ?: ""

            if (selectLeverage.mathCompTo("1000") != -1) {
                mBinding.textView6.visibility = View.VISIBLE
                initTextView6Txt(selectLeverage)
            } else {
                mBinding.textView6.visibility = View.GONE
            }
            typeAdapter.selectTitle = typeAdapter.data.getOrNull(position)?.getShowItemValue()
            typeAdapter.notifyDataSetChanged()

            tradeTypePopup?.dismiss()
        }
        BottomSelectPopup.build(this, getString(R.string.leverage_change), typeAdapter)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.accountCd = intent?.extras?.getString("accountCd")
        mViewModel.accountServer = intent?.extras?.getString("accountServer")
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        mBinding.textView8.text =
            getString(R.string.by_requesting_a_have_deposited) + "\n" + "\n" +
                    getString(R.string.i_agree_that_or_notifications) + "\n" + "\n" +
                    getString(R.string.i_understand_that_significant_lossess)

    }

    override fun initData() {
        super.initData()
        mViewModel.getLeverageApi()
    }

    override fun createObserver() {
        mViewModel.oldLeverageLiveData.observe(this) {
            showOldLeverage(it)
        }
        mViewModel.queryProClientLiveData.observe(this) {
            if (it) {
                showUpgradeDialog()
            } else {
                saveCurrentLeverage()
            }
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }
        mBinding.ctlLeverageChange.setOnClickListener(this)
        mBinding.tvConfirm.setOnClickListener(this)
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.ctlLeverageChange -> {
                selectLeverage()
            }
            R.id.tvConfirm -> {
                val selectLeverage = (mBinding.tvLeverageChange.text.toString().trim().split(":").elementAtOrNull(0) ?: "0").toIntCatching()
                val supervisionType = SpManager.getSuperviseNum("")
                if (supervisionType == "1" && selectLeverage > 30) {
                    mViewModel.queryProClientApi()
                } else {
                    saveCurrentLeverage()
                }
            }
        }
    }

    private fun saveCurrentLeverage() {
        val selectLeverage = mBinding.tvLeverageChange.text.toString().trim().split(":").elementAtOrNull(0) ?: ""
        mViewModel.saveCurrentLeverageApi(selectLeverage, mBinding.cbAgreement.isChecked)
    }

    private fun showUpgradeDialog() {
        CenterActionDialog.Builder(this)
            .setContent(getString(R.string.upgrade_to_pro_content)) //设置内容
            .setSingleButton(true) //展示一个按钮，默认两个按钮
            .setSingleButtonText(getString(R.string.upgrade_to_pro)) //设置单个按钮文本
            //如果展示一个按钮，点击监听使用setOnSingleButtonListener
            .setOnSingleButtonListener { textView ->
                //默认关闭
                val htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/nativeTitle/pro/index"
                NewHtmlActivity.openActivity(this, htmlUrl)
            }
            .build()
            .showDialog()
    }

    private fun selectLeverage() {
        if (mViewModel.leverageList.isEmpty()) {
            // 暂无可申请的杠杆
            ToastUtil.showToast(getString(R.string.the_leverage_selected_unavailable))
            return
        }
        tradeTypePopup?.show()
    }

    /**
     * 展示老的杠杆比例
     */
    private fun showOldLeverage(currentLeverage: String) {
        mBinding.tvCurrencyLeverage.text = currentLeverage
        mBinding.tvLeverageChange.text = mViewModel.leverageList.lastOrNull()?.getShowItemValue() ?: "100:1"
        if ("1000:1" == mBinding.tvLeverageChange.text.toString() || "2000:1" == mBinding.tvLeverageChange.text.toString()) {
            mBinding.textView6.visibility = View.VISIBLE
            initTextView6Txt(mBinding.tvLeverageChange.text.split(":").elementAtOrNull(0) ?: "")
        }
    }

    // 3. 如果你的账户净值超过%2s美元或同等价值，账户杠杆将被降至%1s：1。
    // 3. 귀하의 계좌 자산이 %2s USD 또는 이에 상응하는 금액을 초과하는 경우 계좌 레버리지는 %1s:1로 감소됩니다.<
    @SuppressLint("SetTextI18n")
    private fun initTextView6Txt(selectLeverage: String) {
        mBinding.textView6.text =
            "${
                getString(R.string.leverage_explain_title, selectLeverage)
            }\n\n" +
                    "1. ${
                        getString(
                            R.string.leverage_explain_item_2,
                            if ("1000" == selectLeverage) "40000" else "5000"
                        )
                    }\n\n" +
                    "2. ${
                        getString(
                            R.string.leverage_explain_item_3,
                            if ("1000" == selectLeverage) "500" else "1000",
                            if ("1000" == selectLeverage) "45000" else "10000"
                        )
                    }\n\n" +
                    "3. ${getString(R.string.leverage_explain_item_4)}\n\n" +
                    "4. ${getString(R.string.leverage_explain_item_5)}"
    }

}
