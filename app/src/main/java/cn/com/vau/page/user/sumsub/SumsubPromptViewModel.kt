package cn.com.vau.page.user.sumsub

import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import kotlinx.coroutines.runBlocking

/**
 * Filename: SumsubPromptViewModel
 * Author: GG
 * Date: 2024/8/27
 * Description:
 */
class SumsubPromptViewModel : BaseViewModel() {

    var accessToken: String? = ""

    var type: String? = "" // 1-活动；2-live开户Lv2的token
    var campaignId: String? = ""

    /**
     * 获取SumSub SDK Token
     */
    fun getKycSumsubAccessToken(isClick: Boolean = false) {
        requestNet({
            kycSumsubAccessToken()
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            accessToken = it.data?.obj?.accessToken.ifNull()
            if (isClick) {
                sendEvent(Unit)
            }
        }, isShowDialog = true)
    }

    /**
     * 同步方法获取SumSub SDK Token
     */
    fun getNewToken(): String =
        try {
            runBlocking {
                val data = kycSumsubAccessToken()
                if (!data.isSuccess()) {
                    ToastUtil.showToast(data.getResponseMsg())
                    return@runBlocking ""
                }
                return@runBlocking data.data?.obj?.accessToken ?: ""
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }

    /**
     * 实际网络请求方法，因为多个地方调用所以抽出来
     */
    private suspend fun kycSumsubAccessToken() =
        baseService
            .kycSumsubAccessToken(
                hashMapOf(
                    "userToken" to UserDataUtil.loginToken(),
                    "type" to type.ifNull(),
                    "campaignId" to campaignId.ifNull(),
                )
            )
}