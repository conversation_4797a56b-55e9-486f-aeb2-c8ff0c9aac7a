package cn.com.vau.page.notice.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.msg.NoticeBean
import cn.com.vau.util.ToastUtil

/**
 * @description:
 * @author: GG
 * @createDate: 2024 12月 17 13:42
 * @updateUser:
 * @updateDate: 2024 12月 17 13:42
 */
class NoticeItemViewModel : BaseViewModel() {

    private var page: Int = 1

    var type: String = ""

    val uiListLiveData: MutableLiveData<ListUIState<List<NoticeBean>?>> by lazy { MutableLiveData() }

    fun refresh() {
        page = 1
        msgInAppListApi()
    }

    fun loadMore() {
        page++
        msgInAppListApi()
    }

    /**
     * 请求消息列表
     */
    private fun msgInAppListApi() {
        requestNet({
            baseService.msgInAppListApi(token = UserDataUtil.loginToken(), type = type, page = page, size = 10)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val objList = it.data?.obj as? ArrayList<NoticeBean>
            if (objList.isNullOrEmpty() && page == 1) {
                uiListLiveData.value = ListUIState.Empty
                return@requestNet
            }
            if (page == 1) {
                uiListLiveData.value = ListUIState.RefreshSuccess(objList)
            } else {
                if (objList.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.LoadEnd(objList)
                } else {
                    uiListLiveData.value = ListUIState.LoadMoreSuccess(objList)
                }
            }
        }, onError = {
            uiListLiveData.value = ListUIState.Error()
        })
    }

    /**
     * 一条消息已读
     */
    fun msgInAppReadApi(id: String?) {
        requestNet({
            baseService.msgInAppReadApi(token = UserDataUtil.loginToken(), id = id)
        }, { })
    }

}