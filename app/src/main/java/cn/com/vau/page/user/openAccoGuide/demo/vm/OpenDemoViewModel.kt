package cn.com.vau.page.user.openAccoGuide.demo.vm

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.data.account.PlatformTypeObj
import cn.com.vau.util.*

class OpenDemoViewModel : BaseViewModel() {

    var tempData: PlatFormAccountData.OpenAccountData? = null
    val openAccountData: MutableLiveData<PlatFormAccountData.OpenAccountData> by lazy { MutableLiveData(PlatFormAccountData.OpenAccountData()) }
    var platDataList = mutableListOf<PlatFormAccountData.Obj>()
    var typeDataList = mutableListOf<PlatFormAccountData.PlatFormAccountType>()
    var currencyDataList = mutableListOf<PlatFormAccountData.Currency>()

    var platTitleLiveData = MutableLiveData<PlatformTypeObj?>()
    var platTypeCurrencyLiveData = MutableLiveData<PlatFormAccountData.Data?>()

    // 新增/重置成功
    val resetSuccessLiveData = MutableLiveData<Boolean>()

    var isReset = 0 // 0=新增；1=重置
    var accountCd: String? = null // 重置时需要更新缓存里的 accountCd

    /**
     * 交易平台列表
     */
    fun getPlatFormAccountTypeCurrencyApi() {
        val map = hashMapOf<String, Any?>()
        map["userId"] = UserDataUtil.userId()
        map["type"] = "1"
        requestNet({ baseService.getPlatFormAccountTypeCurrencyApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            platTypeCurrencyLiveData.value = it.data
        }, isShowDialog = true)
    }

    /**
     * 交易平台卡片上面的文案
     */
    fun getAccountTypeTitleApi() {
        requestNet({ baseService.getAccountTypeTitleApi() }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            platTitleLiveData.value = it.data?.obj
        })
    }

    /**
     * 新增/重置 vts demo
     */
    fun accountDemoEditAccountApi() {
        val map = hashMapOf<String, Any?>()
        map["token"] = UserDataUtil.loginToken()
        map["tradingPlatform"] = tempData?.platform.ifNull()
        map["accountType"] = tempData?.accountType.ifNull()
        map["currency"] = tempData?.currency.ifNull()
        map["isReset"] = isReset // 0=新增；1=重置
        requestNet({ baseService.accountDemoEditAccountApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                hideLoading()
                return@requestNet
            }
            if (isReset == 1) {
                if (UserDataUtil.isLogin() && UserDataUtil.accountCd() == accountCd) {
                    UserDataUtil.setAccountCd(it.data?.obj?.accountId) // demo账户每次点击都调接口，所以这里赋值的作用不大，如果后期有需求再做处理
                    UserDataUtil.setMt4PWD(it.data?.obj?.password) // mt4PWD这个值应该没用，只在重置时赋值了
                }
            }
            resetSuccessLiveData.value = true
        }, isShowDialog = true)
    }
}