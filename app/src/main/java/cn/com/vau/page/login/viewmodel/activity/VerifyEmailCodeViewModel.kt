package cn.com.vau.page.login.viewmodel.activity

import androidx.lifecycle.*
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2025/04/02
 * desc：验证邮箱验证码
 */
class VerifyEmailCodeViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null

    // kyc认证、绑定、修改邮箱成功
    private val _updateUserEmailSuccessLiveData = MutableLiveData<Boolean>()
    val updateUserEmailSuccessLiveData: LiveData<Boolean> = _updateUserEmailSuccessLiveData

    /**
     * kyc绑定、认证、修改邮箱
     */
    fun updateUserEmailApi(type: String) {
        val map = hashMapOf<String, Any?>()
        map["email"] = signUpRequestBean?.email
        map["txId"] = signUpRequestBean?.txId
        map["password"] = AESUtil.encryptAES(signUpRequestBean?.pwd, AESUtil.PWD_AES_KEY)
        map["code"] = signUpRequestBean?.smsCode
        map["type"] = type
        if (type == "28") { // 修改邮箱验证新邮箱
            map["originalEmailTxId"] = signUpRequestBean?.oldTxId // 原邮箱发送验证码的txId
            map["originalEmailCode"] = signUpRequestBean?.oldEmailCode // 原邮箱发送的验证码code
            map["mfaCode"] = signUpRequestBean?.tfaCode // 多因子验证code
        }
        requestNet({ baseService.updateUserEmailApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val emailEventID = it.getResponseData()?.emailEventID
            if (!emailEventID.isNullOrBlank()) {
                SpManager.putSensorsDataEmailEventId(emailEventID)
            }
            if (type == "28") { // 只有修改邮箱才吐司后台的文案
                ToastUtil.showToast(it.getResponseMsg())
            }
            _updateUserEmailSuccessLiveData.value = true
        }, {
            LogUtil.e(it.message)
        }, isShowDialog = true)
    }
}