package cn.com.vau.page.login.viewmodel.activity

import androidx.lifecycle.*
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.util.ToastUtil

/**
 * author：lvy
 * date：2025/04/02
 * desc：验证sms验证码
 */
class VerifySmsCodeViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null

    // kyc认证、绑定、修改手机号成功
    private val _updateUserPhoneSuccessLiveData = MutableLiveData<Boolean>()
    val updateUserPhoneSuccessLiveData: LiveData<Boolean> = _updateUserPhoneSuccessLiveData

    /**
     * kyc认证、绑定、修改手机号
     */
    fun updateUserPhoneApi(type: String) {
        val map = hashMapOf<String, Any?>()
        map["phoneCountryCode"] = signUpRequestBean?.countryCode
        map["code"] = signUpRequestBean?.countryNum
        map["phoneNum"] = signUpRequestBean?.mobile
        map["type"] = type
        map["smsSendType"] = signUpRequestBean?.sendCodeType?.value
        map["validateCode"] = signUpRequestBean?.smsCode
        requestNet({ baseService.updatePhoneApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            _updateUserPhoneSuccessLiveData.value = true
        }, isShowDialog = true)
    }
}