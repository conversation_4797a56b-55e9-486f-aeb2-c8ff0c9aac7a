package cn.com.vau.page.coupon

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.page.coupon.CouponFragment.Companion.TYPE_ACTIVE
import cn.com.vau.page.coupon.CouponFragment.Companion.TYPE_EXPIRED
import cn.com.vau.page.coupon.CouponFragment.Companion.TYPE_USED
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 02 14:53
 * @updateUser:
 * @updateDate: 2025 4月 02 14:53
 */
class CouponViewModel : BaseViewModel() {

    var type: Int = 0

    var isFirst = true

    val uiListLiveData: MutableLiveData<ListUIState<List<DepositCouponDetail>?>> by lazy { MutableLiveData() }

    fun getCouponList() {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        paramMap["mt4AccountId"] = UserDataUtil.accountCd()
        paramMap["currency"] = UserDataUtil.currencyType()
        when (type) {
            // 已使用
            CouponFragment.TYPE_USED -> {
                paramMap["queryType"] = type
                usercouponUsedOrOut(paramMap)
            }
            // 已过期
            CouponFragment.TYPE_EXPIRED -> {
                paramMap["queryType"] = type
                usercouponUsedOrOut(paramMap)
            }
            // 可用
            else -> {
                paramMap["queryType"] = ""
                usercouponList(paramMap)
            }
        }
    }

    /**
     * 获取已使用已过期优惠券
     */
    fun usercouponUsedOrOut(paramMap: HashMap<String, Any>) {
        requestNet({
            paramMap.put("start", "0")
            baseService.usercouponUsedOrOut(paramMap)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                uiListLiveData.value = ListUIState.Error(it.getResponseMsg())
                return@requestNet
            }
            uiListLiveData.value = if (it.data?.obj.isNullOrEmpty()) {
                ListUIState.Empty
            } else {
                ListUIState.RefreshSuccess(it.data.obj)
            }
        })
    }

    /**
     * 获取可用的优惠券列表
     */
    fun usercouponList(paramMap: HashMap<String, Any>) {
        requestNet({
            // 支付类型，用于过滤限制某种支付类型的券
            paramMap.put("payType", "")
            paramMap.put("couponStatus", "1")
            paramMap.put("currency", UserDataUtil.currencyType())
            baseService.usercouponList(paramMap)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                uiListLiveData.value = ListUIState.Error(it.getResponseMsg())
                return@requestNet
            }
            uiListLiveData.value = if (it.data?.obj.isNullOrEmpty()) {
                ListUIState.Empty
            } else {
                ListUIState.RefreshSuccess(it.data.obj)
            }
        })
    }

    /**
     * 释放优惠券
     */
    fun usercouponReleaseCoupon(couponDataBean: DepositCouponDetail) {
        requestNet({
            val paramMap = hashMapOf<String, Any>()
            paramMap["userToken"] = UserDataUtil.loginToken()
            paramMap["couponId"] = couponDataBean.couponId ?: ""
            paramMap["userCouponId"] = couponDataBean.userCouponId ?: ""
            paramMap["couponType"] = couponDataBean.couponType ?: ""
            baseService.usercouponReleaseCoupon(paramMap)
        }, {
            ToastUtil.showToast(it.getResponseMsg())

            getCouponList()
        })
    }

    /**
     * 优惠券激活
     */
    fun usercouponActivateCoupon(currentCoupon: DepositCouponDetail) {
        requestNet({
            val paramMap = hashMapOf<String, Any>()
            paramMap["userToken"] = UserDataUtil.loginToken()
            paramMap["couponId"] = currentCoupon.couponId ?: ""
            paramMap["userCouponId"] = currentCoupon.userCouponId ?: ""
            paramMap["couponSource"] = currentCoupon.couponSource ?: ""
            baseService.usercouponActivateCoupon(paramMap)
        }, {
            sendEvent(DataEvent(EVENT_COUPON_ACTIVATE, Pair(currentCoupon.couponType, it)))
        }, isShowDialog = true)
    }

    /**
     * 神策自定义埋点(v3710)
     * 优惠券夹页_主页面浏览
     */
    fun sensorsTrack(isCouponAvailable: Boolean) {
        if (isFirst) return
        val properties = JSONObject()
        properties.put(
            SensorsConstant.Key.TAB_NAME, when (type) {
                TYPE_ACTIVE -> "Active"
                TYPE_USED -> "Used"
                TYPE_EXPIRED -> "Expired"
                else -> ""
            }
        ) // 所属Tab 名称
        properties.put("is_coupon_available", if (isCouponAvailable && type == TYPE_ACTIVE) 1 else 0)
        SensorsDataUtil.track(SensorsConstant.V3710.COUPONMAINPAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3710)
     * 优惠券夹页_按钮点击
     */
    fun sensorsButtonTrack(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        properties.put("redeem_entry_page", "CouponMainPage")
        SensorsDataUtil.track(SensorsConstant.V3710.COUPONPAGEBUTTON_CLICK, properties)
    }

    companion object {
        const val EVENT_COUPON_ACTIVATE = "EVENT_COUPON_ACTIVATE"
    }
}