package cn.com.vau.page.customerservice.viewmodel

import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.msg.CSConsultObj
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import kotlin.collections.hashMapOf

class HelpCenterViewModel: BaseViewModel() {

    var channelKey: String? = ""

    var data: CSConsultObj? = null

    fun initDepartment() {
        requestNet({
            baseService.consultItems(hashMapOf("userToken" to UserDataUtil.loginToken()))
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            data = it.data?.obj
            sendEvent(DataEvent(Constants.EVENT_TAG_DEPARTMENT))
        })
    }

    fun getChannelKey(isAutoIntoCS: Boolean) {
        channelKey = ""
        requestNet({
            baseService.customerServiceGetCustomerServiceDetail()
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            channelKey = it.data?.obj?.keyDetail.ifNull()
            sendEvent(DataEvent(Constants.EVENT_TAG_CHANNELKEY, isAutoIntoCS))
        })
    }
}