package cn.com.vau.page.user.asicOpenAccount.viewmodel

import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.MoreAboutYouDetail
import cn.com.vau.data.account.RealAccountCacheObj
import cn.com.vau.data.account.SelectNationalityObjDetail
import cn.com.vau.page.ResidenceEvent
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

class AsicOpenViewModel: BaseViewModel() {

    var isFrom = -1
    var cacheData: RealAccountCacheObj? = null

    var accountTitle: MutableList<MoreAboutYouDetail>? = mutableListOf()
    var selectAccountTitle: MoreAboutYouDetail? = null
    var accountIdType: MutableList<MoreAboutYouDetail>? = null
    var selectAccountIdType: MoreAboutYouDetail? = null
    var selectNationData = SelectNationalityObjDetail()
    var selectPlaceOfBirth = ResidenceEvent()  //已选择的地区

    var selectResidenceEvent = ResidenceEvent()

    fun getAccountSelect() {
        requestNet({
            baseService.getAccountSelectApi(hashMapOf<String, String>())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 头衔，证件类型
            accountTitle = it.data?.obj?.cima?.listAccountTitle
            accountIdType = it.data?.obj?.cima?.listAccountIdType

            getStep1Data()
        }, isShowDialog = true)
    }

    fun emailIsExistApi(email: String) {
        requestNet({
            baseService.emailIsExistApi(hashMapOf<String, Any>(
                "token" to UserDataUtil.loginToken(),
                "email" to email
            ))
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val cacheData = it.data?.obj
            if (cacheData?.emailStatus == true) {
                sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIRST_EXIST_EMAIL, email))
            }
        }, isShowDialog = true)
    }

    fun getStep1Data() {
        requestNet({
            baseService.getDataApi(hashMapOf<String, Any>(
                "token" to UserDataUtil.loginToken(),
                "step" to "1-1"
            ))
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            cacheData = it.data?.obj

            SpManager.putSuperviseNum(cacheData?.supervisionType ?: "0")

            selectNationData.id = cacheData?.nationalityId ?: ""
            selectNationData.nationality = cacheData?.nationalityName ?: ""

            selectPlaceOfBirth.countryId = cacheData?.placeOfBirth ?: ""
            selectPlaceOfBirth.countryEn = cacheData?.placeOfName

            selectAccountTitle = accountTitle?.find {
                it.valueName == cacheData?.title || it.displayName == cacheData?.title
            }
            selectAccountIdType = accountIdType?.find {
                it.id == cacheData?.idType
            }
            sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIRST_GET_DATA, cacheData))
        }, isShowDialog =true)
    }

    fun getStep12Data() {
        requestNet({
            baseService.getDataApi(hashMapOf<String, Any>(
                "token" to UserDataUtil.loginToken(),
                "step" to "1-2"
            ))
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            cacheData = it.data?.obj
            selectResidenceEvent.countryId = cacheData?.countryId
            selectResidenceEvent.provinceCode = cacheData?.state
            selectResidenceEvent.cityCode = cacheData?.suburb
            selectResidenceEvent.countryEn = cacheData?.countryName
            selectResidenceEvent.provinceEn = cacheData?.stateName
            selectResidenceEvent.cityEn = cacheData?.suburbName
            sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIRST_SECOND_GET_DATA, cacheData))
        }, isShowDialog =true)
    }

    fun getStep5Data() {
        requestNet({
            baseService.getDataApi(hashMapOf<String, Any>(
                "token" to UserDataUtil.loginToken(),
                "step" to "5"
            ))
        }, {
            if (it.getResponseCode() != "V00000" || "greenid" != it.data?.obj?.verifyMethod) {
                sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIFTH_ERROR))
                return@requestNet
            }
            sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIFTH_TO_H5))
        }, onError = {
            sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIFTH_ERROR))
        }, isShowDialog = true)
    }

    fun saveStep1Data(param: HashMap<String, Any>) {
        if (null == cacheData) {
            getStep1Data()
            return
        }
        requestNet({
            baseService.processApi(param)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 绑定神策业务ID，场景5
            SensorsDataUtil.bindBusinessIdForMerge(it.data?.obj?.emailEventID)
            // 接口返回成功
            sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIRST_SAVE_DATA))
        }, isShowDialog = true)
    }

    fun saveStep12Data(param: HashMap<String, Any>) {
        if (null == cacheData) {
            getStep1Data()
            return
        }
        requestNet({
            baseService.processApi(param)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            //绑定神策业务ID，场景5
            SensorsDataUtil.bindBusinessIdForMerge(it.data?.obj?.emailEventID)
            // 接口返回成功
            sendEvent(DataEvent(Constants.EVENT_TAG_ASIC_FIRST_SECOND_SAVE_DATA))
        }, isShowDialog = true)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    fun sensorsOpenIdentityPageView() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    fun sensorsTrackClick() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, "ASIC-Next") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }
}