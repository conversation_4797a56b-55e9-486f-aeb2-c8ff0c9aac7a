package cn.com.vau.page.setting.viewmodel

import android.text.TextUtils
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.livedata.UnPeekLiveData
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.mvvm.state.UIState
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.popup.bean.DrawingBean2
import cn.com.vau.common.view.popup.bean.MainBean
import cn.com.vau.common.view.timeSelection.PickerDateUtil.longTimeToString
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.trade.KChartData
import cn.com.vau.trade.bean.kchart.ChartPriceChange
import cn.com.vau.trade.bean.kchart.ChartTradeEmotion
import cn.com.vau.trade.bean.kchart.KLineSettingData
import cn.com.vau.trade.kchart.ChartUIParamUtil
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.util.AppUtil
import cn.com.vau.util.DealLogUtil.saveFailedDealLog
import cn.com.vau.util.DealLogUtil.saveStartDealLog
import cn.com.vau.util.DealLogUtil.saveSuccessDealLog
import cn.com.vau.util.GsonUtil.buildGson
import cn.com.vau.util.LogUtil
import cn.com.vau.util.TimeUtil.frontDateLongWithStartDate
import cn.com.vau.util.ToastUtil.showToast
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathMul
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.toFloatCatching
import cn.com.vau.util.toLongCatching
import com.github.tifezh.kchartlib.OrderRecordEntity
import com.github.tifezh.kchartlib.data.OrderData
import com.github.tifezh.kchartlib.helper.bean.KLineEntity
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnum
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnumUtils
import com.github.tifezh.kchartlib.helper.bean.KlineOtherEnum
import com.github.tifezh.kchartlib.helper.bean.KlineOtherEnumUtils
import com.google.gson.JsonObject
import com.upex.common.drawTools.DrawViewDataHelper
import com.upex.common.language.Keys
import com.upex.common.utils.DataUtils
import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import java.util.Calendar

class KLineChartNewViewModel : BaseViewModel(), DefaultLifecycleObserver{
    var symbol = ""           // K线产品名
    var isSwitching = false     // 切换新产品中 (避免因切换后到页面初始方法执行完毕前发生异常 | 防止K线页onResume重复刷新)

    // K线产品数据
    var data: ShareProductData? = null

    var intervalList = mutableListOf("Tick", "1m", "15m", "1h", "1D", "1W")
    var moreIntervalList = mutableListOf("5m", "30m", "4h", "1M")
    var landIntervalList = mutableListOf("Tick", "1m", "5m", "15m", "30m", "1h", "4h", "1D", "1W", "1M")
    var selectedInterval = "1D"
    var isTimeShare = false     // 是否是分时图
    var period: Int = 0         // K线请求的分钟数
    var isRequestEnd: Boolean = true
    var isRequestLoadMore: Boolean = false  // 是否正在请求加载更多
    var isEnableLoadMore: Boolean = true  // 是否可以继续加载更多
    var isLandRequestLoadMore: Boolean = false  // k线横屏 - 是否正在请求加载更多
    var isLandEnableLoadMore: Boolean = true  // k线横屏 - 是否可以继续加载更多
    private var fromTime: String? = null
    private var toTime: Long = 0    // K线请求的结束时间   start-to-end

    var noDataLiveData = UnPeekLiveData<Boolean>()
    var tradeEmotionUiState = MutableLiveData<UIState<ChartTradeEmotion>>()
    var priceChangeData: ChartPriceChange? = null
    var priceChangeDataMore = MutableLiveData<ChartPriceChange?>()
    var newListLiveData = UnPeekLiveData<List<KLineEntity>>()
    var addListLiveData = UnPeekLiveData<List<KLineEntity>>()
    var autoRefreshLiveData = UnPeekLiveData<List<KLineEntity>>()
    val tokenErrorLiveData by lazy { UnPeekLiveData<String>() }
    val resetLineLiveData by lazy { UnPeekLiveData<Boolean>() }
    var lastKLineEntity: KLineEntity? = null

    var klineScale = 1f
    var isShowDraw = true
    // 日志用参数
    private var startTimeMillisWs: Long = 0
    private var finalLogType: String? = null

    var mainChartNames = mutableListOf(
        KlineMainEnum.MA,
        KlineMainEnum.BOLL,
//        KlineMainEnum.EMA,
//        KlineMainEnum.MIKE,
        KlineMainEnum.BBI,
//        KlineMainEnum.SAR.getShowName()
    )
    var subChartNames = mutableListOf(
        KlineOtherEnum.VOL,
        KlineOtherEnum.MACD,
        KlineOtherEnum.KDJ,
        KlineOtherEnum.RSI,
        KlineOtherEnum.CCI,
        KlineOtherEnum.KD,
//        KlineOtherEnum.WR.getShowName(),
//        KlineOtherEnum.DMI.getShowName(),
//        KlineOtherEnum.ROC.getShowName()
    )

    val drawingList by lazy {
        mutableListOf<DrawingBean2>().apply {
            add(DrawingBean2(Keys.Markets_Kline_draw_trendline, R.attr.icon2KlineDrawTrendLineSelect, false))//趋势线
            add(DrawingBean2(Keys.Markets_Kline_draw_rays, R.attr.icon2KlineDrawRaysSelect, false))//射线
            add(DrawingBean2(Keys.Markets_Kline_draw_horizontal_line, R.attr.icon2KlineDrawLineHorizontalSelect, false))//水平线
            add(DrawingBean2(Keys.Markets_Kline_draw_vertical_line, R.attr.icon2KlineDrawLineVerticalSelect, false))//垂直线
            add(DrawingBean2(Keys.Markets_Kline_draw_parallel_lines, R.attr.icon2KlineDrawParallelLineSelect, false))//平行
            add(DrawingBean2(Keys.Markets_Kline_DrawBezier, R.attr.icon2KlineDrawShapeBezierSelect, false))//自由线
            add(DrawingBean2(Keys.Markets_Kline_DrawRectangle, R.attr.icon2KlineDrawShapeRectangleSelect, false))//矩形
        }
    }

    var mainIndicatorList: MutableList<KlineMainEnum> = mutableListOf()    // 选中主图指标类型
    var subIndicatorList: MutableList<KlineOtherEnum> = mutableListOf()      // 选中副图指标类型
    val chartTypeDataList by lazy {
        mutableListOf<MainBean>().apply {
            mainChartNames.forEach { enum ->
                if (enum != KlineMainEnum.NONE) {
                    add(MainBean(enum, enum.getShowName()))
                }
            }
            subChartNames.forEach { enum ->
                if (enum != KlineOtherEnum.NONE) {
                    add(MainBean(enum, enum.getShowName()))
                }
            }
        }
    }

    var shareOrderList: ArrayList<ShareOrderData> = arrayListOf()
    var shareOrderData: ShareOrderData? = null  // 当前选中的订单

    companion object {
        private const val TAG = "KLineChartNewViewModel"
        private const val REQUEST_TAG = "KLineChartNewViewModelREquest"
    }

    val drawDataManager by lazy {
        DrawViewDataHelper(UserDataUtil.accountCd(), viewModelScope).apply {
            setLightTheme(AppUtil.isLightTheme())
        }
    }

    fun initDrawData() {
        drawDataManager.digits = data?.digits.ifNull(2)
        drawDataManager.loadDrawData(symbol, "")
    }

    fun initOrderData() {
        shareOrderList.clear()
        shareOrderList.addAll(VAUSdkUtil.shareOrderList().filter { it.symbol == data?.symbol })
        setOrderLine()
    }

    fun setOrderLine() {
        if (shareOrderList.isNotEmpty()) {
            var selectedOrderIndex = 0
            if (!KLineDataUtils.selectedOrderNo.equals("0")) {
                selectedOrderIndex = findIndexForOrderNo()
            } else {
                KLineDataUtils.selectedOrderNo = shareOrderList[0].order
            }
            shareOrderData = shareOrderList[selectedOrderIndex]
        } else {
            shareOrderData = null
        }
    }

    // 转换选中订单的数据格式
    fun getOrderData(): OrderData? {
        return shareOrderData?.let {
            var profitUI = if (it.profitUI.isNullOrEmpty()) it.profit.numCurrencyFormat() else it.profitUI
            OrderData(
                it.volume ?: "",
                it.order ?: "",
                OrderUtil.isBuyOfOrder(it.cmd),
                it.openPrice.toFloatCatching(),
                it.takeProfit.toFloatCatching(),
                it.stopLoss.toFloatCatching(),
                it.cmd,
                profitUI ?: "",
                UserDataUtil.currencyType()
            )
        }
    }

    private fun findIndexForOrderNo(): Int {
        for (i in shareOrderList.indices) {
            if (shareOrderList[i].order == KLineDataUtils.selectedOrderNo) {
                return i
            }
        }
        return 0
    }

    fun positionToInterval(position: Int): String {
        return when (position) {
            0 -> "Tick"
            1 -> "1m"
            2 -> "5m"
            3 -> "15m"
            4 -> "30m"
            5 -> "1h"
            6 -> "4h"
            7 -> "1D"
            8 -> "1W"
            9 -> "1M"
            else -> "1D"
        }
    }

    fun updateData() {
        VAUSdkUtil.symbolList().find { it.symbol == symbol }?.let {
            symbol = it.symbol
            data = it
        }
    }

    fun intervalToPosition(interval: String): Int {
        return when (interval) {
            "Tick" -> 0
            "1m" -> 1
            "5m" -> 2
            "15m" -> 3
            "30m" -> 4
            "1h" -> 5
            "4h" -> 6
            "1D" -> 7
            "1W" -> 8
            "1M" -> 9
            else -> 7
        }
    }

    // 点击图表类型recyclerview后：滑动recyclerview 切换chartview类型
    fun switchChartPeriod(isShowLoading: Boolean, isSwitchingSymbol: Boolean = false) {
        val period = when (selectedInterval) {
            "Tick" -> 0
            "1m" -> 1
            "5m" -> 5
            "15m" -> 15
            "30m" -> 30
            "1h" -> 60
            "4h" -> 240
            "1D" -> 1440
            "1W" -> 10080
            "1M" -> 43200
            else -> return
        }
        symbolsChartPeriod(isShowLoading, period, isSwitchingSymbol)
    }

    fun getOrderRecordEntityList(): List<OrderRecordEntity> {
        val array = mutableListOf<OrderRecordEntity>()
        val orderList = VAUSdkUtil.shareOrderList().filter {
            symbol == it.symbol
        }
        orderList.forEach { order ->
            val orderRecordEntity = OrderRecordEntity().apply {
                this.time = DataUtils.dateStringToTimestamp(order.openTimeStr, "dd/MM/yyyy hh:mm:ss") / 1000
                this.isBuy = OrderUtil.isBuyOfOrder(order.cmd)
            }
            array.add(orderRecordEntity)
        }
        return array
    }

    // 设置周期，联网请求数据
    private fun symbolsChartPeriod(isShowLoading: Boolean, period: Int, isSwitchingSymbol: Boolean = false) {
        isTimeShare = period == 0
        this.period = if (period == 0) 1 else period
        symbolsChart(isShowLoading, false, false, isSwitchingSymbol = isSwitchingSymbol)
    }

    // 请求蜡烛图数据 （3000耗时10秒左右）  进入页面联网，切换1分，5分联网，每隔1分钟联网看是否有新数据
    fun symbolsChart(isShowLoading: Boolean, isLoadMore: Boolean, isAutoRefresh: Boolean, toTimeStr: String? = null, isSwitchingSymbol: Boolean = false) {
        LogUtil.v(REQUEST_TAG, "请求k线数据 [symbolsChart]: isLoadMore: $isLoadMore, isAutoRefresh: $isAutoRefresh")
        if (isShowLoading) {
            showLoading()
        }
        isRequestEnd = false
        val isSTAccount = UserDataUtil.isStLogin()
        //isSTAccount = false;
        val jsonMap = HashMap<String?, String?>()
        jsonMap.put("symbol", data?.symbol.ifNull())
        jsonMap.put("period", "$period")

        val toTimeLong = toTimeStr.toLongCatching(-1)
        toTime = if (toTimeLong != -1L && isLoadMore) {
            toTimeLong
        } else {
            (System.currentTimeMillis() / 1000 + Constants.season * 60 * 60L)
        }

        var logType = ""
        when (period) {
            1 -> logType = "1m"
            5 -> logType = "5m"
            15 -> logType = "15m"
            30 -> logType = "30m"
            60 -> logType = "1h"
            240 -> logType = "4h"
            1440 -> logType = "1D"
            10080 -> logType = "1W"
            43200 -> logType = "1M"
        }
        startTimeMillisWs = System.currentTimeMillis()

        finalLogType = logType

        if (isSTAccount) {
            stChartHistory(jsonMap, isLoadMore, isAutoRefresh, toTimeStr, isSwitchingSymbol)
        } else {
            chartHistory(jsonMap, isLoadMore, isAutoRefresh, toTimeStr, isSwitchingSymbol)
        }
    }

    fun getSecondFromInterval(): Long {
        val second = when (selectedInterval) {
            "Tick" -> 60L
            "1m" -> 60
            "5m" -> 5 * 60
            "15m" -> 15 * 60
            "30m" -> 30 * 60
            "1h" -> 60 * 60
            "4h" -> 4 * 60 * 60
            "1D" -> 24 * 60 * 60
            "1W" -> 7 * 24 * 60 * 60
            "1M" -> 30 * 24 * 60 * 60
            else -> {
                24 * 60 * 60
            }
        }
        return second
    }

    /**
     * 跟单的k线请求
     */
    private fun stChartHistory(jsonMap: HashMap<String?, String?>, isLoadMore: Boolean, isAutoRefresh: Boolean, toTimeStr: String? = null, isSwitchingSymbol: Boolean = false) {
        // 跟单账户，第一次不传，第二次才传
        if (isLoadMore) {
            jsonMap.put("to", toTime.toString() + "")
        }

        jsonMap.put("size", "300")

        saveStartDealLog(
            "symbol:" + data?.symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                toTime.toString() + "000",
                "yyyy-MM-dd HH:mm:ss"
            ), "k-line", startTimeMillisWs
        )

//        Log.i("wj", "stChartHistory: -----param: $jsonMap")
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(jsonMap).toString())
        requestNet({
            stTradingService.klineStHistoryMarketsApi(requestBody)
        }, onSuccess = {
            setKChartData(it, true, isLoadMore, isAutoRefresh, toTimeStr, isSwitchingSymbol)
        }, onError = {
            if (isSwitchingSymbol) {
                isSwitching = false
            }
            isRequestLoadMore = false
            isLandRequestLoadMore = false
            hideLoading()
            if (!isLoadMore) {
                // 展示缺省图
                noDataLiveData.value = true
            }
            stopTimerRequest()
            saveFailedDealLog(
                "symbol:" + data?.symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                    toTime.toString() + "000",
                    "yyyy-MM-dd HH:mm:ss"
                ), "-1", "k-line", startTimeMillisWs
            )
        })
    }

    /**
     * 非跟单k线请求
     */
    private fun chartHistory(jsonMap: HashMap<String?, String?>, isLoadMore: Boolean, isAutoRefresh: Boolean, toTimeStr: String? = null, isSwitchingSymbol: Boolean = false) {
        // mt5 第一次不传 to
        if (UserDataUtil.isMT5()) {
            if (isLoadMore) {
                jsonMap.put("to", toTime.toString() + "")
            }
            jsonMap.put("size", if (isAutoRefresh) "5" else "300")
        } else {
            jsonMap.put("to", toTime.toString() + "")
            //如果不是跟单账户，照旧， 跟单账户不需要传
            if (isAutoRefresh) {
                if (lastKLineEntity != null) {
                    fromTime = lastKLineEntity?.timestamp.ifNull()
                    jsonMap.put("from", fromTime)
                }
            } else {
                fromTime = frontDateLongWithStartDate(period, toTime).toString() + ""
                jsonMap.put("from", fromTime)
            }
        }

        if (!TextUtils.isEmpty(UserDataUtil.accountCd()) && !UserDataUtil.isRebateAccount()) {
            jsonMap.put("server", UserDataUtil.serverId())
        } else {
            jsonMap.put("server", "")
        }
        jsonMap.put("type", "1")
        if (UserDataUtil.isLogin()) {
            jsonMap.put("login", UserDataUtil.accountCd())
        }

        saveStartDealLog(
            "symbol:" + data?.symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                toTime.toString() + "000",
                "yyyy-MM-dd HH:mm:ss"
            ), "k-line", startTimeMillisWs
        )

        val bodyMap = HashMap<String?, String?>()
        bodyMap.put("data", buildGson().toJson(jsonMap))
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(bodyMap).toString())

        requestNet({
            tradingService.klineHistoryMarketsApi(requestBody)
        }, onSuccess = {
            LogUtil.d(TAG, "接口返回:${it.obj}")
            setKChartData(it, false, isLoadMore, isAutoRefresh, toTimeStr, isSwitchingSymbol)
        }, onError = {
            if (isSwitchingSymbol) {
                isSwitching = false
            }
            isRequestLoadMore = false
            isLandRequestLoadMore = false
            hideLoading()
            if (!isLoadMore) {
                // 展示缺省图
                noDataLiveData.value = true
            }
            stopTimerRequest()
            saveFailedDealLog(
                "symbol:" + data?.symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                    toTime.toString() + "000",
                    "yyyy-MM-dd HH:mm:ss"
                ), "-1", "k-line", startTimeMillisWs
            )
        })
    }

    private fun setKChartData(baseData: ApiResponse<KChartData>, isSTAccount: Boolean, isLoadMore: Boolean, isAutoRefresh: Boolean, toTimeStr: String? = null, isSwitchingSymbol: Boolean = false) {
        //baseData.getData() 这个后台返回值存在null的情况，会造成app闪退，因此加了特殊处理
        val isStLogin = UserDataUtil.isStLogin()
        val isUnAvailable = if (isStLogin) baseData.data == null else baseData.obj == null
        val isDataUnAvailable = if (isStLogin) baseData.data?.data?.list?.isEmpty() == true else baseData.obj?.data?.list?.isEmpty() == true
        isRequestEnd = true
        isRequestLoadMore = false
        isLandRequestLoadMore = false
        hideLoading()
        if (!baseData.isSuccess() || isUnAvailable || isDataUnAvailable) {
            if (isSwitchingSymbol) {
                isSwitching = false
            }
            if (!isLoadMore) {
                // 展示缺省图
                noDataLiveData.value = true
            }
            saveFailedDealLog(
                "symbol:" + data?.symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                    toTime.toString() + "000",
                    "yyyy-MM-dd HH:mm:ss"
                ), baseData.getResponseCode() + "", "k-line", startTimeMillisWs
            )
            return
        }

        val chartsBeanList = mutableListOf<KLineEntity>()

        if (isSTAccount) {
            val kChartData = baseData.data?.data
            kChartData?.list?.let {
                chartsBeanList.addAll(it)
            }
            // 保存小数位
            if (chartsBeanList.isNotEmpty() && kChartData?.digits.ifNull() > 0) {
                ChartUIParamUtil.digits = kChartData?.digits.ifNull()
            }
        } else {
            val kChartObj = baseData.obj?.data
            kChartObj?.list?.let {
                chartsBeanList.addAll(it)
            }
            // 保存小数位
            if (chartsBeanList.isNotEmpty() && kChartObj?.digits.ifNull() > 0) {
                ChartUIParamUtil.digits = kChartObj?.digits.ifNull()
            }
        }

        // 去掉后端返回的重复数据
        val toTime = toTimeStr.toLongCatching(-1)
        if (toTime != -1L) {
            for (i in chartsBeanList.indices.reversed()) {
                if (chartsBeanList.getOrNull(i)?.timestampLong.ifNull() >= toTime) {
                    chartsBeanList.removeAt(i)
                }
            }
        }
        if (isSwitchingSymbol) {
            isSwitching = false
        }
        noDataLiveData.value = false
        if (isLoadMore) {
            addListLiveData.value = chartsBeanList
        } else if (isAutoRefresh) {
            autoRefreshLiveData.value = chartsBeanList
            startTimerRequestKline()
        } else {
            lastKLineEntity = chartsBeanList.lastOrNull()
            newListLiveData.value = chartsBeanList
            if(isSwitchingSymbol) {
                resetLineLiveData.postValue(true)
            }
            startTimerRequestKline()
        }
        saveSuccessDealLog(
            "symbol:" + data?.symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                toTime.toString() + "000",
                "yyyy-MM-dd HH:mm:ss"
            ) + "  count:" + (if (chartsBeanList.isEmpty()) 0 else chartsBeanList.size), "k-line", startTimeMillisWs
        )
    }

    // 调用交易情绪接口
    fun tradeOrderTradeEmotion() {
        val bodyMap = HashMap<String, String>()
        bodyMap.put(
            "data", buildGson().toJson(
                hashMapOf<String, String>(
                    "symbol" to data?.symbol.ifNull()
                )
            )
        )
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(bodyMap).toString())
        requestNet({
            tradingService.tradeOrderTradeEmotion(requestBody)
        }, onSuccess = {
            if (it.isSuccess()) {
                tradeEmotionUiState.value = UIState.Success(it.data)
            } else {
                tradeEmotionUiState.value = UIState.Error(it.getResponseMsg())
            }
        })
    }

    // 调用交易价格变动接口
    fun tradeOrderTradePriceChange(isMore: Boolean = false) {
        val bodyMap = HashMap<String, String>()
        bodyMap.put(
            "data", buildGson().toJson(
                hashMapOf<String, String>(
                    "symbol" to data?.symbol.ifNull(),
                    "server" to if (UserDataUtil.accountCd().isNotEmpty() && !UserDataUtil.isRebateAccount()) UserDataUtil.serverId() else "",
                    "login" to UserDataUtil.accountCd()
                )
            )
        )
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(bodyMap).toString())
        requestNet({
            tradingService.tradeOrderTradePriceChange(requestBody)
        }, onSuccess = {
            if (it.isSuccess()) {
                if (isMore) {
                    priceChangeDataMore.value = it.data
                } else {
                    priceChangeData = it.data
                }
            } else {
                if (isMore) {
                    priceChangeDataMore.value = null
                } else {
                    priceChangeData = null
                }
            }
        }, onError = {
            if (isMore) {
                priceChangeDataMore.value = null
            } else {
                priceChangeData = null
            }
        })
    }

    fun setTakeProfitOrStopLoss(tpPrice: Float, slPrice: Float, bean: ShareOrderData?) {
        if (UserDataUtil.isStLogin()) {
            stTradePositionUpdate(tpPrice, slPrice, bean)
            return
        }
        if (bean != null) {
            val jsonObject = JsonObject()
            jsonObject.addProperty("login", UserDataUtil.accountCd())
            jsonObject.addProperty("price", bean.openPrice)
            jsonObject.addProperty("tp", tpPrice)
            jsonObject.addProperty("sl", slPrice)
            jsonObject.addProperty("order", bean.order)
            jsonObject.addProperty("token", UserDataUtil.tradeToken())
            jsonObject.addProperty("cmd", bean.cmd)
            jsonObject.addProperty("symbol", bean.symbol)
            var mulNum = "100"
            if (UserDataUtil.isMT5()) {
                mulNum = "10000"
            }
            var handleCount = bean.volume.mathMul(mulNum)
            if (handleCount.contains(".")) {
                handleCount = handleCount.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()[0]
            }
            jsonObject.addProperty("volume", handleCount)
            jsonObject.addProperty("serverId", UserDataUtil.serverId())

            val jsonObject2 = JsonObject()
            jsonObject2.addProperty("data", jsonObject.toString())
            val requestBody = jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())
            requestNet({ tradingService.tradeOrdersUpdateApi(requestBody) }, {
                if (it.code == "10100051") {
                    tokenErrorLiveData.value = it.info
                    return@requestNet
                }
                LogUtil.v(TAG, "code:${it.code}, info:${it.info}, msg:${it.msg}")
                if (it.code == "200") {
                    EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                    showToast(it.info)
                    return@requestNet
                }
                showToast(it.info)
                resetLineLiveData.postValue(true)
            }, {
                resetLineLiveData.postValue(true)
            }, isShowDialog = true)
        }
    }

    /**
     * 跟单-订单更新或取消
     */
    private fun stTradePositionUpdate(tpPrice: Float, slPrice: Float, bean: ShareOrderData?) {
        if (bean == null) {
            return
        }
        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
        jsonObject.addProperty("positionId", bean.stOrder ?: "")
        jsonObject.addProperty("takeProfit", tpPrice)
        jsonObject.addProperty("stopLoss", slPrice)
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject.toString())
        requestNet({ stTradingService.tradePositionUpdateApi(requestBody) }, {
            if (it.code == "200") {
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                // LogUtil.i("wj", "HKLineChartModel  [Post]  Constants.ORDER_CHANGE_SOCKET");
                showToast(it.msg)
                return@requestNet
            }
            showToast(it.msg)
            resetLineLiveData.postValue(true)

        }, {
            resetLineLiveData.postValue(true)
        }, isShowDialog = true)
    }

    var isScreenPortrait = true
    var isClickDrawSwitch = false
    val screenChangeRefreshLiveData by lazy { MutableLiveData<List<KLineEntity>>() }
    val landScreenChangeRefreshLiveData by lazy { MutableLiveData<List<KLineEntity>>() }
    val screenChangeNewGuideLiveData by lazy { MutableLiveData<Unit>() }
    val drawNextDataLiveData by lazy { UnPeekLiveData<Unit>() }

    /******************************************** START KLineSettingData数据初始化 *****************************************/
    /**
     * 初始化选中的主副指标数据 from KLineDataUtils.userData
     */
    fun initChartIndicatorList() {
        KLineDataUtils.userData?.let {
            //主图选中
            val mainList = it.mainChartNameList
                .mapNotNull { value ->
                    val enum = KlineMainEnumUtils.value2KlineMainEnum(value)
                    if (enum != KlineMainEnum.NONE) enum else null
                }
                .takeIf { take ->
                    take.isNotEmpty()
                } ?: listOf()
            mainIndicatorList = mainList.toMutableList()
            //副图选中
            val subList = it.subChartNameList
                .mapNotNull { value ->
                    val enum = KlineOtherEnumUtils.value2KlineOtherEnum(value)
                    if (enum != KlineOtherEnum.NONE) enum else null
                }
                .takeIf { take ->
                    take.isNotEmpty()
                } ?: listOf()
            subIndicatorList = subList.take(4).toMutableList()
        }
    }

    /**
     * 初始化KLineDataUtils.userData from sp
     */
    fun initKLineSettingData() {
        if (KLineDataUtils.userData == null) {
            KLineDataUtils.userData = KLineSettingData.getUserData()
        }
    }

    fun updateAndSaveKLineSettingData() {
        KLineDataUtils.userData?.run {
            val mainIndicatorShowNameList = mainIndicatorList.map {
                it.getShowName()
            }
            val subIndicatorShowNameList = subIndicatorList.map {
                it.getShowName()
            }
            mainChartNameList = mainIndicatorShowNameList.toMutableList()
            subChartNameList = subIndicatorShowNameList.toMutableList()
            save()
        }
    }
    /******************************************** END KLineSettingData数据初始化 *****************************************/

    /*********************************************START 横屏k线定时请求数据逻辑 ****************************************/

    private var requestKLineTimerJob: Job? = null
    private var requestPeriod = 0
    private var isFirstLoad = true

    /**
     * KlineActivity onResume
     */
    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        LogUtil.v(REQUEST_TAG, "onResume isFirstLoad:$isFirstLoad, $owner")
        if (isSwitching) return
        if (isFirstLoad) {
            isFirstLoad = false
        } else {
            symbolsChart(isShowLoading = false, isLoadMore = false, isAutoRefresh = false)
        }
    }

    /**
     * KlineActivity onPause
     */
    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        LogUtil.v(REQUEST_TAG, "onPause $owner")
        stopTimerRequest()
    }

    override fun onCleared() {
        super.onCleared()
        stopTimerRequest()
    }

    fun stopTimerRequest() {
        LogUtil.v(TAG, "停止定时任务")
        requestKLineTimerJob?.cancel()
    }

    private fun startTimerRequestKline(isForeStart: Boolean = false) {
        LogUtil.v(TAG, "准备开启 isActive: ${requestKLineTimerJob?.isActive}, isForeStart:${isForeStart}, requestPeriod:$requestPeriod, period:$period")
        if (!isForeStart && (requestKLineTimerJob?.isActive == true && requestPeriod == period)) {
            return
        }
        requestPeriod = period
        LogUtil.v(TAG, "取消定时任务 requestPeriod:$requestPeriod")
        requestKLineTimerJob?.cancel()
        if (period <= 60) {//1小时以外不用开启定时 && 不是分时图
            val initialDelay = calculateDelayTime() // 首次延迟
            val interval = (60 * 1000 * period).toLong() // 后续间隔
            LogUtil.v(TAG, "定时任务开启 initialDelay:$initialDelay, interval:$interval")
            requestKLineTimerJob = viewModelScope.launch {
                LogUtil.v(TAG, "调用")
                repeatWhile(initialDelay, interval) {
                    LogUtil.v(TAG, "请求接口 initialDelay:$initialDelay, interval:$interval")
                    requestKLineIntervals()
                }
            }
        }
    }

    private fun requestKLineIntervals() {
        val dataBean = data
        if (dataBean?.ask == 0f && dataBean.bid == 0f) return
        drawNextDataLiveData.value = Unit
        symbolsChart(isShowLoading = false, isLoadMore = false, isAutoRefresh = true)
    }

    private fun calculateDelayTime(): Long {
        val second = (60 * period).toLong() // 每个时间段的总计秒数
        val calendar = Calendar.getInstance()
        val currentMinute = calendar.get(Calendar.MINUTE)
        val currentSecond = calendar.get(Calendar.SECOND)
        return (second - (currentMinute * 60 + currentSecond) % second) * 1000
    }

    private suspend fun repeatWhile(initialDelay: Long, interval: Long, action: () -> Unit) = coroutineScope {
        delay(initialDelay)
        action()
        while (isActive) {
            delay(interval)
            action()
        }
    }
    /*********************************************END 横屏k线定时请求数据逻辑 ****************************************/
}