package cn.com.vau.page.common.selectArea

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseExpandableListAdapter
import android.widget.TextView
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.data.account.SelectCountryNumberObj
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.setFontG400
import cn.com.vau.util.setFontG600


/**
 * 区号选择适配器
 * Created by Administrator on 2018/9/17.
 */
class AreaCodeAdapter(
    var mContext: Context,
    val listData: List<SelectCountryNumberObj>,
    val selectedCountryNumber: String,
    var listener: OnItemClickListener
) : BaseExpandableListAdapter() {

    override fun getGroup(groupPosition: Int): SelectCountryNumberObj =
        listData.elementAt(groupPosition)

    override fun isChildSelectable(groupPosition: Int, childPosition: Int): Boolean = false

    override fun hasStableIds(): Boolean = true

    override fun getGroupView(
        groupPosition: Int,
        isExpanded: Boolean,
        convertView: View?,
        parent: ViewGroup?
    ): View {
        var view = convertView
        var holder: ParentViewHolder? = null
        if (view == null) {
            view = LayoutInflater.from(mContext)
                .inflate(R.layout.item_select_country_number_letter, parent, false)
            holder = ParentViewHolder(view)
            view!!.tag = holder
        } else {
            holder = view.tag as ParentViewHolder
        }
        val data = getGroup(groupPosition)
        holder.letterView?.text = data.lettername
        val regex = """[A-Z]{1}""".toRegex()
        holder.letterView?.visibility =
            if (regex.matches(data.lettername.toString())) View.VISIBLE else View.GONE
        /*
        逻辑代码判断接口返回的第一条数据Popular不显示，item还存在，会导致多一条分隔线，显得顶部分隔线偏粗，
        所以要把顶部的分隔线去掉，后续如果想显示第一条item的Popular，这行代码就没用了可删除
         */
        holder.viewTopLine?.isVisible = groupPosition != 0
        return view
    }

    override fun getChildrenCount(groupPosition: Int): Int =
        listData.elementAt(groupPosition).list?.size ?: 0

    override fun getChild(groupPosition: Int, childPosition: Int): SelectCountryNumberObjDetail =
        listData.elementAt(groupPosition).list?.elementAt(childPosition)
            ?: SelectCountryNumberObjDetail()

    override fun getGroupId(groupPosition: Int): Long = groupPosition.toLong()

    @SuppressLint("SetTextI18n")
    override fun getChildView(
        groupPosition: Int,
        childPosition: Int,
        isLastChild: Boolean,
        convertView: View?,
        parent: ViewGroup?
    ): View {
        var view = convertView
        var holder: ChildViewHolder? = null
        if (view == null) {
            view = LayoutInflater.from(mContext)
                .inflate(R.layout.item_select_country_number, parent, false)
            holder = ChildViewHolder(view)
            view!!.tag = holder
        } else {
            holder = view.tag as ChildViewHolder
        }
        val data = listData.elementAt(groupPosition).list?.elementAt(childPosition)
        holder.tvCountryName?.text = "${data?.countryName ?: ""}(${data?.countryCode ?: ""})"
        holder.tvCountryNumber?.text = "+${data?.countryNum ?: ""}"
        holder.gapline?.visibility =
            if (isLastChild || data?.countryNum == selectedCountryNumber || listData.elementAt(
                    groupPosition
                ).list?.elementAtOrNull(childPosition + 1)?.countryNum == selectedCountryNumber
            ) View.GONE else View.VISIBLE
        if (data?.countryNum == selectedCountryNumber) {
            holder.tvCountryName?.setBackgroundColor(
                AttrResourceUtil.getColor(mContext, R.attr.color_c0a1e1e1e_c0affffff)
            )
            holder.tvCountryName?.setFontG600()
            holder.tvCountryNumber?.setFontG600()
        } else {
            holder.tvCountryName?.setBackgroundColor(0)
            holder.tvCountryName?.setFontG400()
            holder.tvCountryNumber?.setFontG400()
        }
        view.setOnClickListener {
            listener.onItemSelected(groupPosition, childPosition)
        }

        return view
    }

    override fun getChildId(groupPosition: Int, childPosition: Int): Long = childPosition.toLong()

    override fun getGroupCount(): Int = listData.size

    class ParentViewHolder(convertView: View) {
        var letterView: TextView? = null
        var viewTopLine : View?=null

        init {
            letterView = convertView.findViewById(R.id.tvItemLetter)
            viewTopLine = convertView.findViewById(R.id.viewTopLine)
        }
    }

    class ChildViewHolder(convertView: View) {
        var tvCountryName: TextView? = null
        var tvCountryNumber: TextView? = null
        var gapline: View? = null

        init {
            tvCountryName = convertView.findViewById(R.id.tvCountryName)
            tvCountryNumber = convertView.findViewById(R.id.tvCountryNumber)
            gapline = convertView.findViewById(R.id.gapline)
        }
    }

    interface OnItemClickListener {
        fun onItemSelected(groupPosition: Int, childPosition: Int)
    }

}