package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.popup.InfoBottomListXPopup
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.ActivityCloseByOrderBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.adapter.CloseByOrderRecyclerAdapter
import cn.com.vau.trade.model.CloseByOrderViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import com.lxj.xpopup.XPopup
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 互抵平仓
 */
class CloseByOrderActivity : BaseMvvmActivity<ActivityCloseByOrderBinding, CloseByOrderViewModel>(), SDKIntervalCallback {

    private val c00c79c by lazy { ContextCompat.getColor(this, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(this, R.color.cf44040) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff) }
    private val lots by lazy { getString(R.string.lots) }

    private var mAdapter: CloseByOrderRecyclerAdapter? = null

    override fun onCallback() {
        updateMainOrderViewData()
        refreshAdapter(false)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
        SDKIntervalUtil.instance.addCallBack(this)
        // 赋值主订单号
        mViewModel.mainOrderId = intent?.extras?.getString(Constants.PARAM_ORDER_NUMBER).ifNull()
        // 主订单
        mViewModel.mainOrderData = VAUSdkUtil.shareOrderList().firstOrNull {
            it.order == mViewModel.mainOrderId
        }
        // 互抵订单集合
        mViewModel.closeByOrderList = VAUSdkUtil.shareOrderList().filter {
            it.symbol == mViewModel.mainOrderData?.symbol && it.cmd != mViewModel.mainOrderData?.cmd
        }
        mViewModel.closeByOrderId = mViewModel.closeByOrderList?.firstOrNull()?.order.ifNull()
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {

        mBinding.mHeaderBar.setEndIconClickListener {
            showGlossary()
        }

        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)

        mBinding.includeOrder.tvPnlTitle.text = "${getString(R.string.pnl)} (${UserDataUtil.currencyType()})"
        mBinding.includeOrder.tvVolTitle.text = "${getString(R.string.volume)} (${getString(R.string.lot)})"
        mBinding.includeOrder.ivKLine.visibility = View.GONE

        if (OrderUtil.isBuyOfOrder(mViewModel.mainOrderData?.cmd)) {
            mBinding.includeOrder.tvOrderType.text = "Buy"
            mBinding.includeOrder.tvOrderType.setTextColor(ContextCompat.getColor(this, R.color.c00c79c))
            mBinding.includeOrder.tvOrderType.background = ContextCompat.getDrawable(this, R.drawable.shape_c1f00c79c_r100)
        } else {
            mBinding.includeOrder.tvOrderType.text = "Sell"
            mBinding.includeOrder.tvOrderType.setTextColor(ContextCompat.getColor(this, R.color.ce35728))
            mBinding.includeOrder.tvOrderType.background = ContextCompat.getDrawable(this, R.drawable.shape_c1fe35728_r100)
        }
        updateMainOrderViewData()

        mAdapter = CloseByOrderRecyclerAdapter(this, mViewModel.closeByOrderList)
        mBinding.mRecyclerView.adapter = mAdapter

        mAdapter?.setDataOfCloseByOrderId(mViewModel.closeByOrderId)

        mBinding.tvEstimatedTotalPnlTitle.text = "${getString(R.string.estimated_total_pnl)}: "

    }

    override fun initListener() {
        super.initListener()

        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        mBinding.tvNext.setOnClickListener(this)

        mAdapter?.setOnItemClickListener(object : CloseByOrderRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(orderId: String) {
                mViewModel.closeByOrderId = orderId
                mAdapter?.setDataOfCloseByOrderId(mViewModel.closeByOrderId)
            }
        })
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.tradePositionMutualOffsetClosePositionLiveData.observe(this) {
            CenterActionWithIconDialog.Builder(this)
                .setTitle(getString(R.string.close_confirmed))
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setSingleButton(true)
                .setSingleButtonText(getString(R.string.ok))
                .setOnSingleButtonListener {
                    ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
                    finish()
                }
                .build()
                .showDialog()
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.ivLeft -> finish()
            R.id.tvNext -> {
                mViewModel.tradePositionMutualOffsetClosePosition()
            }
        }
    }

    /**
     * 更新主订单数据
     */
    @SuppressLint("SetTextI18n")
    fun updateMainOrderViewData() {
        val data = mViewModel.mainOrderData ?: return
        mBinding.includeOrder.tvProdName.setTextDiff(data.symbol.ifNull())
        mBinding.includeOrder.tvOrderId.setTextDiff("#${data.order.ifNull()}")
        mBinding.includeOrder.tvVolume.setTextDiff("${data.volumeUI} $lots")
        mBinding.includeOrder.tvOpenPrice.setTextDiff(data.openPrice.ifNull())
        mBinding.includeOrder.tvCurrentPrice.setTextDiff(
            if ("-" == data.closePrice) "-" else data.currentPriceUI.ifNull()
        )
        mBinding.includeOrder.tvPnl.setTextDiff(
            if ("-" == data.closePrice) "-" else data.profitUI.ifNull()
        )
        if ("-" == data.closePrice) {
            mBinding.includeOrder.tvPnl.setTextColorDiff(color_c1e1e1e_cebffffff)
        } else {
            mBinding.includeOrder.tvPnl.setTextColorDiff(if (data.profit >= 0) c00c79c else cf44040)
        }

        val closeData = mViewModel.closeByOrderList?.firstOrNull {
            it.order == mViewModel.closeByOrderId
        }

        val mainData = mViewModel.mainOrderData

        /**
         * 计算公式为订单盈亏（该订单盈亏和两个订单无关，是一个单独计算的订单盈亏）+ 本身订单的隔夜仓息 + 本身订单的手续费 + 互抵订单的隔夜仓息 + 互抵订单的手续费
         * 订单盈亏计算的开仓价为本身订单的开仓价，平仓价为互抵订单的开仓价，方向为本身订单方向，手数为两个订单手数中最小的那个
         */
        val estimatedOrderPnl = VAUSdkUtil.getProfitLoss(
            VAUSdkUtil.symbolList().firstOrNull { it.symbol == mainData?.symbol } ?: ShareProductData(),
            mainData?.openPrice.ifNull(),
            // 取两个订单最小值
            mainData?.volume.ifNull().coerceAtMost(closeData?.volume.ifNull()),
            mainData?.cmd.ifNull(),
            closeData?.openPrice.ifNull()
        )

        mBinding.tvEstimatedTotalPnl.setTextDiff(estimatedOrderPnl.numCurrencyFormat())
        mBinding.tvEstimatedTotalPnl.setTextColorDiff(if (estimatedOrderPnl < 0) cf44040 else c00c79c)

    }

    @SuppressLint("NotifyDataSetChanged")
    fun refreshAdapter(state: Boolean) {
        if (state) {
            mAdapter?.notifyDataSetChanged()
        } else {
            for (index in (mViewModel.closeByOrderList ?: CopyOnWriteArrayList()).indices) {
                mAdapter?.notifyItemChanged(index, Constants.DEFAULT_PAYLOAD_KEY)
            }
        }
    }

    /**
     * 持仓订单有变化,同步订单数据
     * 1 持仓列表订单小于2  --> GG
     * 2 主订单不存在      --> GG
     * 3 互抵订单不存在    --> GG
     * 4 选中订单
     *   a 原选中订单存在             --> 刷新数据
     *   b 原选中订单不存在，选中第一个  --> 刷新数据
     */
    fun syncOrdersData() {

        if (false == mViewModel.isReceiptNotification) return

        val shareOrderList = VAUSdkUtil.shareOrderList()
        if (shareOrderList.size < 2) {
            showNoOrderDialog()
            return
        }

        // 无主订单
        if (shareOrderList.none { it.order == mViewModel.mainOrderId }) {
            showNoOrderDialog()
            return
        }

        mViewModel.mainOrderData = VAUSdkUtil.shareOrderList().firstOrNull {
            it.order == mViewModel.mainOrderId
        }

        // 寻找 互抵 订单
        mViewModel.closeByOrderList = shareOrderList.filter {
            it.symbol == mViewModel.mainOrderData?.symbol && it.cmd != mViewModel.mainOrderData?.cmd
        }
        if (mViewModel.closeByOrderList?.size.ifNull() <= 0) {
            showNoOrderDialog()
            return
        }
        mAdapter?.setData(mViewModel.closeByOrderList)

        // 处理选中订单
        if (mViewModel.closeByOrderList?.none { it.order == mViewModel.closeByOrderId }.ifNull()) {
            mViewModel.closeByOrderId = mViewModel.closeByOrderList?.getOrNull(0)?.order.ifNull()
            mAdapter?.setDataOfCloseByOrderId(mViewModel.closeByOrderId)
        }

    }

    private fun showNoOrderDialog() {
        CenterActionDialog.Builder(this)
            // 无互抵订单存在
            .setContent(getString(R.string.there_is_no_order_can_close_by))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .setOnSingleButtonListener {
                finish()
            }
            .build()
            .showDialog()
    }

    private fun showGlossary() {
        XPopup.Builder(this)
            .navigationBarColor(AttrResourceUtil.getColor(this, R.attr.mainLayoutBg))
            .asCustom(
                InfoBottomListXPopup(
                    this,
                    mBinding.mHeaderBar.getTitleView()?.text.toString(),
                    arrayListOf(
                        HintLocalData(getString(R.string.glossary_close_by_1)),
                        HintLocalData(getString(R.string.glossary_close_by_2)),
                        HintLocalData(getString(R.string.glossary_close_by_3)),
                        HintLocalData(getString(R.string.glossary_close_by_4))
                    )
                )
            ).show()
    }

    override fun onMsgEvent(eventTag: String) {
        super.onMsgEvent(eventTag)
        when (eventTag) {
            // 持仓订单有变化
            NoticeConstants.Init.DATA_SUCCESS_ORDER -> {
                syncOrdersData()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        SDKIntervalUtil.instance.removeCallBack(this)
    }

}