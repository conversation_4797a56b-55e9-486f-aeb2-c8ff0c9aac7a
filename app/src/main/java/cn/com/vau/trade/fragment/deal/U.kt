package cn.com.vau.trade.fragment.deal

import android.util.Log
import cn.com.vau.data.init.ShareProductData

class U {

    companion object{
        fun print(where : String, dataList: ArrayList<ShareProductData>){
//            Log.i("xinhuan","-------- " + where + " -----开始------")
//            dataList.forEachIndexed { index, shareProductData ->
//                printBean(index, shareProductData)
//            }
//            Log.i("xinhuan","-------- " + where + " ------结束-----")
        }

        fun printBean(index :Int,  shareProductData : ShareProductData) {
//            Log.i("xinhuan", "index = " + index + " symbol = " + shareProductData.symbol + ", refresh = " + shareProductData.refresh+ ", ask = " + shareProductData.ask + " , askUI = " + shareProductData.askUI)
        }
    }
}