package cn.com.vau.trade.fragment.deal

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.*
import android.view.*
import android.view.animation.AccelerateInterpolator
import androidx.recyclerview.widget.*
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.FragmentDealItemBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.activity.*
import cn.com.vau.trade.adapter.DealItemRecyclerAdapter
import cn.com.vau.trade.model.DealItemModel
import cn.com.vau.trade.presenter.*
import cn.com.vau.util.AdapterRefreshNotifyItemController
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.TradeSortUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.greenrobot.eventbus.*
import org.json.JSONObject

@SuppressLint("WrongConstant")
class DealItemFragment : BaseFrameFragment<DealItemPresenter, DealItemModel>(),
    DealItemContract.View ,SDKIntervalCallback {

    private val mBinding by lazy { FragmentDealItemBinding.inflate(layoutInflater) }

    private var isViewCreated: Boolean = false
    private var isUIVisible: Boolean = false

    private var dealIndex: Int = 0
    private var mAdapter: DealItemRecyclerAdapter? = null
    private var dataList: ArrayList<ShareProductData> = arrayListOf()
    private var refreshController : AdapterRefreshNotifyItemController?= null

    private val layoutManager: WrapContentLinearLayoutManager by lazy {
        WrapContentLinearLayoutManager(context).apply {
            orientation = OrientationHelper.VERTICAL
        }
    }

    override fun onCallback() {
        refreshAdapter(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (arguments != null) {
            dealIndex = requireArguments().getInt(ARG_PARAM1)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        isViewCreated = true
        lazyInitData()
        return mBinding.root
    }

    override fun initParam() {
        super.initParam()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    @SuppressLint("WrongConstant", "SetTextI18n")
    override fun initView() {
        super.initView()
        val switchMode = SpManager.getTradeSwitchMode()
        mBinding.mRecyclerView.layoutManager = layoutManager
        mAdapter = DealItemRecyclerAdapter(requireContext(), dataList, switchMode)
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.dp2px()))
        recyclerViewOpt()
        startAlphaAnimator()
    }

    private fun startAlphaAnimator() {
        val ofFloat = ObjectAnimator.ofFloat(mBinding.mRecyclerView, "alpha", 0f, 1f)
        ofFloat.duration = 350
        ofFloat.interpolator = AccelerateInterpolator()
        ofFloat.start()
    }

    //recyclerView优化
    private fun recyclerViewOpt() {
        //加大缓存
        mBinding.mRecyclerView.recycledViewPool.setMaxRecycledViews(0, 20)
        mBinding.mRecyclerView.setHasFixedSize(true)
//        mBinding.mRecyclerView.setRecycledViewPool(TradesFragment.sRecycledViewPool)
        layoutManager.recycleChildrenOnDetach = true
        (mBinding.mRecyclerView.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        refreshController = AdapterRefreshNotifyItemController(mBinding.mRecyclerView, mAdapter)
    }

    private fun lazyInitData() {
        if (!isViewCreated || !isUIVisible) return
        dataList.clear()
        refreshAdapter(true)

        if (dealIndex >= VAUSdkUtil.shareGoodList().size) return

        dataList.addAll(VAUSdkUtil.shareGoodList().getOrNull(dealIndex)?.symbolList ?: arrayListOf())
        val sortMode = SpManager.getTradeSortRose()
        if (sortMode == 0) {
            dataList.sortBy { it.marketClose }
            dataList.sortByDescending { it.enable }
        } else {
            TradeSortUtil.sort(dataList, null)
        }

        refreshAdapter(true)
        isViewCreated = false
        isUIVisible = false
    }

    @SuppressLint("SetTextI18n")
    override fun initListener() {
        super.initListener()

        mAdapter?.setOnItemClickListener(object : DealItemRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {
                adapterOnItemClick(position)
            }
        })
    }

    private fun adapterOnItemClick(position: Int) {

        if ("0" == dataList.getOrNull(position)?.enable) {
            ToastUtil.showToast(getString(R.string.this_symbol_is_untradable))
            return
        }

        val symbolName = dataList.elementAtOrElse(position) { ShareProductData() }.symbol.ifNull()
        val bundle = Bundle()
        bundle.putString(Constants.PARAM_PRODUCT_NAME, symbolName)
        openActivity(KLineActivity::class.java, bundle, true)

        // 神策自定义埋点(v3500)
        sensorsTrack(symbolName, position)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun refreshAdapter(refreshState: Boolean) {
        TestLog.print("消费数据  refresh", dataList)
        if (refreshState) {
            mAdapter?.notifyDataSetChanged()
        } else {
            refreshController?.refresh(recordRefreshPositionList())
        }
    }

    //搜集这次刷新的position
    private fun recordRefreshPositionList() :MutableList<Int>{
        val refreshPositionList = mutableListOf<Int>()
        for ((index, dataBean) in dataList.withIndex()) {
            if (dataBean.refresh) {
                //收集刷新的位置
                refreshPositionList.add(index)
                dataBean.refresh = false
            }
        }
        return refreshPositionList
    }

    @SuppressLint("NotifyDataSetChanged")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.Init.APPLICATION_START -> {
                refreshAdapter(true)
            }

            NoticeConstants.Init.DATA_SUCCESS_GOODS -> {
                isViewCreated = true
                isUIVisible = true
                lazyInitData()
            }

            // 切换排序
            NoticeConstants.Quotes.TRADE_SORT_CHANGE_UP,
            NoticeConstants.Quotes.TRADE_SORT_CHANGE_DOWN -> {
                if (mAdapter != null && dataList.isNotEmpty()) {
                    TradeSortUtil.sort(dataList, tag) {
                        mAdapter?.notifyDataSetChanged()
                    }
                }
            }

            NoticeConstants.Quotes.TRADE_SORT_CHANGE_NONE -> {
                if (mAdapter != null && dataList.isNotEmpty()) {
                    dataList.clear()
                    dataList.addAll(
                        VAUSdkUtil.shareGoodList().getOrNull(dealIndex)?.symbolList ?: arrayListOf()
                    )
                    dataList.sortBy { it.marketClose }
                    dataList.sortByDescending { it.enable }
                    mAdapter?.notifyDataSetChanged()
                }
            }

            // 切换Buy/Sell模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_BUYSELL,
            // 切换Classic模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_CLASSIC -> {
                mAdapter?.setMode(SpManager.getTradeSwitchMode())
            }

        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        if (isVisibleToUser) {

            //问题现象：tabLayout切换时，尤其是两个tab间隔较远切换。出现Fragment中的列表部分一片空白。在普通手机上可以偶现，但是在小米13（QA张瑶的测试机）上必现。
            //原因：由于Fragment被系统回收的情况，导致Fragment页面中的recyclerView无子View，后续notifyItemChange自然也无效，除非notifyDataSetChange一下让列表显示，之后的更新再用notifyItemChange。
            // lazyInitData()方法中的isViewCreated为false，导致lazyInitData()后面的方法得不到执行，无法执行adapter?.notifyDataSetChanged()。
            //因此在这里加上notifyDataSetChange则解决此问题
            mAdapter?.notifyDataSetChanged()
            isUIVisible = true

            lazyInitData()
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
        } else {
            SDKIntervalUtil.instance.removeCallBack(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    /**
     * 神策自定义埋点(v3500)
     * App_交易页产品点击 -> 点击app交易页产品时触发
     */
    private fun sensorsTrack(symbolName: String, position: Int) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.SYMBOL_ID, symbolName) // symbolId
        properties.put(SensorsConstant.Key.SYMBOL_NAME, symbolName) // symbol名称
        properties.put(SensorsConstant.Key.SYMBOL_RANK, position + 1) // symbol排序
        properties.put(SensorsConstant.Key.BUTTON_NAME, "") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TRADES_PRODUCT_CLICK, properties)
    }

    companion object {

        const val ARG_PARAM1 = "typeParam"

        fun newInstance(typeParam: Int): DealItemFragment {
            val fragment = DealItemFragment()
            val args = Bundle()
            args.putInt(ARG_PARAM1, typeParam)
            fragment.arguments = args
            return fragment
        }
    }

}
