package cn.com.vau.trade.st.model

import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.livedata.UnPeekLiveData
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.StShareStrategyData
import cn.com.vau.data.strategy.*
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.fragment.strategy.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import com.google.gson.JsonObject
import kotlinx.coroutines.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

class StStrategyOrdersViewModel : BaseViewModel() {

    val tabTitleList = arrayListOf<String>()
    val fragmentList = arrayListOf<Fragment>()
    var stSettingsFragment: StStrategyOrdersSettingsFragment? = null

    var baseData: StrategyOrderBaseData? = null

    var stProfitSharingProfileFollowerPortfolioLiveData = MutableLiveData<StFollowerStrategyPortfolioData>()
    var stStrategyCopySettingsLiveData = MutableLiveData<StStrategyCopySettingsData>()

    // 停止跟随策略响应
    var stAccountRemoveFollowerLiveData = MutableLiveData<Boolean>()

    // 暂停/恢复 跟随策略响应
    var responsePauseOrResumeStrategyLiveData = MutableLiveData<Boolean>()

    // 删除 open order
    var stTradePositionCancelLiveData = MutableLiveData<Long>()

    // 收藏数量
    var stStrategyFansCountLiveData = MutableLiveData<StStrategyFansCountBean>()

    // 接口响应 (临时方案)
    var responseLiveData = MutableLiveData<String>()

    var shareStrategyData: StShareStrategyData? = null

    val popManagerTitleList by lazy {
        arrayListOf(
            StringUtil.getString(R.string.add_funds),
            StringUtil.getString(R.string.remove_funds),
            StringUtil.getString(R.string.pause_copying),
            StringUtil.getString(R.string.stop_copy),
            StringUtil.getString(R.string.more_settings)
        )
    }

    var closeConfirmPopupLiveData = UnPeekLiveData<Boolean>()

    fun initShareFollowStrategyData() {
        shareStrategyData = VAUSdkUtil.stShareStrategyList().firstOrNull {
            baseData?.signalStrategyId == it.strategyId
        }

    }

    fun initTabTitleList() {
        tabTitleList.clear()
        fragmentList.clear()

        if (EnumStrategyFollowState.OPEN == baseData?.type) {

            tabTitleList.add(StringUtil.getString(R.string.positions))
            fragmentList.add(StStrategyOrdersPositionsFragment())

            if (0 != (shareStrategyData?.pendingOpen?.size ?: 0)) {
                tabTitleList.add(StringUtil.getString(R.string.pending_open))
                fragmentList.add(StStrategyOrdersPendingOpenFragment())
            }

            if (0 != (shareStrategyData?.pendingClose?.size ?: 0)) {
                tabTitleList.add(StringUtil.getString(R.string.pending_close))
                fragmentList.add(StStrategyOrdersPendingCloseFragment())
            }

        }

        if (EnumStrategyFollowState.OPEN == baseData?.type || EnumStrategyFollowState.HISTORY == baseData?.type) {
            tabTitleList.add(StringUtil.getString(R.string.history))
            fragmentList.add(StStrategyOrdersHistoryFragment())
        }

        if (EnumStrategyFollowState.OPEN == baseData?.type) {
            // 分润比例
            tabTitleList.add(StringUtil.getString(R.string.profit_sharing))
            fragmentList.add(StStrategyOrdersProfitSharingFragment())
        }

        tabTitleList.add(StringUtil.getString(R.string.settings))
        stSettingsFragment = StStrategyOrdersSettingsFragment()
        fragmentList.add(stSettingsFragment!!)

    }

    // 分润
    fun stProfitSharingProfileFollowerPortfolio() {
        requestNet({
            stTradingService.profitSharingProfileFollowerPortfolioApi(
                shareStrategyData?.portfolioId ?: ""
            )
        }, {
            responseLiveData.value = StringUtil.getString(R.string.profit_sharing)
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            it.data?.let { data ->
                stProfitSharingProfileFollowerPortfolioLiveData.value = data
            }
        }, {
            responseLiveData.value = StringUtil.getString(R.string.profit_sharing)
        })
    }

    /**
     *  跟随设置
     */
    fun stStrategyCopySettings() {
        val type = when (baseData?.type) {
            EnumStrategyFollowState.OPEN -> "Following"
            EnumStrategyFollowState.HISTORY -> "Stopped"
            EnumStrategyFollowState.PENDING_REVIEW -> "Pending"
            EnumStrategyFollowState.REJECTED -> "Rejected"
            else -> ""
        }

        val id = when (baseData?.type) {
            EnumStrategyFollowState.OPEN,
            EnumStrategyFollowState.HISTORY -> {
                baseData?.portfolioId ?: ""
            }

            else -> {
                baseData?.followRequestId ?: ""
            }
        }

        requestNet({
            stTradingService.strategyCopySettingsApi(type, id)
        }, {
            responseLiveData.value = StringUtil.getString(R.string.settings)
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            it.data?.let { data ->
                stStrategyCopySettingsLiveData.value = data
            }
        }, {
            responseLiveData.value = StringUtil.getString(R.string.settings)
        })
    }

    /**
     * 暂停
     */
    fun stAccountPauseFollowing() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
        jsonObject.addProperty("portfolioId", shareStrategyData?.portfolioId ?: "")
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        val dealLog = "copy trader:${shareStrategyData?.strategyName ?: ""}"
        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(dealLog, "pause copy", startTimeMillis)

        requestNet({
            stTradingService.accountPauseFollowingApi(requestBody)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                DealLogUtil.saveFailedDealLog(dealLog, it.getResponseCode(), "pause copy", startTimeMillis)
                return@requestNet
            }

            shareStrategyData?.followingStatus = "2"
            responsePauseOrResumeStrategyLiveData.value = true

            DealLogUtil.saveSuccessDealLog(dealLog, "pause copy", startTimeMillis)

        }, isShowDialog = true)
    }

    /**
     * 恢复跟随
     */
    fun stAccountResumeFollowing() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
        jsonObject.addProperty("portfolioId", shareStrategyData?.portfolioId ?: "")
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        val dealLog = "copy trader:${shareStrategyData?.strategyName ?: ""}"
        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(dealLog, "resume copy", startTimeMillis)

        requestNet({
            stTradingService.accountResumeFollowingApi(requestBody)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                DealLogUtil.saveFailedDealLog(dealLog, it.getResponseCode(), "resume copy", startTimeMillis)
                return@requestNet
            }
            shareStrategyData?.followingStatus = "1"
            responsePauseOrResumeStrategyLiveData.value = false

            DealLogUtil.saveSuccessDealLog(dealLog, "resume copy", startTimeMillis)
        }, isShowDialog = true)
    }

    /**
     * 停止跟随
     */
    fun stAccountRemoveFollower() {
        val dealLog = "copy trader:${shareStrategyData?.strategyName ?: ""}"
        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(dealLog, "stop copy", startTimeMillis)

        requestNet({
            stTradingService.accountRemoveFollowerApi(shareStrategyData?.portfolioId ?: "")
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                DealLogUtil.saveFailedDealLog(dealLog, it.getResponseCode(), "stop copy", startTimeMillis)
                // 埋点
                sensorsCopyTradingStopCopyResult(shareStrategyData?.strategyName.ifNull(), shareStrategyData?.strategyId.ifNull(), false)
                return@requestNet
            }
            stAccountRemoveFollowerLiveData.value = 0 == (shareStrategyData?.positions?.size ?: 0)
            // 埋点
            sensorsCopyTradingStopCopyResult(shareStrategyData?.strategyName.ifNull(), shareStrategyData?.strategyId.ifNull(), true)
            DealLogUtil.saveSuccessDealLog(dealLog, "stop copy", startTimeMillis)
        }, isShowDialog = true)
    }

    /**
     * 平仓
     */
    fun stTradePositionClose(orderData: ShareOrderData?) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", baseData?.portfolioId ?: "")
        jsonObject.addProperty("positionId", orderData?.stOrder ?: "")
        jsonObject.addProperty("volume", orderData?.volume ?: "")

        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("copy trader:${stStrategyCopySettingsLiveData.value?.nickname ?: ""} close order:#${orderData?.stOrder ?: ""} volume:${orderData?.volume ?: ""}", startTimeMillis)

        if (orderData?.status == "PENDINGOPEN")
            jsonObject.addProperty("orderState", "PendingOpen")
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet({
            stTradingService.tradePositionCloseApi(requestBody)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                DealLogUtil.saveFailedDealLog(
                    "copy trader:${stStrategyCopySettingsLiveData.value?.nickname ?: ""} close order:#${orderData?.stOrder ?: ""}",
                    it.getResponseCode(), "close", startTimeMillis
                )
                return@requestNet
            }
            DealLogUtil.saveSuccessDealLog(
                "copy trader:${stStrategyCopySettingsLiveData.value?.nickname ?: ""} close order:#${orderData?.stOrder ?: ""}",
                "close",
                startTimeMillis
            )

            viewModelScope.launch(Dispatchers.Main) {
                delay(1000)
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                closeConfirmPopupLiveData.value = true
            }
        }, {
            DealLogUtil.saveFailedDealLog(
                "copy trader:${stStrategyCopySettingsLiveData.value?.nickname ?: ""} close order:#${orderData?.stOrder ?: ""}",
                "-1", "close", startTimeMillis
            )
        }, isShowDialog = true)
    }

    /**
     *  获取 收藏状态 和 收藏数量
     */
    fun stStrategyFansCountApi() {
        requestNet({
            stTradingService.strategyFansCountApi(
                UserDataUtil.stUserId(),
                baseData?.signalStrategyId
            )
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }
            stStrategyFansCountLiveData.value = it.data ?: StStrategyFansCountBean("0", false)
        })
    }

    fun initStrategyFollow() {
        val fansData = stStrategyFansCountLiveData.value
        if (true == fansData?.watched) {
            stWatchedRelationRemoveApi()
            stStrategyFansCountLiveData.value = StStrategyFansCountBean(fansData.totalCount.mathSub("1"), false)
            EventBus.getDefault().post(DataEvent(NoticeConstants.STStrategy.NOTIFY_STRATEGY_COLLECT_COUNT, "-1"))
        } else {
            stWatchedRelationSaveApi()
            stStrategyFansCountLiveData.value = StStrategyFansCountBean(fansData?.totalCount.mathAdd("1"), true)
            EventBus.getDefault().post(DataEvent(NoticeConstants.STStrategy.NOTIFY_STRATEGY_COLLECT_COUNT, "1"))
        }
    }

    fun stTradePositionCancel(orderData: ShareOrderData) {
        val requestBody = hashMapOf<String, Any?>().apply {
            this["orderId"] = orderData.stOrder ?: ""
            this["portfolioId"] = baseData?.portfolioId ?: ""
        }.json.toRequestBody("application/json".toMediaTypeOrNull())

        requestNet({
            stTradingService.tradePositionCancelApi(requestBody)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            EventBus.getDefault().post(NoticeConstants.STStrategy.CHANGE_OF_ST_COPY_TRADING_ORDERS)
            shareStrategyData?.pendingOpen?.removeAll { data ->
                data.stOrder == orderData.stOrder
            }

            stTradePositionCancelLiveData.value = System.currentTimeMillis()
        }, isShowDialog = true)
    }

    /**
     * 收藏策略
     */
    private fun stWatchedRelationSaveApi() {
        val map = hashMapOf<String, Any?>()
        map["stUserId"] = UserDataUtil.stUserId()
        map["strategyId"] = baseData?.signalStrategyId ?: ""
        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({
            stTradingService.watchedRelationSaveApi(requestBody)
        }, {})
    }

    /**
     * 取消收藏
     */
    private fun stWatchedRelationRemoveApi() {
        val map = hashMapOf<String, Any?>()
        map["stUserId"] = UserDataUtil.stUserId()
        map["strategyId"] = baseData?.signalStrategyId ?: ""
        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())

        requestNet({
            stTradingService.watchedRelationRemoveApi(requestBody)
        }, {

        })
    }

    /**
     * 用户系统设置
     */
    fun userSetItemsetApi(value: Int, code: String) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        paramMap["code"] = code
        paramMap["value"] = value

        requestNet({
            baseService.usersetItemsetApi(paramMap)
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }
            if (code == Constants.KEY_FAST_CLOSE) {
                UserDataUtil.setFastCloseState("1")
            } else if (code == Constants.KEY_FAST_CLOSE_ST) {
                UserDataUtil.setFastStopCopyState("1")
            }
        })
    }

    private fun sensorsCopyTradingStopCopyResult(strategyName: String, strategyId: String, isSuccess: Boolean) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.SUBMIT_RESULT, if (isSuccess) "success" else "failure")
        properties.put(SensorsConstant.Key.TARGET_NAME, strategyName)
        properties.put(SensorsConstant.Key.STRATEGY_ID, strategyId)
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGSTOPCOPY_RESULT, properties)
    }
}