package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.ActivityTradeSettingBinding
import cn.com.vau.page.user.leverage.LeverageActivity
import cn.com.vau.page.user.leverage.st.StLeverageActivity
import cn.com.vau.trade.model.TradeSettingViewModel
import cn.com.vau.util.*

/**
 * 交易设置
 */
class TradeSettingActivity : BaseMvvmActivity<ActivityTradeSettingBinding, TradeSettingViewModel>() {

    private val leverage: String? by lazy { SpManager.getLeverageTrade()}

    private val bottomTipPopup: BottomSelectPopup? by lazy { BottomSelectPopup.build(this) }

    @SuppressLint("SetTextI18n")
    override fun initView() {

        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        // v3.54.0 下单页优化需求 by LyuPin
        // 原先是快速操作（不需要二次确认）的开关，开代表不需要二次确认，关代表需要确认。现在全部改成需要二次确认的开关，开代表需要二次确认，关代表不需要二次确认。
        // 交易订单确认
        mViewModel.orderConfirmLiveData.value = TextUtils.equals("1", UserDataUtil.orderConfirmState()).not()

        // 快速平仓
        mViewModel.fastCloseLiveData.value = TextUtils.equals("1", UserDataUtil.fastCloseState()).not()

        // 快速停止跟单
        mBinding.groupQuickStopCopy.isVisible= UserDataUtil.isStLogin()
        mViewModel.fastStopCopyLiveData.value = TextUtils.equals("1", UserDataUtil.fastStopCopyState()).not()

        // 杠杆
        mBinding.tvLeverage.text = "${leverage}:1"
        mBinding.tvLeverageTitle.setOnClickListener {
            if (UserDataUtil.isLiveAccount()) {// 真实账户
                openActivity(LeverageActivity::class.java,Bundle().apply {
                    putString("accountCd", UserDataUtil.accountCd())
                    putString("accountServer", UserDataUtil.serverId())
                })
            } else if (UserDataUtil.isStLogin()) {// 跟单账户
                openActivity(StLeverageActivity::class.java,Bundle().apply {
                    putString("accountCd", UserDataUtil.accountCd())
                    putString("accountServer", UserDataUtil.serverId())
                })
            } else {
                ToastUtil.showToast(getString(R.string.this_function_is_accounts))
            }
        }

        mBinding.tvTradeConfirmation.setOnClickListener {
            val tipsAdapter = PlatAdapter()
            val tips = arrayListOf<HintLocalData>()
            tips.add(HintLocalData(getString(R.string.trade_confirmation), getString(R.string.if_this_function_confirmation_trading_operation)))
            tipsAdapter.setList(tips)
            bottomTipPopup?.setAdapter(tipsAdapter)
            bottomTipPopup?.show()
        }

    }

    override fun initListener() {
        super.initListener()
        // 自主交易
        mBinding.sbOrderConfirm.setOnLoadingListener {
            mViewModel.setOrderConfirmation(mViewModel.orderConfirmLiveData.value == true)
        }
        // 平仓确认
        mBinding.sbQuickClose.setOnLoadingListener {
            mViewModel.setFastClose(mViewModel.fastCloseLiveData.value == true)
        }
        // 停止跟单确认
        mBinding.sbQuickStopCopy.setOnLoadingListener {
            mViewModel.setFastStopCopy(mViewModel.fastStopCopyLiveData.value == true)
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.orderConfirmLiveData.observe(this){
            // 快速平仓
            mBinding.sbOrderConfirm.setState(it)
        }
        mViewModel.fastCloseLiveData.observe(this){
            // 快速平仓
            mBinding.sbQuickClose.setState(it)
        }
        mViewModel.fastStopCopyLiveData.observe(this){
            // 快速停止跟单
            mBinding.sbQuickStopCopy.setState(it)
        }
    }

    companion object {
        fun startActivity(activity: Activity) {
            val intent = Intent(activity, TradeSettingActivity::class.java)
            activity.startActivity(intent)
        }
    }

}