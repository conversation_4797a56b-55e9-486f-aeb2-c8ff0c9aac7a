package cn.com.vau.trade.st.model

import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.StShareStrategyData
import cn.com.vau.data.strategy.StStrategyCopySettingsData
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.util.*
import com.google.gson.JsonObject
import kotlinx.coroutines.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

class StStrategyAddOrRemoveViewModel : BaseViewModel() {

    val fragmentList = arrayListOf<Fragment>()

    var baseDataLiveData = MutableLiveData<StrategyOrderBaseData>()

    var stStrategyCopySettingsLiveData = MutableLiveData<StStrategyCopySettingsData?>()

    var stTradeUpdateAllocationLiveData = MutableLiveData<String>()

    var shareStrategyData: StShareStrategyData? = null
    var calcStrategyData: StShareStrategyData? = null  // Copy计算用

    var minAllocatedMoney = 50.00
    var maxAllocatedMoney = 100000.00

    var isAdd = true

    fun initShareFollowStrategyData() {

        shareStrategyData = VAUSdkUtil.stShareStrategyList().firstOrNull {
            baseDataLiveData.value?.signalStrategyId == it.strategyId
        }
        calcStrategyData = shareStrategyData?.copy()
    }

    // 跟随设置
    fun stStrategyCopySettingsApi() {
        requestNet({stTradingService.strategyCopySettingsApi("Following", baseDataLiveData.value?.portfolioId.ifNull())},{
            if(!it.isSuccess()){
                ToastUtil.showToast(it.getResponseMsg())
                stStrategyCopySettingsLiveData.value = null
                return@requestNet
            }
            stStrategyCopySettingsLiveData.value = it.data
        }, onError = {
            stStrategyCopySettingsLiveData.value = null
        })
    }

    // 增加/减少 资金
    fun stTradeUpdateAllocationApi(submitMoney: String) {

        if (isAdd && submitMoney.mathCompTo("$maxAllocatedMoney") == 1) {
            // 可用余额不足
            ToastUtil.showToast(VauApplication.context.getString(R.string.free_margin_is_not_enough))
            return
        }

        val jsonObject = JsonObject()
        jsonObject.addProperty("addedAmount", submitMoney)
        jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
        // 投资组合id
        jsonObject.addProperty("portfolioId", shareStrategyData?.portfolioId ?: "")
        // 信号源account Id
        jsonObject.addProperty("signalAccountId", shareStrategyData?.strategyId)
        jsonObject.addProperty("type", if (isAdd) "DEPOSIT" else "WITHDRAW")
        jsonObject.addProperty("copyOpenTrades", 0)
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        val dealLog =
            "copy trader:${shareStrategyData?.strategyName} ${if (isAdd) "add" else "remove"}:${submitMoney}"

        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            dealLog, if (isAdd) "add" else "remove", startTimeMillis
        )

        requestNet({ stTradingService.tradeUpdateAllocationApi(requestBody) },{
            if(!it.isSuccess()){
                ToastUtil.showToast(it.getResponseMsg())
                DealLogUtil.saveFailedDealLog(
                    dealLog, it.getResponseCode(), if (isAdd) "add" else "remove", startTimeMillis
                )
                return@requestNet
            }

            EventBus.getDefault().post(NoticeConstants.STStrategy.CHANGE_OF_ST_COPY_TRADING_ORDERS)

            viewModelScope.launch(Dispatchers.Main) {
                delay(1000)
                hideLoading()
                stTradeUpdateAllocationLiveData.value = System.currentTimeMillis().toString()
            }

            DealLogUtil.saveSuccessDealLog(dealLog, if (isAdd) "add" else "remove", startTimeMillis)
        }, onError = {
            DealLogUtil.saveFailedDealLog(dealLog, "-1", if (isAdd) "add" else "remove", startTimeMillis)
        }, isShowDialog = true)
    }
}