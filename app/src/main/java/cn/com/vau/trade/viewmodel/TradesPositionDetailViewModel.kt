package cn.com.vau.trade.viewmodel

import androidx.lifecycle.viewModelScope
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.util.DealLogUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathAdd
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathMul
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.tracking.SensorsHelper
import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject

/**
 * Created by array on 2025/6/9 16:20
 * Desc:
 */
class TradesPositionDetailViewModel : BaseViewModel() {

    var orderId: String? = null
    var orderBean: ShareOrderData? = null

    /**
     * 按钮loading结束通知
     */
    private val _stopLoadingFlow = MutableSharedFlow<Boolean>()
    val stopLoadingFlow: SharedFlow<Boolean> = _stopLoadingFlow.asSharedFlow()

    /**
     * 平仓成功
     */
    private val _closeSuccessFlow = MutableSharedFlow<ShareOrderData>()
    val closeSuccessFlow: SharedFlow<ShareOrderData> = _closeSuccessFlow.asSharedFlow()

    /**
     * 平仓波动较大
     */
    private val _closeDelayFlow = MutableSharedFlow<ShareOrderData>()
    val closeDelayFlow: SharedFlow<ShareOrderData> = _closeDelayFlow.asSharedFlow()

    /**
     * 提示
     */
    private val _closeHintFlow = MutableSharedFlow<String>()
    val closeHintFlow: SharedFlow<String> = _closeHintFlow.asSharedFlow()

    /**
     * 移除定时器监听
     */
    private val _removeCallbackFlow = MutableSharedFlow<Boolean>()
    val removeCallbackFlow: SharedFlow<Boolean> = _removeCallbackFlow.asSharedFlow()

    /**
     * 仓位不存在
     */
    private val _notExistFlow = MutableSharedFlow<Boolean>()
    val notExistFlow: SharedFlow<Boolean> = _notExistFlow.asSharedFlow()
    private var checkExistJob: Job? = null

    fun findOrder(orderId: String?): ShareOrderData? {
        return when {
            orderId.isNullOrEmpty() -> null
            else -> VAUSdkUtil.shareOrderList().firstOrNull {
                it.order == orderId
            }
        }
    }

    fun removeCallback() {
        viewModelScope.launch {
            _removeCallbackFlow.emit(true)
        }
    }


    fun stopCloseLoading() {
        viewModelScope.launch {
            _stopLoadingFlow.emit(true)
        }
    }

    fun checkPositionExist() {
        checkExistJob?.cancel()
        checkExistJob = viewModelScope.launch {
            val notExist: Boolean = withContext(Dispatchers.Default) {
                null == findOrder(orderId)
            }
            _notExistFlow.emit(notExist)
        }
    }

    fun tradeOrdersClose(orderBean: ShareOrderData, checkDelay: Int) {

        val jsonObject = JsonObject()
        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("order", orderBean.order)
        var volume = orderBean.volume.mathMul(
            if (UserDataUtil.isMT5()) "10000" else "100"
        )
        if (volume.contains("."))
            volume = volume.split(".")[0]
        jsonObject.addProperty("volume", volume)
        jsonObject.addProperty("maxOffset", "*********")
        jsonObject.addProperty("symbol", orderBean.symbol)
        jsonObject.addProperty("cmd", orderBean.cmd)
        jsonObject.addProperty("lasttime", orderBean.lasttime)
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("price", orderBean.closePrice ?: "")
        jsonObject.addProperty("st", VAUSdkUtil.serverTimeMillis)
        jsonObject.addProperty("checkDelay", checkDelay)
        val startTimeMillisOrdersClose = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "close order:#${orderBean.order}  volume:${orderBean.volume}",
            startTimeMillisOrdersClose
        )
        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet(
            { tradingService.tradeOrdersCloseApi(requestBody) },
            onSuccess = {
                stopCloseLoading()
                if (it?.code == "********") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        it.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    tradeAccountLogin()
                    ToastUtil.showToast(it.info)
                }
                // 价格波动较大
                if (it?.code == "********") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        it.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    viewModelScope.launch {
                        _closeDelayFlow.emit(orderBean)
                    }
                    return@requestNet
                }
                if (it?.code == "********") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        it.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    viewModelScope.launch {
                        _closeHintFlow.emit(it.info ?: "")
                    }
                    return@requestNet
                }
                if (it?.code != "200") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        it?.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    ToastUtil.showToast(it?.info)
                    return@requestNet
                }
                DealLogUtil.saveSuccessDealLog(
                    "close order:#${orderBean.order}",
                    "close",
                    startTimeMillisOrdersClose
                )
                handleBalance(orderBean)
                viewModelScope.launch {
                    _closeSuccessFlow.emit(orderBean)
                }
            },
            onError = {
                stopCloseLoading()
            }
        )

        // 神策自定义埋点(v3500)
        sensorsTrack(orderBean)
    }

    private fun tradeAccountLogin() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("password", UserDataUtil.mt4PWD())
        jsonObject.addProperty("token", UserDataUtil.loginToken())
        jsonObject.addProperty("accountType", Integer.valueOf(UserDataUtil.mt4State()) - 1)

        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())

        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet(
            { tradingService.tradeAccountLogin(requestBody) },
            onSuccess = {
                if ("200" != it.getResponseCode()) {
                    return@requestNet
                }
                UserDataUtil.setTradeToken(it.data?.token.ifNull())
            }
        )

    }

    /**
     * 手动处理余额，暂时解决平仓瞬间净值显示问题
     */
    private fun handleBalance(data: ShareOrderData?) {
        if (UserDataUtil.isStLogin()) {
            VAUSdkUtil.stShareAccountBean().balance = VAUSdkUtil.stShareAccountBean().balance.mathAdd(data?.profit ?: 0)
            return
        }
        VAUSdkUtil.shareAccountBean().balance = VAUSdkUtil.shareAccountBean().balance.mathAdd(data?.profit ?: 0)
    }

    /**
     * 跟单，闪电平仓
     */
    fun stTradePositionClose(orderBean: ShareOrderData) {

        val jsonObject = JsonObject().apply {
            addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
            addProperty("positionId", orderBean.stOrder ?: "")
            addProperty("volume", orderBean.volume ?: "")
        }
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet(
            { stTradingService.stTradePositionCloseApi(requestBody) },
            onSuccess = {
                stopCloseLoading()
                if (it?.code != "200") {
                    ToastUtil.showToast(it?.msg)
                    return@requestNet
                }
                handleBalance(orderBean)
                viewModelScope.launch {
                    _closeSuccessFlow.emit(orderBean)
                }
            },
            onError = {
                stopCloseLoading()
            }
        )

        // 神策自定义埋点(v3500)
        sensorsTrack(orderBean)
    }

    /**
     * 删除历史订单
     */
    fun deletePastOrder(orderBean: ShareOrderData) {
        VAUSdkUtil.shareOrderList().removeAll {
            if (UserDataUtil.isStLogin()) {
                it?.stOrder == orderBean.stOrder
            } else {
                it?.order == orderBean.order
            }
        }
    }

    /**
     * 神策自定义埋点(v3500)
     */
    private fun sensorsTrack(orderBean: ShareOrderData) {
        val isBuy = OrderUtil.isBuyOfOrder(orderBean.cmd)
        // 交易详情页按钮点击 -> 交易详情页点击关闭平仓按钮时触发
        SensorsDataUtil.track(SensorsConstant.V3500.TRADE_CLOSE_SUBMIT, JSONObject().apply {
            // 交易类型
            put(SensorsConstant.Key.TRADE_TYPE, SensorsHelper.getTradeType())
            // 交易产品组
            put(SensorsConstant.Key.PRODUCT_GROUP, "")
            // 交易产品
            put(SensorsConstant.Key.PRODUCT_SYMBOL, orderBean.symbol.ifNull())
            // 交易方向
            put(SensorsConstant.Key.TRADE_DIRECTION, if (isBuy) "Buy" else "Sell")
            // 按钮名称
            put(SensorsConstant.Key.BUTTON_NAME, "Close")
            // 订单ID
            put(SensorsConstant.Key.ORDER_ID, orderBean.order.ifNull())
            // 是否选择止盈
            put(SensorsConstant.Key.IS_PROFIT, if (orderBean.takeProfit.mathCompTo("0") == 1) 1 else 0)
            // 是否选择止损
            put(SensorsConstant.Key.IS_LOSS, if (orderBean.stopLoss.mathCompTo("0") == 1) 1 else 0)
            // 交易方式
            put(SensorsConstant.Key.TRADE_MODE, "")
            // 账户币种
            put(SensorsConstant.Key.ACCOUNT_CURRENCY, UserDataUtil.currencyType())
        })
    }

}
