package cn.com.vau.trade.model

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * 交易设置
 */
class TradeSettingViewModel : BaseViewModel() {

    val orderConfirmLiveData = MutableLiveData<Boolean>()
    val fastCloseLiveData = MutableLiveData<Boolean>()
    val fastStopCopyLiveData = MutableLiveData<Boolean>()

    /**
     * 快速平仓
     */
    fun setFastClose(checked: Boolean) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        // 设置项代码，参考用户设置代码表
        paramMap["code"] = Constants.KEY_FAST_CLOSE
        paramMap["value"] = if (checked) 1 else 0
        requestNet({ baseService.usersetItemsetApi(paramMap) }, {
            if (it.isSuccess()) {
                UserDataUtil.setFastCloseState(if (checked) "1" else "0")
                fastCloseLiveData.value = checked.not()
                // 埋点
                sensorsData("Close Confirmation", checked)
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        })
    }

    /**
     * 快速停止跟单
     */
    fun setFastStopCopy(checked: Boolean) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        // 快速停止跟单
        paramMap["code"] = Constants.KEY_FAST_CLOSE_ST
        paramMap["value"] = if (checked) 1 else 0
        requestNet({ baseService.usersetItemsetApi(paramMap) }, {
            if (it.isSuccess()) {
                UserDataUtil.setFastStopCopyState(if (checked) "1" else "0")
                fastStopCopyLiveData.value = checked.not()
                // 埋点
                sensorsData("Stop copy Confirmation", checked)
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        })
    }

    /**
     * 设置订单确认
     */
    fun setOrderConfirmation(checked: Boolean) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        // 订单确认key
        paramMap["code"] = Constants.KEY_ORDER_CONFIRM
        paramMap["value"] = if (checked) 1 else 0
        requestNet({ baseService.usersetItemsetApi(paramMap) }, {
            if (it.isSuccess()) {
                UserDataUtil.setOrderConfirmState(if (checked) "1" else "0")
                orderConfirmLiveData.value = checked.not()
                // 埋点
                sensorsData("Order Confirmation", checked)
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        })
    }

    private fun sensorsData(type: String, checked: Boolean) {
        // 埋点
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_SETTING_CONFIRMATION_SWITCH_CLICK, JSONObject().apply {
            put(SensorsConstant.Key.FIELD_NAME, type)
            put(SensorsConstant.Key.SWITCH_STATUS, if (checked) "Close" else "Open")
        })
    }

}