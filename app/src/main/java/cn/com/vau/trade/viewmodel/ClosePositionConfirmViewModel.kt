package cn.com.vau.trade.viewmodel

import androidx.annotation.Keep
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

class ClosePositionConfirmViewModel : BaseViewModel() {
    var closePositionSuccessLiveData = MutableLiveData<Long>()
    var tradeOrdersCloseHintLiveData = MutableLiveData<BaseBean?>()
    var tradeOrdersCloseCheckDelayLiveData = MutableLiveData<BaseBean?>()

    var closePositionConfirmData: ClosePositionConfirmData? = null
    var orderData: ShareOrderData? = null
    var isShowNotAgain = false

    fun getCurrentPrice(): String {
        return orderData?.closePrice.numFormat(orderData?.digits?:2).addComma(orderData?.digits?:2)
    }

    fun getProfit(orderData: ShareOrderData, volume: String): String {
        val profit = ("${orderData.profit}").mathMul(volume).mathDiv(orderData.volume, 8).numCurrencyFormat()
        return profit
    }

    fun stTradePositionClose() {

        val closeVolume = closePositionConfirmData?.closeVolume ?: return
        val jsonObject = JsonObject().apply {
            addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
            addProperty("positionId", orderData?.stOrder ?: "")
            addProperty("volume", closeVolume)
        }
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet(
            { stTradingService.stTradePositionCloseApi(requestBody) },
            onSuccess = {
                if ("200" != it?.code) {
                    ToastUtil.showToast(it?.msg)
                    return@requestNet
                }
                handleClosePosition()
                closePositionSuccessLiveData.value = System.currentTimeMillis()
            },
            isShowDialog = true
        )

        // 神策自定义埋点(v3500)
        sensorsTrack()
    }

    fun tradeOrdersClose(checkDelay: Int = 1) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("order", orderData?.order)
        val closeVolume = closePositionConfirmData?.closeVolume ?: return
        var handleCount = closeVolume.mathMul(
            if (UserDataUtil.isMT5()) "10000" else "100"
        ) // 手数  *100以后
        if (handleCount.contains("."))
            handleCount = handleCount.split(".")[0]
        jsonObject.addProperty("volume", handleCount)
        jsonObject.addProperty("maxOffset", *********)
        jsonObject.addProperty("symbol", orderData?.symbol)
        jsonObject.addProperty("cmd", orderData?.cmd)
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("price", orderData?.closePrice)
        jsonObject.addProperty("lasttime", orderData?.lasttime ?: "")

        jsonObject.addProperty("st", VAUSdkUtil.serverTimeMillis)
        jsonObject.addProperty("checkDelay", checkDelay)

        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", jsonObject.toString())
        val startTimeMillisOrdersClose = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "close order:${orderData?.order}  " +
                    "volume:#${closeVolume}",
            startTimeMillisOrdersClose
        )

        val requestBody =
            jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet(
            { tradingService.tradeOrdersCloseApi(requestBody) },
            onSuccess = {
                if ("********" == it?.code ) {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderData?.order}",
                        it.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    tradeAccountLogin()
                    ToastUtil.showToast(it.info)
                    return@requestNet
                }
                // 价格波动较大
                if ("********" == it?.code ) {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderData?.order}",
                        it.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    tradeOrdersCloseCheckDelayLiveData.value = it
                    return@requestNet
                }
                if ("********" == it?.code ) {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderData?.order}",
                        it.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    tradeOrdersCloseHintLiveData.value = it
                    return@requestNet
                }
                if ("200" != it?.code ) {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderData?.order}",
                        it?.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    ToastUtil.showToast(it?.info)
                    return@requestNet
                }
                DealLogUtil.saveSuccessDealLog(
                    "close order:#${orderData?.order}",
                    "close",
                    startTimeMillisOrdersClose
                )

                closePositionSuccessLiveData.value = System.currentTimeMillis()
                handleClosePosition()
            }, isShowDialog = true
        )
    }

    private fun tradeAccountLogin() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("password", UserDataUtil.mt4PWD())
        jsonObject.addProperty("token", UserDataUtil.loginToken())
        jsonObject.addProperty("accountType", Integer.valueOf(UserDataUtil.mt4State()) - 1)

        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())

        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet(
            { tradingService.tradeAccountLogin(requestBody) },
            onSuccess = {
                if ("200" != it.getResponseCode() ) {
                    return@requestNet
                }
                UserDataUtil.setTradeToken(it.data?.token.ifNull())
            }
        )

    }

    private fun handleClosePosition() {
        if ((closePositionConfirmData?.closeVolume ?: "0") == (orderData?.volume ?: "0")) {
            VAUSdkUtil.shareOrderList().removeAll {
                it.order == orderData?.order
            }
            handleBalance()
        } else {
            VAUSdkUtil.shareOrderList().firstOrNull {
                it.order == orderData?.order
            }?.volume = orderData?.volume.mathSub(closePositionConfirmData?.closeVolume)
        }

        EventBus.getDefault().post(NoticeConstants.REFRESH_ORDER_DATA_SHARE)
    }

    /**
     *手动处理余额，暂时解决平仓瞬间净值显示问题
     */
    private fun handleBalance() {
        if (UserDataUtil.isStLogin()) {
            VAUSdkUtil.stShareAccountBean().balance =  VAUSdkUtil.stShareAccountBean().balance.mathAdd(orderData?.profit?:0)
            return
        }
        VAUSdkUtil.shareAccountBean().balance = VAUSdkUtil.shareAccountBean().balance.mathAdd(orderData?.profit?:0)
    }

    // 快速平仓
    fun usersetItemset(value: Int) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        paramMap["code"] = Constants.KEY_FAST_CLOSE
        paramMap["value"] = value
        requestNet(
            { baseService.usersetItemsetApi(paramMap) },
            onSuccess = {
                if (!it.isSuccess()) return@requestNet
                UserDataUtil.setFastCloseState("1")
            }
        )
    }

    /**
     * 神策自定义埋点(v3500)
     */
    private fun sensorsTrack() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TRADE_TYPE, SensorsHelper.getTradeType()) // 交易类型
        properties.put(SensorsConstant.Key.PRODUCT_GROUP, "") // 交易产品组
        properties.put(SensorsConstant.Key.PRODUCT_SYMBOL, orderData?.symbol.ifNull()) // 交易产品
        val isBuy = OrderUtil.isBuyOfOrder(orderData?.cmd)
        properties.put(SensorsConstant.Key.TRADE_DIRECTION, if (isBuy) "Buy" else "Sell") // 交易方向
        properties.put(SensorsConstant.Key.BUTTON_NAME, "Partially Close") // 按钮名称
        properties.put(SensorsConstant.Key.ORDER_ID, orderData?.order.ifNull()) // 订单ID
        properties.put(
            SensorsConstant.Key.IS_PROFIT, if (orderData?.takeProfit.mathCompTo("0") == 1) 1 else 0
        ) // 是否选择止盈
        properties.put(
            SensorsConstant.Key.IS_LOSS, if (orderData?.stopLoss.mathCompTo("0") == 1) 1 else 0
        ) // 是否选择止损
        properties.put(SensorsConstant.Key.TRADE_MODE, "") // 交易方式
        properties.put(SensorsConstant.Key.ACCOUNT_CURRENCY, UserDataUtil.currencyType()) // 账户币种
        // 交易详情页按钮点击 -> 交易详情页点击关闭平仓按钮时触发
        SensorsDataUtil.track(SensorsConstant.V3500.TRADE_CLOSE_SUBMIT, properties)
    }
}

@Keep
data class ClosePositionConfirmData(
    var orderData: ShareOrderData?,
    var closeVolume: String?
)