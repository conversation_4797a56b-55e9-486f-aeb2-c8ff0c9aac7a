package cn.com.vau.trade.model

import android.content.Context
import android.graphics.Paint
import android.text.TextUtils
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.base.rx.RxManager
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.UIState
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.kchart.viewbeans.BrokenLine
import cn.com.vau.common.view.kchart.viewbeans.CandleLine
import cn.com.vau.common.view.kchart.viewbeans.CandleLine.CandleLineBean
import cn.com.vau.common.view.kchart.viewbeans.HistogramView
import cn.com.vau.common.view.kchart.viewbeans.HistogramView.HistogramBean
import cn.com.vau.common.view.kchart.viewbeans.IndicatorLine
import cn.com.vau.common.view.kchart.viewbeans.MACDHistogram
import cn.com.vau.common.view.kchart.viewbeans.MACDHistogram.MACDBean
import cn.com.vau.common.view.kchart.views.ChartViewImp
import cn.com.vau.common.view.timeSelection.PickerDateUtil.longTimeToString
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.trade.KChartBean.DataBean.ChartsBean
import cn.com.vau.data.trade.KChartBean.DataBean.TimeChartBean
import cn.com.vau.data.trade.SymbolsChartData
import cn.com.vau.trade.bean.kchart.ChartPriceChange
import cn.com.vau.trade.bean.kchart.ChartTradeEmotion
import cn.com.vau.trade.kchart.ChartUIParamUtil
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.util.AppUtil.getTimeZoneRawOffsetToHour
import cn.com.vau.util.DealLogUtil.saveFailedDealLog
import cn.com.vau.util.DealLogUtil.saveStartDealLog
import cn.com.vau.util.DealLogUtil.saveSuccessDealLog
import cn.com.vau.util.GsonUtil.buildGson
import cn.com.vau.util.TimeUtil.frontDateLongWithStartDate
import cn.com.vau.util.ToastUtil.showToast
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathMul
import cn.com.vau.util.numFormat
import cn.com.vau.util.toLongCatching
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

class KLineChartViewModel : BaseViewModel() {
    // K线产品数据
    var data: ShareProductData? = null

    var intervalList = mutableListOf("Tick", "1m", "15m", "1h", "1D", "1W")
    var moreIntervalList = mutableListOf("5m", "30m", "4h", "1M")
    var selectedInterval = ""
    var isAutoRefresh = false   // 是否自动刷新(K线最后一根)
    var isTimeShare = false     // 是否是分时图
    var period: Int = 0         // K线请求的分钟数
    var isRequestEnd: Boolean = true
    var isDataEnd: Boolean = false
    private var fromTime: String? = null
    private var toTime: Long = 0

    var noDataLiveData = MutableLiveData<Boolean>()
    var tradeEmotionUiState = MutableLiveData<UIState<ChartTradeEmotion>>()
    var priceChangeData: ChartPriceChange? = null
    var priceChangeDataMore = MutableLiveData<ChartPriceChange?>()

    // 日志用参数
    private var startTimeMillisWs: Long = 0
    private var finalLogType: String? = null

    var mainChartNames = mutableListOf("MA", "BOLL", "MIKE", "BBI")
    var subChartNames = mutableListOf("VOL", "MACD", "KDJ", "RSI", "CCI", "KD")
    var chartTypeName: String = ""      // 选中主图类型
    var chartSubTypeName: String = ""   // 选中副图类型
    var chartTypeAllList = mutableListOf<Pair<Int, String>>()

    var timeShareList = arrayListOf<TimeChartBean>()
    val candleDataList = arrayListOf<CandleLineBean>()
    var shareOrderList: ArrayList<ShareOrderData> = arrayListOf()
    var shareOrderData: ShareOrderData? = null  // 当前选中的订单
    val tokenErrorLiveData by lazy {
        MutableLiveData<String>()
    }

    private val rxManager: RxManager = RxManager()


    fun initOrderData(chartView: ChartViewImp) {
        shareOrderList.clear()
        shareOrderList.addAll(VAUSdkUtil.shareOrderList().filter { it.symbol == data?.symbol })
        setOrderLine(chartView)
    }

    fun setOrderLine(mChartViewImp: ChartViewImp) {
        if (shareOrderList.isNotEmpty()) {
            var selectedOrderIndex = 0
            if (!KLineDataUtils.selectedOrderNo.equals("0")) {
                selectedOrderIndex = findIndexForOrderNo()
            } else {
                KLineDataUtils.selectedOrderNo = shareOrderList[0].order
            }
            shareOrderData = shareOrderList[selectedOrderIndex]
        } else {
            shareOrderData = null
        }
        // 设置订单View
        val positionLine = mChartViewImp.positionLine
        positionLine.orderData = shareOrderData
        positionLine.setLineDashPath(ChartUIParamUtil.commonDashEffect)
        positionLine.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums

        val movableLine = mChartViewImp.movableLine
        movableLine.setOrderData(shareOrderData)
        mChartViewImp.setSymbolData(data)
        movableLine.setLineDashPath(ChartUIParamUtil.commonDashEffect)
        movableLine.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums

        val takeProfitLine = mChartViewImp.takeProfitLine
        takeProfitLine.orderData = shareOrderData
        takeProfitLine.setLineDashPath(ChartUIParamUtil.commonDashEffect)
        takeProfitLine.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums

        val stopLossLine = mChartViewImp.stopLossLine
        stopLossLine.orderData = shareOrderData
        stopLossLine.setLineDashPath(ChartUIParamUtil.commonDashEffect)
        stopLossLine.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums

        positionLine.setShow(KLineDataUtils.userData != null && KLineDataUtils.userData.positionLineDisplay)
        movableLine.setShow(false)
        takeProfitLine.setShow(KLineDataUtils.userData != null && KLineDataUtils.userData.tpLineDisplay)
        stopLossLine.setShow(KLineDataUtils.userData != null && KLineDataUtils.userData.slLineDisplay)
    }

    fun getCandleLine(context: Context?): CandleLine? {
        context?.let {
            return CandleLine(it).apply {
                defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
                setMinShownPointNums(ChartUIParamUtil.minShownPointNums)
                setMaxShownPointNums(ChartUIParamUtil.maxShownPointNums)
                setUpColor(ChartUIParamUtil.candleUpColor)
                setDownColor(ChartUIParamUtil.candleDownColor)
                setFill(true)
            }
        }
        return null
    }

    fun getIndicatorLine(context: Context?): IndicatorLine? {
        context?.let {
            return IndicatorLine(it).apply {
                setLineColor(ChartUIParamUtil.sellIndicatorColor)
                setTextBackgroundColor(ChartUIParamUtil.sellIndicatorScaleBgColor)
                setTextColor(ChartUIParamUtil.sellIndicatorTextColor)
                setLineDashPath(ChartUIParamUtil.commonDashEffect)
                setTextGravity(Paint.Align.CENTER)
                setScaleAlign(IndicatorLine.ScaleAlign.BELOW)
                defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
            }
        }
        return null
    }

    fun getMACD0IndicatorLine(context: Context?): IndicatorLine? {
        context?.let {
            return IndicatorLine(it).apply {
                setLineColor(ContextCompat.getColor(it, R.color.transparent))
                setTextBackgroundColor(ContextCompat.getColor(it, R.color.transparent))
                setTextColor(ChartUIParamUtil.coordinateScaleColor)
                setTextSize(9f)
                setShowTextBorder(false)
                setKeepSameToCoordinateScalePosition(true)
                defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
            }
        }
        return null
    }

    fun getBuyLine(context: Context?): IndicatorLine? {
        context?.let {
            return IndicatorLine(it).apply {
                setLineColor(ChartUIParamUtil.buyIndicatorColor)
                setTextBackgroundColor(ChartUIParamUtil.buyIndicatorScaleBgColor)
                setTextColor(ChartUIParamUtil.buyIndicatorTextColor)
                setLineDashPath(ChartUIParamUtil.commonDashEffect)
                setTextGravity(Paint.Align.CENTER)
                setScaleAlign(IndicatorLine.ScaleAlign.ABOVE)
                defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
            }
        }
        return null
    }

    fun getTimeShareDataList(): ArrayList<TimeChartBean> {
        val list: ArrayList<TimeChartBean> = ArrayList<TimeChartBean>()
        val timeZone = getTimeZoneRawOffsetToHour()
        for (x in KLineDataUtils.mainList.indices) {
            val dataBean = KLineDataUtils.mainList.getOrNull(x)
            KLineDataUtils.timeShareList.add(dataBean?.close.toString())
            val timeShareBean = TimeChartBean()
            timeShareBean.close = dataBean?.close.ifNull()
            timeShareBean.timestamp = dataBean?.timestamp
            timeShareBean.mt4TimeMills = (dataBean?.timestamp?.toLongCatching().ifNull() * 1000) - (timeZone * 3600 * 1000)
            if (x == KLineDataUtils.mainList.size - 1 && data != null) {
                // 个人感觉这段代码没有意义(页面每次刷新都会去赋值)，故注释掉
//                boolean buyLineVisible = KLineDataUtils.userData != null && KLineDataUtils.userData.getAskLineDisplay();
//                if (buyLineVisible) {
//                }
                if (data?.bid != 0f) {
                    timeShareBean.close = if (data?.originalBid == 0f) data?.bid?.toDouble().ifNull() else data?.originalBid?.toDouble().ifNull()
                }
                if (data?.ask != 0f) {
                    timeShareBean.originalAsk = if (data?.originalAsk == 0f) data?.ask?.toDouble().ifNull() else data?.originalAsk?.toDouble().ifNull()
                }
            }
            list.add(timeShareBean)
        }
        return list
    }

    fun getCandleDataList(): MutableList<CandleLineBean> {
        // 线转换数据
        val list: MutableList<CandleLineBean> = ArrayList<CandleLineBean>()
        val timeZone = getTimeZoneRawOffsetToHour()
        for (x in KLineDataUtils.mainList.indices) {
            val dataBean = KLineDataUtils.mainList.getOrNull(x)
            val candleLineBean = CandleLineBean(x,
                dataBean?.high?.toFloat().ifNull(),
                dataBean?.low?.toFloat().ifNull(),
                dataBean?.open?.toFloat().ifNull(),
                dataBean?.close?.toFloat().ifNull(),
                ChartUIParamUtil.digits
            )
            candleLineBean.timeMills = dataBean?.timestamp?.toLong().ifNull() * 1000
            candleLineBean.mt4TimeMills = (dataBean?.timestamp?.toLong().ifNull() * 1000) - (timeZone * 3600 * 1000)
            if (x == KLineDataUtils.mainList.size - 1 && data != null) {
                // 个人感觉这段代码没有意义(页面每次刷新都会去赋值)，故注释掉
//                boolean buyLineVisible = KLineDataUtils.userData != null && KLineDataUtils.userData.getAskLineDisplay();
//                if (buyLineVisible) {
//                }
                if (data?.originalBid != 0f) {
                    candleLineBean.originalBid = data?.originalBid.ifNull()
                    candleLineBean.closePrice = data?.originalBid.ifNull()
                }
                if (data?.originalAsk != 0f) {
                    candleLineBean.originalAsk = data?.originalAsk.ifNull()
                }
            }
            list.add(candleLineBean)
        }
        return list
    }

    /**
     * minShowPoint 最少
     * maxShowPoint 最多
     * defaultShowPoint  默认
     * showPoint 当前显示蜡烛个数
     * drawPointIndex 最左边蜡烛下标（相对整个集合的下标）
     */
    fun getBrokenLine(context: Context?, list: MutableList<String?>, lineColor: Int): BrokenLine? {
        context ?: return null
        val brokenLine = BrokenLine(context)
        brokenLine.dataList = list
        brokenLine.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
        brokenLine.setMinShownPointNums(ChartUIParamUtil.minShownPointNums)
        brokenLine.setMaxShownPointNums(ChartUIParamUtil.maxShownPointNums)
        brokenLine.isFill = false
        brokenLine.setLineColor(lineColor)
        brokenLine.setDrawPointIndex(list.size - brokenLine.defaultShowPointNums)
        return brokenLine
    }

    // 分时
    fun getTimeBrokenLine(context: Context?): BrokenLine? {
        context ?: return null
        val brokenLine = BrokenLine(context)
        brokenLine.dataList = KLineDataUtils.timeShareList
        brokenLine.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
        brokenLine.setMinShownPointNums(ChartUIParamUtil.minShownPointNums)
        brokenLine.setMaxShownPointNums(ChartUIParamUtil.maxShownPointNums)
        brokenLine.isFill = true
        brokenLine.setDigits(ChartUIParamUtil.digits)
        brokenLine.setShowMaxPrice(true)
        brokenLine.setShowMinPrice(true)
        brokenLine.setDrawPointIndex(KLineDataUtils.timeShareList.size - brokenLine.defaultShowPointNums)
        return brokenLine
    }

    fun getHistogramDataList(): MutableList<HistogramBean> {
        val histogramList: MutableList<HistogramBean> = ArrayList<HistogramBean>()
        for (x in KLineDataUtils.mainList.indices) {
            val bean = KLineDataUtils.mainList[x]
            val histogramBean = HistogramBean(bean.open.ifNull(), bean.close.ifNull(), bean.volume.toFloat().ifNull())
            if (x == KLineDataUtils.mainList.size - 1 && data != null) {
                if (data?.originalBid != 0f) {
                    histogramBean.setClosePrice(data?.originalBid.ifNull().toDouble())
                }
            }
            histogramList.add(histogramBean)
        }
        return histogramList
    }

    // 交易量-柱状组件
    fun getHistogram(context: Context?): HistogramView? {
        context ?: return null
//        val histogramList: MutableList<HistogramBean?> = ArrayList<HistogramBean?>()
//        for (x in KLineDataUtils.mainList.indices) {
//            val bean = KLineDataUtils.mainList[x]
//            val histogramBean = HistogramBean(bean.open.ifNull(), bean.close.ifNull(), bean.volume.toFloat().ifNull())
//            if (x == KLineDataUtils.mainList.size - 1 && data != null) {
//                if (data?.originalBid != 0f) {
//                    histogramBean.setClosePrice(data?.originalBid.ifNull().toDouble())
//                }
//            }
//            histogramList.add(histogramBean)
//        }
        val histogram = HistogramView(context)
        histogram.dataList = getHistogramDataList()
        histogram.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
        histogram.setMinShownPointNums(ChartUIParamUtil.minShownPointNums)
        histogram.setMaxShownPointNums(ChartUIParamUtil.maxShownPointNums)
        histogram.setDrawPointIndex(KLineDataUtils.macdList.size - histogram.defaultShowPointNums)
        histogram.setUpColor(ChartUIParamUtil.subUpColor)
        histogram.setDownColor(ChartUIParamUtil.subDownColor)
        histogram.setFill(true)
        return histogram
    }

    fun getMacdDataList(): MutableList<MACDBean> {
        val macdBeanList: MutableList<MACDBean> = ArrayList<MACDBean>()
        for (x in KLineDataUtils.macdList.indices) {
            macdBeanList.add(MACDBean(KLineDataUtils.macdList.get(x)))
        }
        return macdBeanList
    }

    fun getMacdHistogram(context: Context?): MACDHistogram? {
        context ?: return null
//        val macdBeanList: MutableList<MACDBean?> = ArrayList<MACDBean?>()
//        for (x in KLineDataUtils.macdList.indices) {
//            macdBeanList.add(MACDBean(KLineDataUtils.macdList.get(x)))
//        }
        val macdHistogram = MACDHistogram(context)
        macdHistogram.dataList = getMacdDataList()
        macdHistogram.defaultShowPointNums = ChartUIParamUtil.defaultShowPointNums
        macdHistogram.setMinShownPointNums(ChartUIParamUtil.minShownPointNums)
        macdHistogram.setMaxShownPointNums(ChartUIParamUtil.maxShownPointNums)
        macdHistogram.setDrawPointIndex(KLineDataUtils.macdList.size - macdHistogram.getDefaultShowPointNums())
        macdHistogram.setUpColor(ChartUIParamUtil.subUpColor)
        macdHistogram.setDownColor(ChartUIParamUtil.subDownColor)
        macdHistogram.isFill = true
        return macdHistogram
    }

    fun getMainChartTitle(): String {
        var chartText = ""
        var position = ChartUIParamUtil.chartShowEndPosition
        if (!KLineDataUtils.mainList.isEmpty() && position >= KLineDataUtils.mainList.size) {
            position = KLineDataUtils.mainList.size - 1
        }
        if (KLineDataUtils.mA5List.size <= position) {
            return ""
        }
        when (chartTypeName) {
            "MA" -> if (position < KLineDataUtils.mA5List.size && position < KLineDataUtils.mA10List.size && position < KLineDataUtils.mA20List.size && position < KLineDataUtils.mA30List.size) {
                chartText = "(5,10,20,30) "
            }

            "BOLL" -> if (position < KLineDataUtils.midList.size && position < KLineDataUtils.upperList.size && position < KLineDataUtils.lowerList.size) {
                chartText = "(20,2) "
            }

            "MIKE" -> if (position < KLineDataUtils.wrList.size && position < KLineDataUtils.mrList.size && position < KLineDataUtils.srList.size && position < KLineDataUtils.wsList.size && position < KLineDataUtils.msList.size && position < KLineDataUtils.ssList.size) {
                chartText = "(12) "
            }

            "BBI" -> if (position < KLineDataUtils.bbiList.size) {
                chartText = "(3,6,12,24) "
            }
        }
        return chartText
    }

    fun getMainChartText(): String {
        var chartText = ""
        var position = ChartUIParamUtil.chartShowEndPosition
        if (position >= KLineDataUtils.mainList.size) position = KLineDataUtils.mainList.size - 1
        if (KLineDataUtils.mainList.isNotEmpty() && position > -1) {
            when (chartTypeName) {
                "MA" -> {
                    if (position < KLineDataUtils.mA5List.size && position < KLineDataUtils.mA10List.size && position < KLineDataUtils.mA20List.size && position < KLineDataUtils.mA30List.size) {
                        chartText = "<font color='#4892F3'>MA5:${KLineDataUtils.mA5List[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E2BD7B'>MA10:${KLineDataUtils.mA10List[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#AA72DB'>MA20:${KLineDataUtils.mA20List[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#4DBCCC'>MA30:${KLineDataUtils.mA30List[position].numFormat(ChartUIParamUtil.digits, false)}</font> "
//                                "<font color='#E06CB6'>MA5:${KLineDataUtils.mA60List[position].numFormat(ChartUIParamUtil.digits, false)}</font>"  需求文档和iOS都没有这个指标 所以去掉
                    }
                }
                "BOLL" -> {
                    if (position < KLineDataUtils.midList.size && position < KLineDataUtils.upperList.size && position < KLineDataUtils.lowerList.size) {
                        chartText = "<font color='#4892F3'>BOLL:${KLineDataUtils.midList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E2BD7B'>UB:${KLineDataUtils.upperList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#AA72DB'>LB:${KLineDataUtils.lowerList[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
                "MIKE" -> {
                    if (position < KLineDataUtils.wrList.size && position < KLineDataUtils.mrList.size && position < KLineDataUtils.srList.size && position < KLineDataUtils.wsList.size && position < KLineDataUtils.msList.size && position < KLineDataUtils.ssList.size) {
                        chartText = "<font color='#4892F3'>WR:${KLineDataUtils.wrList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E2BD7B'>MR:${KLineDataUtils.mrList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#AA72DB'>SR:${KLineDataUtils.srList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#4DBCCC'>WS:${KLineDataUtils.wsList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E06CB6'>MS:${KLineDataUtils.msList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#475C94'>SS:${KLineDataUtils.ssList[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
                "BBI" -> {
                    if (position < KLineDataUtils.bbiList.size) {
                        chartText = "<font color='#4892F3'>BBI:${KLineDataUtils.bbiList[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
            }
        }
        return chartText
    }

    fun getSubChartTitle(): String {
        var chartText = ""
        var position = ChartUIParamUtil.chartShowEndPosition
        if (position >= KLineDataUtils.mainList.size) {
            position = KLineDataUtils.mainList.size - 1
        }
        if (KLineDataUtils.diffList.size <= position) {
            return ""
        }
        if (!KLineDataUtils.mainList.isEmpty()) {
            when (chartSubTypeName) {
                "MACD" -> if (position < KLineDataUtils.diffList.size && position < KLineDataUtils.deaList.size && position < KLineDataUtils.macdList.size) {
                    chartText = "(12,26,9) "
                }

                "KDJ" -> if (position < KLineDataUtils.kList.size && position < KLineDataUtils.dList.size && position < KLineDataUtils.jList.size) {
                    chartText = "(9,3,3) "
                }

                "RSI" -> if (position < KLineDataUtils.rsi1List.size && position < KLineDataUtils.rsi2List.size && position < KLineDataUtils.rsi3List.size) {
                    chartText = "(6,12,24) "
                }

                "CCI" -> if (position < KLineDataUtils.cciList.size) {
                    chartText = "(14) "
                }

                "KD" -> if (position < KLineDataUtils.kList.size && position < KLineDataUtils.dList.size) {
                    chartText = "(9,3,3) "
                }
            }
        }
        return chartText
    }

    fun getSubChartText(): String {
        var chartText = ""
        var position = ChartUIParamUtil.chartShowEndPosition
        if (position >= KLineDataUtils.mainList.size) position = KLineDataUtils.mainList.size - 1
        if (KLineDataUtils.mainList.isNotEmpty() && position > -1) {
            when (chartSubTypeName) {
                "MACD" -> {
                    if (position < KLineDataUtils.diffList.size && position < KLineDataUtils.deaList.size && position < KLineDataUtils.macdList.size) {
                        chartText = "<font color='#4892F3'>DIF:${KLineDataUtils.diffList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E2BD7B'>DEA:${KLineDataUtils.deaList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#AA72DB'>MACD:${KLineDataUtils.macdList[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
                "KDJ" -> {
                    if (position < KLineDataUtils.kList.size && position < KLineDataUtils.dList.size && position < KLineDataUtils.jList.size) {
                        chartText = "<font color='#4892F3'>K:${KLineDataUtils.kList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E2BD7B'>D:${KLineDataUtils.dList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#AA72DB'>J:${KLineDataUtils.jList[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
                "RSI" -> {
                    if (position < KLineDataUtils.rsi1List.size && position < KLineDataUtils.rsi2List.size && position < KLineDataUtils.rsi3List.size) {
                        chartText = "<font color='#4892F3'>RSI6:${KLineDataUtils.rsi1List[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E2BD7B'>RSI12:${KLineDataUtils.rsi2List[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#AA72DB'>RSI24:${KLineDataUtils.rsi3List[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
                "CCI" -> {
                    if (position < KLineDataUtils.cciList.size) {
                        chartText = "<font color='#4892F3'>CCI14:${KLineDataUtils.cciList[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
                "KD" -> {
                    if (position < KLineDataUtils.kList.size && position < KLineDataUtils.dList.size) {
                        chartText = "<font color='#4892F3'>K:${KLineDataUtils.kList[position].numFormat(ChartUIParamUtil.digits, false)}</font> " +
                                "<font color='#E2BD7B'>D:${KLineDataUtils.dList[position].numFormat(ChartUIParamUtil.digits, false)}</font>"
                    }
                }
            }
        }
        return chartText
    }

    private fun findIndexForOrderNo(): Int {
        for (i in shareOrderList.indices) {
            if (shareOrderList[i].order == KLineDataUtils.selectedOrderNo) {
                return i
            }
        }
        return 0
    }

    fun positionToInterval(position: Int): String {
        return when (position) {
            0 -> "Tick"
            1 -> "1m"
            2 -> "5m"
            3 -> "15m"
            4 -> "30m"
            5 -> "1h"
            6 -> "4h"
            7 -> "1D"
            8 -> "1W"
            9 -> "1M"
            else -> "1D"
        }
    }

    fun intervalToPosition(interval: String): Int {
        return when (interval) {
            "Tick"  -> 0
            "1m"    -> 1
            "5m"    -> 2
            "15m"   -> 3
            "30m"   -> 4
            "1h"    -> 5
            "4h"    -> 6
            "1D"    -> 7
            "1W"    -> 8
            "1M"    -> 9
            else -> 7
        }
    }

    // 点击图表类型recyclerview后：滑动recyclerview 切换chartview类型
    fun switchChartPeriod(symbol: String, isShowLoading: Boolean, isSwitchingSymbol: Boolean = false) {
        when (selectedInterval) {
            "Tick"  -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 0)
            "1m"    -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 1)
            "5m"    -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 5)
            "15m"   -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 15)
            "30m"   -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 30)
            "1h"    -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 60)
            "4h"    -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 240)
            "1D"    -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 1440)
            "1W"    -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 10080)
            "1M"    -> symbolsChartPeriod(symbol, isShowLoading, isSwitchingSymbol, 43200)
        }
    }

    // 设置周期，联网请求数据
    private fun symbolsChartPeriod(symbol: String, isShowLoading: Boolean, isSwitchingSymbol: Boolean = false, period: Int) {
        isTimeShare = period == 0
        this.period = if (period == 0) 1 else period
        isAutoRefresh = false
        symbolsChart(symbol, isShowLoading, isSwitchingSymbol, selectedInterval, false)
    }

    // 请求蜡烛图数据 （3000耗时10秒左右）  进入页面联网，切换1分，5分联网，每隔1分钟联网看是否有新数据
    fun symbolsChart(symbol: String, isShowLoading: Boolean, isSwitchingSymbol: Boolean = false, interval: String, isLoadMore: Boolean) {
        if (isShowLoading) {
            showLoading()
        }
        isRequestEnd = false
        val isSTAccount = UserDataUtil.isStLogin()
        //isSTAccount = false;
        val jsonMap = HashMap<String?, String?>()
        jsonMap.put("symbol", symbol)
        jsonMap.put("period", "$period")

        if (KLineDataUtils.mainList != null && isLoadMore) {
            toTime = KLineDataUtils.mainList.getOrNull(0)?.timestamp?.toLong().ifNull()
        } else {
            isDataEnd = false
            toTime = (System.currentTimeMillis() / 1000 + Constants.season * 60 * 60L)
        }

        var logType = ""
        when (period) {
            1 -> logType = "1m"
            5 -> logType = "5m"
            15 -> logType = "15m"
            30 -> logType = "30m"
            60 -> logType = "1h"
            240 -> logType = "4h"
            1440 -> logType = "1D"
            10080 -> logType = "1W"
            43200 -> logType = "1M"
        }
        startTimeMillisWs = System.currentTimeMillis()

        finalLogType = logType

        if (isSTAccount) {
            stChartHistory(symbol, interval, jsonMap, isLoadMore, isSwitchingSymbol)
        } else {
//            LogUtil.d("wj", "请求K线数据: ---");
            chartHistory(symbol, interval, jsonMap, isLoadMore, isSwitchingSymbol)
        }
    }

    /**
     * 跟单的k线请求
     *
     * @param position
     * @param jsonMap
     */
    private fun stChartHistory(symbol: String, interval: String, jsonMap: java.util.HashMap<String?, String?>, isLoadMore: Boolean, isSwitchingSymbol: Boolean = false) {
        // 跟单账户，第一次不传，第二次才传
        if (isLoadMore) {
            jsonMap.put("to", toTime.toString() + "")
        }

        jsonMap.put("size", "300")

        saveStartDealLog(
            "symbol:" + symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                toTime.toString() + "000",
                "yyyy-MM-dd HH:mm:ss"
            ), "k-line", startTimeMillisWs
        )

        // 无法用新网络框架，因为数据模型不规范，不符合框架要求的数据泛型
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(jsonMap).toString())
        HttpUtils.loadData<SymbolsChartData>(RetrofitHelper.getStHttpService().historyGetKLineMts(requestBody), object : BaseObserver<SymbolsChartData>() {
            override fun onHandleSubscribe(d: Disposable) {
                rxManager.add(d)
            }

            override fun onNext(baseData: SymbolsChartData) {
                setKChartData(symbol, baseData, interval, true, isLoadMore, isSwitchingSymbol)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                if (isSwitchingSymbol) {
                    sendEvent(NoticeConstants.SEND_EVENT_TAG_SWITCH_SYMBOL_COMPLETE)
                }
                hideLoading()
                if (!isLoadMore) {
                    // 展示缺省图
                    noDataLiveData.value = true
                }
                saveFailedDealLog(
                    "symbol:" + symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                        toTime.toString() + "000",
                        "yyyy-MM-dd HH:mm:ss"
                    ), "-1", "k-line", startTimeMillisWs
                )
            }
        })
    }

    /**
     * 非跟单k线请求
     *
     * @param position
     * @param jsonMap
     */
    private fun chartHistory(symbol: String, interval: String, jsonMap: HashMap<String?, String?>, isLoadMore: Boolean, isSwitchingSymbol: Boolean = false) {
        // mt5 第一次不传 to
        if (UserDataUtil.isMT5()) {
            if (isLoadMore) {
                jsonMap.put("to", toTime.toString() + "")
            }
            jsonMap.put("size", "300")
        } else {
            jsonMap.put("to", toTime.toString() + "")
            //如果不是跟单账户，照旧， 跟单账户不需要传
            if (isAutoRefresh) {
                if (getLastestChecked() != null) {
                    fromTime = getLastestChecked()?.timestamp.ifNull()
                    jsonMap.put("from", getLastestChecked()?.timestamp.ifNull())
                }
            } else {
                fromTime = frontDateLongWithStartDate(period, toTime).toString() + ""
                jsonMap.put("from", fromTime)
            }
        }

        if (!TextUtils.isEmpty(UserDataUtil.accountCd()) && !UserDataUtil.isRebateAccount()) {
            jsonMap.put("server", UserDataUtil.serverId())
        } else {
            jsonMap.put("server", "")
        }
        jsonMap.put("type", "1")
        if (UserDataUtil.isLogin()) {
            jsonMap.put("login", UserDataUtil.accountCd())
        }

        saveStartDealLog(
            "symbol:" + symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                toTime.toString() + "000",
                "yyyy-MM-dd HH:mm:ss"
            ), "k-line", startTimeMillisWs
        )

        // 无法用新网络框架，因为数据模型不规范，不符合框架要求的数据泛型
        val bodyMap = HashMap<String?, String?>()
        bodyMap.put("data", buildGson().toJson(jsonMap))
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(bodyMap).toString())
        HttpUtils.loadData<SymbolsChartData>(RetrofitHelper.getHttpService2().tradeOrderHistoryMarketsApi(requestBody), object : BaseObserver<SymbolsChartData>() {
            override fun onHandleSubscribe(d: Disposable) {
                rxManager.add(d)
            }

            override fun onNext(baseData: SymbolsChartData) {
//                LogUtil.d("wj", "接口返回: ");
                setKChartData(symbol, baseData, interval, false, isLoadMore, isSwitchingSymbol)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                hideLoading()
                if (isSwitchingSymbol) {
                    sendEvent(NoticeConstants.SEND_EVENT_TAG_SWITCH_SYMBOL_COMPLETE)
                }
                if (!isLoadMore) {
                    // 展示缺省图
                    noDataLiveData.value = true
                }
                saveFailedDealLog(
                    "symbol:" + symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                        toTime.toString() + "000",
                        "yyyy-MM-dd HH:mm:ss"
                    ), "-1", "k-line", startTimeMillisWs
                )
            }
        })
    }

    private fun setKChartData(symbol: String, baseData: SymbolsChartData, interval: String, isSTAccount: Boolean, isLoadMore: Boolean, isSwitchingSymbol: Boolean = false) {
        //baseData.getData() 这个后台返回值存在null的情况，会造成app闪退，因此加了特殊处理
        val isStLogin = UserDataUtil.isStLogin()
        val isUnAvailable = if (isStLogin) baseData.data == null else baseData.obj == null
        isRequestEnd = true
        hideLoading()
        if ("200" != baseData.code || isUnAvailable) {
            if (isSwitchingSymbol) {
                sendEvent(NoticeConstants.SEND_EVENT_TAG_SWITCH_SYMBOL_COMPLETE)
            }
            isAutoRefresh = false
            if (!isLoadMore) {
                // 展示缺省图
                noDataLiveData.value = true
            }
            saveFailedDealLog(
                "symbol:" + symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                    toTime.toString() + "000",
                    "yyyy-MM-dd HH:mm:ss"
                ), baseData.code + "", "k-line", startTimeMillisWs
            )
            return
        }

        val chartsBeanList = mutableListOf<ChartsBean>()

        if (isSTAccount) {
            baseData.data.data?.list?.let {
                chartsBeanList.addAll(it)
            }
            // 保存小数位
            if (chartsBeanList.isNotEmpty()) {
                ChartUIParamUtil.digits = baseData.data.data?.digits.ifNull(2)
            }
        } else {
            baseData.obj.data?.list?.let {
                chartsBeanList.addAll(it)
            }
            // 保存小数位
            if (chartsBeanList.isNotEmpty()) {
                ChartUIParamUtil.digits = baseData.obj.data?.digits.ifNull(2)
            }
        }

        saveSuccessDealLog(
            "symbol:" + symbol + "  type:" + finalLogType + "  from:" + longTimeToString(fromTime + "000", "yyyy-MM-dd HH:mm:ss") + "  to:" + longTimeToString(
                toTime.toString() + "000",
                "yyyy-MM-dd HH:mm:ss"
            ) + "  count:" + (if (chartsBeanList == null) 0 else chartsBeanList.size), "k-line", startTimeMillisWs
        )
        // 没有请求到数据
        if (isLoadMore) {
            if (chartsBeanList == null || chartsBeanList.isEmpty()) {
                isDataEnd = true
                // load more不会触发isSwitchingSymbol
                return
            }
        } else {
            if (chartsBeanList == null || chartsBeanList.isEmpty()) {
                hideLoading()
                // 展示缺省图
                noDataLiveData.value = true
                if (isSwitchingSymbol) {
                    sendEvent(NoticeConstants.SEND_EVENT_TAG_SWITCH_SYMBOL_COMPLETE)
                }
                return
            }
        }

        //              LogUtil.d("wj", "V data size: "+chartsBeanList.size());
        noDataLiveData.value = false

        if (!isLoadMore) {
            sendEvent(NoticeConstants.SEND_EVENT_TAG_KLINE_DELAY_TIMER)
        } else {
            if (interval != selectedInterval) {
                return
            }
        }
        // 不是自动画下一根的请求
        if (!isAutoRefresh) {
            for (i in chartsBeanList.indices) {
                if (i != chartsBeanList.size - 1) {
                    val chartsBean = chartsBeanList.get(i)
                    //                            除最后一个数据，其他均打上标识
                    chartsBean.checked = true
                } else {
                    // 因为发现接口数据的最后一根也会返回close价格，所以这里不再做修改，免得因为字段出问题，暂用接口的close，行情连接上以后会继续更新
//                    val lastOne = chartsBeanList.get(i)
//                    if (data?.bid != 0f) {
//                        lastOne.close = "${data?.originalBid}".toDoubleCatching().ifNull()
//                    }
                }
            }
            if (isLoadMore) {
                for (i in chartsBeanList.indices.reversed()) {
                    if (chartsBeanList.getOrNull(i)?.timestamp?.toLongCatching().ifNull() >= KLineDataUtils.mainList.getOrNull(0)?.timestamp?.toLongCatching().ifNull()) {
                        chartsBeanList.removeAt(i)
                    }
                }
                if (chartsBeanList.isEmpty()) {
                    isDataEnd = true
                }
                KLineDataUtils.mainList.addAll(0, chartsBeanList)
            } else {
                KLineDataUtils.mainList.clear()
                // 保存所有数据
                KLineDataUtils.mainList.addAll(chartsBeanList)
            }
            if (isSwitchingSymbol) {
                sendEvent(NoticeConstants.SEND_EVENT_TAG_SWITCH_SYMBOL_COMPLETE)
            }
//            val index = KLineDataUtils.mainList.size - 1
//            LogUtil.i("wj", "起始index = $index")
        } else {
            if (chartsBeanList.isNotEmpty() && KLineDataUtils.mainList.isNotEmpty()) {
//                LogUtil.i("wj", "--------------------------------检查并覆盖----------------------------------");
                val mainLastestBean = KLineDataUtils.mainList.getOrNull(KLineDataUtils.mainList.size - 1)
                for (x in chartsBeanList.indices) {
                    val bean = chartsBeanList.getOrNull(x)
                    if (bean?.timestamp.ifNull() > mainLastestBean?.timestamp.ifNull()) {
                        KLineDataUtils.mainList.add(bean)
                    }
                }
                for (i in chartsBeanList.indices) {
                    val chartsBean = chartsBeanList.getOrNull(i)
                    for (j in KLineDataUtils.mainList.size - 2 downTo 0) {
                        var mainChartsBean = KLineDataUtils.mainList.getOrNull(j)
                        if (chartsBean?.timestamp.ifNull().compareTo(mainChartsBean?.timestamp.ifNull()) == 0 && mainChartsBean?.checked == false) {
//                            LogUtil.i("wj", "j:" + j + ", time:" + sdf.format(new Date(Long.parseLong(mainChartsBean.getTimestamp() + "000"))));
                            chartsBean?.checked = true
                            mainChartsBean = chartsBean
                            KLineDataUtils.mainList.set(j, mainChartsBean)
                        }
                    }
                }
                //                LogUtil.i("wj", "--------------------------------检查完成----------------------------------");
            }
        }
        isAutoRefresh = false
        sendEvent(NoticeConstants.SEND_EVENT_TAG_KLINE_REFRESH_CHART)
    }

    private fun getLastestChecked(): ChartsBean? {
        if (!KLineDataUtils.mainList.isEmpty()) {
            for (i in KLineDataUtils.mainList.indices) {
                val bean = KLineDataUtils.mainList.getOrNull(i)
                if (bean?.checked == false && i > 1) {
                    return KLineDataUtils.mainList.getOrNull(i - 1)
                }
            }
        }
        return null
    }

    // 调用交易情绪接口
    fun tradeOrderTradeEmotion(symbol: String) {
        val bodyMap = HashMap<String, String>()
        bodyMap.put("data", buildGson().toJson(hashMapOf<String, String>(
            "symbol" to symbol
        )))
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(bodyMap).toString())
        requestNet({
            tradingService.tradeOrderTradeEmotion(requestBody)
        }, onSuccess = {
            if (it.isSuccess()) {
                tradeEmotionUiState.value = UIState.Success(it.data)
            } else {
                tradeEmotionUiState.value = UIState.Error(it.getResponseMsg())
            }
        })
    }

    // 调用交易价格变动接口
    fun tradeOrderTradePriceChange(symbol: String, isMore: Boolean = false) {
        val bodyMap = HashMap<String, String>()
        bodyMap.put("data", buildGson().toJson(hashMapOf<String, String>(
            "symbol" to symbol,
            "server" to if (UserDataUtil.accountCd().isNotEmpty() && !UserDataUtil.isRebateAccount()) UserDataUtil.serverId() else "",
            "login" to UserDataUtil.accountCd()
        )))
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(bodyMap).toString())
        requestNet({
            tradingService.tradeOrderTradePriceChange(requestBody)
        }, onSuccess = {
            if (it.isSuccess()) {
                if (isMore) {
                    priceChangeDataMore.value = it.data
                } else {
                    priceChangeData = it.data
                }
            } else {
                if (isMore) {
                    priceChangeDataMore.value = null
                } else {
                    priceChangeData = null
                }
            }
        }, onError = {
            if (isMore) {
                priceChangeDataMore.value = null
            } else {
                priceChangeData = null
            }
        })
    }

    fun setTakeProfitOrStopLoss(tpPrice: String, slPrice: String, bean: ShareOrderData?) {
        if (UserDataUtil.isStLogin()) {
            stTradePositionUpdate(tpPrice, slPrice, bean)
            return
        }
        if (bean != null) {
            val jsonObject = JsonObject()
            jsonObject.addProperty("login", UserDataUtil.accountCd())
            jsonObject.addProperty("price", bean.openPrice)
            jsonObject.addProperty("tp", tpPrice)
            jsonObject.addProperty("sl", slPrice)
            jsonObject.addProperty("order", bean.order)
            jsonObject.addProperty("token", UserDataUtil.tradeToken())
            jsonObject.addProperty("cmd", bean.cmd)
            jsonObject.addProperty("symbol", bean.symbol)
            var mulNum = "100"
            if (UserDataUtil.isMT5()) {
                mulNum = "10000"
            }
            var handleCount = bean.volume.mathMul(mulNum)
            if (handleCount.contains(".")) handleCount = handleCount.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()[0]
            jsonObject.addProperty("volume", handleCount)
            jsonObject.addProperty("serverId", UserDataUtil.serverId())

            val jsonObject2 = JsonObject()
            jsonObject2.addProperty("data", jsonObject.toString())

            val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject2.toString())

            requestNet({
                tradingService.tradeOrdersUpdateApi(requestBody)
            }, onSuccess = {
                if ("10100051" == it.code) {
                    tokenErrorLiveData.value = it.info
                } else {
                    EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                    // LogUtil.i("wj", "HKLineChartModel  [Post]  Constants.ORDER_CHANGE_SOCKET");
                    showToast(it.info)
                }
            }, isShowDialog = true)
        }
    }

    private fun stTradePositionUpdate(tpPrice: String, slPrice: String, bean: ShareOrderData?) {
        if (bean == null) return
        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())

        //        jsonObject.addProperty("positionId", bean.getOrder());
        jsonObject.addProperty("positionId", bean.stOrder)
        jsonObject.addProperty("takeProfit", tpPrice)
        jsonObject.addProperty("stopLoss", slPrice)

        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet({
            stTradingService.tradePositionUpdateApi(requestBody)
        }, onSuccess = {
            if ("200" == it.code) {
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                // LogUtil.i("wj", "HKLineChartModel  [Post]  Constants.ORDER_CHANGE_SOCKET");
                showToast(it.msg)
            } else {
                showToast(it.msg)
            }
        }, isShowDialog = true)
    }
}