package cn.com.vau.trade.viewmodel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.trade.TrendBean
import cn.com.vau.util.ToastUtil.showToast
import com.google.gson.Gson
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * 产品搜索
 * Author: array
 * Date: 2025/1/10 13:40
 */
class SearchProductViewModel : BaseViewModel() {

    val hotProductMap = HashMap<String, List<String>>()
    val weekTrendMap = HashMap<String, ArrayList<String>>()
    val initHotProductsLiveData = MutableLiveData<Boolean>()
    val refreshHotLiveData = MutableLiveData<Boolean>()
    val refreshAdapterLiveData = MutableLiveData<Boolean>()

    val hotList = arrayListOf<ShareProductData>()

    /**
     * 查询热门搜索产品
     */
    fun querySearchHot() {

        requestNet({
            val map = hashMapOf<String, Any>()
            if (UserDataUtil.isLogin() && !UserDataUtil.isRebateAccount()) {
                map["login"] = UserDataUtil.accountCd()
                map["serverId"] = UserDataUtil.serverId()
                map["token"] = UserDataUtil.loginToken()
            }
            if (UserDataUtil.isStLogin()) {
                map["login"] = UserDataUtil.stAccountId()
                map["serverId"] = UserDataUtil.serverId()
                map["token"] = UserDataUtil.stToken()
            }

            baseService.productHot(map)
        }, { productHotBean ->

            if (productHotBean?.resultCode == "V00000") {
                hotProductMap.clear()
                val data = productHotBean.data
                if (data?.obj != null) {
                    data.obj?.forEach { map ->
                        hotProductMap.putAll(map)
                    }
                }
                initHotProductsLiveData.value = true
            }

        })
    }

    /**
     * 趋势图
     */
    fun queryWeekTrend() {

        val paramMap = HashMap<String, Any>()
        val mGson = Gson()
        val prods = getWeekTrendProds()
        if (prods.isEmpty()) {
            return
        }

        paramMap["symbols"] = prods
        var serverId = "5"
        if (!TextUtils.isEmpty(UserDataUtil.accountCd()) && !UserDataUtil.isRebateAccount()) {
            if (!TextUtils.isEmpty(UserDataUtil.serverId())) {
                serverId = UserDataUtil.serverId()
            }
        }
        paramMap["serverId"] = serverId
        if (UserDataUtil.isLogin()) {
            paramMap["login"] = UserDataUtil.accountCd()
        } else {
            paramMap["login"] = ""
        }
        val bodyMap = HashMap<String, String>()
        bodyMap["data"] = mGson.toJson(paramMap)

        requestNet({
            val requestBody = mGson.toJson(bodyMap).toRequestBody("application/json".toMediaTypeOrNull())
            tradingService.tradeProductTrendApi(requestBody)
        }, { dataBean: TrendBean? ->
            if (dataBean?.code != "200") return@requestNet
            if (dataBean.obj != null) {
                weekTrendMap.putAll(dataBean.obj!!)
                refreshHotLiveData.value = true
            }
        })
    }

    private fun getWeekTrendProds(): ArrayList<String> {
        val arr = ArrayList<String>()
        for (hot in hotList) {
            val data = weekTrendMap[hot.symbol]
            // 把没有趋势图数据的产品添加进去，传递给后台，请求趋势图数据
            if (data == null) {
                arr.add(hot.symbol)
            }
        }
        return arr
    }

    /**
     * 跟单热门产品
     */
    fun querySTProductHot() {
        requestNet({
            stTradingService.productHot()
        }, { dataBean ->
            if (dataBean?.code == "200") {
                hotProductMap.clear()
                hotProductMap.putAll(dataBean.data ?: HashMap())
//                mView.initHotProducts()
                initHotProductsLiveData.value = true
            }
        })
    }

    /**
     * 跟单趋势图
     */
    fun querySTHistoryGetRunChart() {

        val paramMap = HashMap<String, Any>()
        val mGson = Gson()
        paramMap["server"] = "MT5"
        val prods = getWeekTrendProds()
        if (prods.isEmpty()) {
            return
        }
        paramMap["symbols"] = prods

        requestNet({
            val requestBody = mGson.toJson(paramMap).toRequestBody("application/json".toMediaTypeOrNull())
            stTradingService.historyGetRunChart(requestBody)
        }, { stTrendBean ->
            if (stTrendBean?.code != "200") return@requestNet

            val data = stTrendBean.data?.data?.data ?: emptyList()
            data.forEach { dataBean ->
                weekTrendMap.putAll(dataBean)
            }
            refreshHotLiveData.value = true
        })
    }

    /**
     * 搜索内容发送服务器
     */
    fun addSearchRecord(token: String, code: String, type: String?) {
        val params = HashMap<String, Any>()
        if ("" != token) {
            params["token"] = token
        }
        params["code"] = code
        requestNet({ baseService.productRecord(params) }, {})
    }

    /**
     * status:  true表示已收藏   false表示未收藏
     */
    fun updOptionalProd(symbol: String, status: Boolean) {
        val optionalList = VAUSdkUtil.collectSymbolList
        if (status) {
            for (i in optionalList.indices) {
                if (optionalList[i].symbol == symbol) {
                    optionalList.removeAt(i)
                    break
                }
            }
        } else {
            val shareSymbolList = VAUSdkUtil.symbolList()
            for (i in shareSymbolList.indices) {
                val symbolData = shareSymbolList[i]
                if (symbolData.symbol == symbol) {
                    optionalList.add(symbolData)
                    break
                }
            }
        }

        refreshAdapterLiveData.value = true

        val symbols = StringBuilder()
        for (i in optionalList.indices) {
            symbols.append(optionalList[i].symbol)
            symbols.append(",")
        }

        if (UserDataUtil.isStLogin()) {
            val jsonObject = JsonObject()
            jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
            jsonObject.addProperty("symbols", symbols.toString())

            requestNet({
                val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
                //ST 【跟单】自选产品 移动
                stTradingService.stAccountProductMyUpd(requestBody)
            }, {
                if ("200" != it.getResponseCode()) {
                    showToast(it.getResponseMsg())
                }
            })

        } else {
            val paramMap = HashMap<String, Any>()
            paramMap["login"] = UserDataUtil.accountCd()
            paramMap["token"] = UserDataUtil.loginToken()
            paramMap["symbols"] = symbols.toString()

            requestNet({
                //自选产品 移动
                baseService.prodUpd(paramMap)
            }, { baseData ->
                if ("********" != baseData.getResponseCode()) {
                    showToast(baseData.getResponseMsg())
                    return@requestNet
                }
            })
        }
    }

}