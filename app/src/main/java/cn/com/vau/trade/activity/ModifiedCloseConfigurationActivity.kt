package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.graphics.Paint
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.BottomStaticLayoutPopup
import cn.com.vau.databinding.*
import cn.com.vau.trade.adapter.CloseConfigurationAdapter
import cn.com.vau.trade.bean.CloseConfigSymbolBean
import cn.com.vau.trade.model.ModifiedCloseConfigurationViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import com.lxj.xpopup.XPopup
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * 批量平仓 -- 筛选订单 页
 * 快捷进入时传入symbol，key:PARAM_ORDER_NAME_PRODUCT
 */
@SuppressLint("SetTextI18n")
class ModifiedCloseConfigurationActivity :
    BaseMvvmActivity<ActivityModifiedCloseConfigurationBinding, ModifiedCloseConfigurationViewModel>() {

    private val selectAll by lazy {
        this.getString(R.string.select_all)
    }
    private val unSelectAll by lazy {
        this.getString(R.string.unselect_all)
    }

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cebffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e)
    }

    private val draw_shape_c1e1e1e_cebffffff_r100 by lazy {
        ContextCompat.getDrawable(
            this,
            R.drawable.draw_shape_c1e1e1e_cebffffff_r100
        )
    }
    private val draw_shape_c0a1e1e1e_c0affffff_r100 by lazy {
        ContextCompat.getDrawable(
            this,
            R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
    }
    private var selectedSymbol = ""
    private val headerView: HeaderCloseConfigurationBinding by lazy {
        HeaderCloseConfigurationBinding.inflate(layoutInflater)
    }

    private val helpView by lazy { FilterIntroduceDialogBinding.inflate(layoutInflater, null, false) }
    private val helpPopup by lazy {
        helpView.tvContent.text =
            "${getString(R.string.filter_your_trades_and_eg)}\n${getString(R.string.select_which_then_select_to_close)}"
        XPopup.Builder(this).asCustom(BottomStaticLayoutPopup(this, helpView.root))
    }

    private val mAdapter by lazy {
        CloseConfigurationAdapter()
    }
    private val unSelectDrawable by lazy {
        ContextCompat.getDrawable(this, R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
    }

    private val selectDrawable by lazy {
        ContextCompat.getDrawable(this, R.drawable.icon2_cb_tick_circle_c15b374)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
        intent.extras?.let {
            selectedSymbol = it.getString(Constants.PARAM_PRODUCT_NAME, "")
        }
    }

    override fun initData() {
        super.initData()
        if (selectedSymbol.isEmpty()) {
            getSymbols()
        } else {
            getSymbolsAndSelectedOne()
        }
    }

    @SuppressLint("SetTextI18n", "UseCompatLoadingForDrawables")
    override fun initView() {
        mBinding.mHeaderBar.setTitleText(getString(R.string.close_configuration))
            .setEndIconDrawable(getDrawable(AttrResourceUtil.getDrawable(this, R.attr.icon1Faq)))
            .setEndIconClickListener {
                helpPopup.show()
            }
        initRecyclerView()
    }

    private fun initRecyclerView() {
        initHeader()
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        mBinding.mRecyclerView.adapter = mAdapter
        mAdapter.setHeaderView(headerView.root)
        mAdapter.setNbOnItemClickListener { adapter, view, position ->
            adapter.data[position]?.let {
                selectedSymbols((it as CloseConfigSymbolBean), position + adapter.headerLayoutCount)
            }
        }
    }

    private fun selectedSymbols(closeConfigSymbolBean: CloseConfigSymbolBean, position: Int) {
        closeConfigSymbolBean.isSelected = closeConfigSymbolBean.isSelected.not()
        mAdapter.notifyItemChanged(position, "")
        closeConfigSymbolBean.shareOrderData?.let {
            if (closeConfigSymbolBean.isSelected) {
                CloseConfigHelper.selectSymbols.add(it)
            } else {
                CloseConfigHelper.selectSymbols.remove(it)
            }
        }
        checkConfig()
        setTvSymbolSelect()
    }

    private fun initHeader() {
        headerView.tvUnSelectAll.paintFlags =
            headerView.tvUnSelectAll.paintFlags or Paint.UNDERLINE_TEXT_FLAG
        headerView.tvProfit.clickNoRepeat {
            val isSelected =
                (CloseConfigHelper.configProfitLoss and CloseConfigHelper.CONFIG_PROFIT) == CloseConfigHelper.CONFIG_PROFIT
            headerView.ivProfitSelected.setImageDrawable(if (isSelected.not()) selectDrawable else unSelectDrawable)
            if (isSelected.not()) {
                CloseConfigHelper.configProfitLoss =
                    CloseConfigHelper.configProfitLoss or CloseConfigHelper.CONFIG_PROFIT
            } else {
                CloseConfigHelper.configProfitLoss =
                    CloseConfigHelper.configProfitLoss and (CloseConfigHelper.CONFIG_PROFIT.inv())
            }
            checkConfig()
        }
        headerView.tvLoss.clickNoRepeat {
            val isSelected =
                (CloseConfigHelper.configProfitLoss and CloseConfigHelper.CONFIG_LOSS) == CloseConfigHelper.CONFIG_LOSS
            headerView.ivLossSelected.setImageDrawable(if (isSelected.not()) selectDrawable else unSelectDrawable)
            if (isSelected.not()) {
                CloseConfigHelper.configProfitLoss =
                    CloseConfigHelper.configProfitLoss or CloseConfigHelper.CONFIG_LOSS
            } else {
                CloseConfigHelper.configProfitLoss =
                    CloseConfigHelper.configProfitLoss and (CloseConfigHelper.CONFIG_LOSS.inv())
            }
            checkConfig()

        }
        headerView.tvBuy.clickNoRepeat {
            val isSelected =
                (CloseConfigHelper.configDirection and CloseConfigHelper.CONFIG_BUY) == CloseConfigHelper.CONFIG_BUY
            headerView.ivBuySelected.setImageDrawable(if (isSelected.not()) selectDrawable else unSelectDrawable)
            if (isSelected.not()) {
                CloseConfigHelper.configDirection =
                    CloseConfigHelper.configDirection or CloseConfigHelper.CONFIG_BUY
            } else {
                CloseConfigHelper.configDirection =
                    CloseConfigHelper.configDirection and (CloseConfigHelper.CONFIG_BUY.inv())
            }
            checkConfig()

        }
        headerView.tvSell.clickNoRepeat {
            val isSelected =
                (CloseConfigHelper.configDirection and CloseConfigHelper.CONFIG_SELL) == CloseConfigHelper.CONFIG_SELL
            headerView.ivSellSelected.setImageDrawable(if (isSelected.not()) selectDrawable else unSelectDrawable)
            if (isSelected.not()) {
                CloseConfigHelper.configDirection =
                    CloseConfigHelper.configDirection or CloseConfigHelper.CONFIG_SELL
            } else {
                CloseConfigHelper.configDirection =
                    CloseConfigHelper.configDirection and (CloseConfigHelper.CONFIG_SELL.inv())
            }
            checkConfig()
        }

        headerView.tvUnSelectAll.clickNoRepeat {
            selectAllOrNot()
            checkConfig()
        }

    }

    private fun sensorsTrack(buttonName: String, orderOptions: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        properties.put("order_options", orderOptions)
        SensorsDataUtil.track(SensorsConstant.V3510.CLOSE_CONFIGUATION_PAGE_STEP1BTN_CLICK, properties)
    }

    private fun sensorsTrackClick() {
        SensorsDataUtil.track(SensorsConstant.V3510.CLOSE_CONFIGUATION_PAGE_STEP1_NEXT_BTN_CLICK)
    }

    private fun selectAllOrNot() {
        if (headerView.tvUnSelectAll.text.toString() == selectAll) {
            mAdapter.data.forEach { data ->
                data.isSelected = true
                data.shareOrderData?.let {
                    if (CloseConfigHelper.selectSymbols.contains(it).not()) {
                        CloseConfigHelper.selectSymbols.add(it)
                    }
                }
            }
            headerView.tvUnSelectAll.text = unSelectAll
            sensorsTrack("select All", "")
        } else {
            mAdapter.data.forEach { data ->
                data.isSelected = false
                data.shareOrderData?.let {
                    if (CloseConfigHelper.selectSymbols.contains(it)) {
                        CloseConfigHelper.selectSymbols.remove(it)
                    }
                }
            }
            headerView.tvUnSelectAll.text = selectAll
            sensorsTrack("Unselect All", "")
        }
        mAdapter.notifyDataSetChanged()
    }

    private fun checkConfig() {
        val hasProfitLoss = CloseConfigHelper.hasSelectProfitOrLoss()
        val hasDirection = CloseConfigHelper.hasSelectDirection()
        val hasSymbols = CloseConfigHelper.hasSelectSymbols()
        headerView.tvProfitLossTip.isVisible = hasProfitLoss.not()
        headerView.tvDirectionTip.isVisible = hasDirection.not()
        headerView.tvSymbolTip.isVisible = hasSymbols.not()
        mBinding.tvNext.setTextColor(if (hasDirection && hasSymbols && hasProfitLoss) color_cebffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
        mBinding.tvNext.background =
            if (hasDirection && hasSymbols && hasProfitLoss) draw_shape_c1e1e1e_cebffffff_r100 else draw_shape_c0a1e1e1e_c0affffff_r100
    }

    private fun getSymbols() {
        val list = VAUSdkUtil.shareOrderList().distinctBy { it.symbol }.sortedBy { it.symbol }
        val symbolList = arrayListOf<CloseConfigSymbolBean>()
        list.forEach {
            val closeConfigSymbolBean = CloseConfigSymbolBean().apply {
                this.isSelected = true
                this.shareOrderData = it
            }
            symbolList.add(closeConfigSymbolBean)
            CloseConfigHelper.selectSymbols.add(it)
        }
        mAdapter.setList(symbolList)
    }

    private fun getSymbolsAndSelectedOne() {
        val list = VAUSdkUtil.shareOrderList().distinctBy { it.symbol }.sortedBy { it.symbol }
        val symbolList = arrayListOf<CloseConfigSymbolBean>()
        list.forEach {
            val closeConfigSymbolBean = CloseConfigSymbolBean().apply {
                this.isSelected = selectedSymbol == it.symbol
                this.shareOrderData = it
            }
            symbolList.add(closeConfigSymbolBean)
            if (selectedSymbol == it.symbol) {
                CloseConfigHelper.selectSymbols.add(it)
            }

        }
        mAdapter.setList(symbolList)
        setTvSymbolSelect()
    }

    private fun setTvSymbolSelect() {
        if (CloseConfigHelper.selectSymbols.size == mAdapter.data.size) {
            headerView.tvUnSelectAll.text = unSelectAll
        } else {
            headerView.tvUnSelectAll.text = selectAll
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvNext.setOnClickListener(this)
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvNext -> {
                val hasProfitLoss = CloseConfigHelper.hasSelectProfitOrLoss()
                val hasDirection = CloseConfigHelper.hasSelectDirection()
                val hasSymbols = CloseConfigHelper.hasSelectSymbols()
                if ((hasDirection && hasSymbols && hasProfitLoss).not()) {
                    ToastUtil.showToast(getString(R.string.select_at_least_per_section))
                    return
                }
                openActivity(ModifiedCloseConfigurationEndActivity::class.java)
                sensorsTrackClick()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        CloseConfigHelper.clear()
        EventBus.getDefault().unregister(this)
    }

}