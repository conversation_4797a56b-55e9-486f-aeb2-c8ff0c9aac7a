package cn.com.vau.trade.fragment.deal

import android.util.Log
import cn.com.vau.BuildConfig
import cn.com.vau.data.init.ShareProductData

class TestLog {

    companion object{
        fun print(where : String, dataList: ArrayList<ShareProductData>){
//            if (!BuildConfig.DEBUG) {
//                return
//            }
//            Log.i("xinhuan","-------- " + where + " -----开始------")
//            dataList.forEachIndexed { index, shareProductData ->
//                printBean(index, shareProductData)
//            }
//            Log.i("xinhuan","-------- " + where + " ------结束-----")
        }

        fun printBean(index :Int,  shareProductData : ShareProductData) {
//            if (!BuildConfig.DEBUG) {
//                return
//            }
//            Log.i("xinhuan", "index = " + index + " symbol = " + shareProductData.symbol + ", refresh = " + shareProductData.refresh+ ", ask = " + shareProductData.ask + " , askUi = " + shareProductData.askUI)
        }
    }
}