package cn.com.vau.trade.adapter

import androidx.recyclerview.widget.DiffUtil
import cn.com.vau.data.init.ShareOrderData

class OrderPendingDiffItemCallback: DiffUtil.ItemCallback<ShareOrderData>() {

    // 是否是同一个对象  判断旧数据集中的某个元素和新数据集中的某个元素是否代表同一个实体
    override fun areItemsTheSame(oldItem: ShareOrderData, newItem: ShareOrderData): Boolean {
        return oldItem.javaClass == newItem.javaClass
    }

    // 是否是相同内容  判断旧数据集中的某个元素和新数据集中的某个元素的内容是否相同
    override fun areContentsTheSame(oldItem: ShareOrderData, newItem: ShareOrderData): Boolean {
        return oldItem.ask == newItem.ask && oldItem.bid == newItem.bid
    }

    /**
     * 获取旧数据集中的某个元素和新数据集中的某个元素之间的差异信息。
     * 如果这两个元素相同，但是内容发生改变，可以通过这个方法获取它们之间的差异信息，从而只更新需要改变的部分，减少不必要的更新操作。
     *
     * areItemsTheSame()返回true 且 areContentsTheSame()返回false 时调用
     * 两个对象代表的数据是一条，但是内容更新了。此方法为定向刷新使用，可选。
     */
    override fun getChangePayload(oldItem: ShareOrderData, newItem: ShareOrderData): Boolean {
        return (oldItem.ask != newItem.ask || oldItem.bid != newItem.bid)
    }

}