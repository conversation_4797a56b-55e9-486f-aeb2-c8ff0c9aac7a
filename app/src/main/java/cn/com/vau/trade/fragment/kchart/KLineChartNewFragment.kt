package cn.com.vau.trade.fragment.kchart

import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.drawable.Drawable
import android.os.*
import android.util.Log
import android.view.*
import android.view.View.OnTouchListener
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.mvvm.state.UIState
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.BottomKLineNewGuideDialog
import cn.com.vau.common.view.popup.bean.KLineViewSettingData
import cn.com.vau.common.view.popup.bean.TradingViewSettingData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.*
import cn.com.vau.page.setting.viewmodel.KLineChartNewViewModel
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.profile.activity.pricealert.activity.PriceAlterListActivity
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.adapter.KLineChartIndicatorTabAdapter
import cn.com.vau.trade.adapter.KLineChartIntervalTabNewAdapter
import cn.com.vau.trade.bean.KLineEvent
import cn.com.vau.trade.kchart.ChartUIParamUtil
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.kchart.pop.BottomKLinePriceChangeDialog
import cn.com.vau.trade.kchart.pop.BottomKLineSettingsDialog
import cn.com.vau.trade.kchart.tradingview.ChartCandleLandscapeActivity
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.tracking.*
import cn.com.vau.util.tracking.SensorsHelper.getTradeType
import cn.com.vau.util.widget.dialog.BottomContentDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.github.tifezh.kchartlib.chart.BaseKChartView.OnTakeProfitStopLossChanged
import com.github.tifezh.kchartlib.chart.draw.MainDraw
import com.github.tifezh.kchartlib.chart.formatter.DateFormatter
import com.github.tifezh.kchartlib.chart.impl.IKChartView
import com.github.tifezh.kchartlib.helper.bean.*
import com.github.tifezh.kchartlib.helper.chart.KChartAdapter
import com.scwang.smart.refresh.layout.api.RefreshLayout
import org.greenrobot.eventbus.*
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.*

class KLineChartNewFragment : BaseMvvmBindingFragment<FragmentKlineChartNewBinding>() {

    val mViewModel: KLineChartNewViewModel by activityViewModels()
    private val activityViewModel: KLineViewModel by activityViewModels()
    private val shape_cf44040_r8: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.shape_cf44040_r8) }
    private val shape_c00c79c_r8: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.shape_c00c79c_r8) }
    private val color_c0a1e1e1e_c262930 by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c0a1e1e1e_c262930) }
    private val color_ca61e1e1e_c99ffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_ca61e1e1e_c99ffffff) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff) }
    private val imgKlineLogo by lazy { AttrResourceUtil.getDrawable(requireContext(), R.attr.imgKlineLogo) }
    private val color_cebffffff_c1e1e1e by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_cebffffff_c1e1e1e) }
    private val mainLayoutBg by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg) }
    private val draw_shape_c731e1e1e_c61ffffff_r8: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.draw_shape_c731e1e1e_c61ffffff_r8) }
    private val color_c731e1e1e_c61ffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c731e1e1e_c61ffffff) }
    private val c00c79c by lazy { ContextCompat.getColor(requireContext(), R.color.c00c79c) }
    private val ce35728 by lazy { ContextCompat.getColor(requireContext(), R.color.ce35728) }
    private val transparent by lazy { ContextCompat.getColor(requireContext(), R.color.transparent) }

    private var isShowMore = false
    private var isShareShow = false
    private var firstLoaded = false
    private var titleToggleTop: Int = -1
    var isShowTitle = false

    var mAdapter = KChartAdapter()
    private var isShowDragTip = false

    /**
     * k线设置弹窗
     */
    private var settingDialog: BottomKLineSettingsDialog? = null
    private var kLineChartTipFloatBinding: IncludePortraitKlineChartTipsLayoutBinding? = null

    /**
     * k线新版本引导弹窗
     */
    private var bottomKLineNewGuideDialog: BottomKLineNewGuideDialog? = null

    private var wrPriceChangeDialog: WeakReference<BottomKLinePriceChangeDialog>? = null

    private val footerView: FootKlineTabIntervalNewBinding by lazy { FootKlineTabIntervalNewBinding.inflate(layoutInflater, mBinding.intervalRecyclerView, false) }
    private val intervalAdapter by lazy {
        KLineChartIntervalTabNewAdapter().apply {
            addFooterView(footerView.root, orientation = LinearLayout.HORIZONTAL)
        }
    }
    private val chartTypeAdapter by lazy { KLineChartIndicatorTabAdapter() }
    private val tradeSentimentTipDialog by lazy {
        BottomContentDialog.Builder(requireActivity())
            .setTitle(getString(R.string.traders_sentiment))
            .setContent(getString(R.string.traders_sentiment_tip))
            .build()
    }

    private val performManager by lazy {
        PerformManager(this)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.data = activityViewModel.data
        mViewModel.symbol = activityViewModel.symbol
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        KLineDataUtils.isFrontPortrait = true
        mViewModel.isScreenPortrait = true
        HttpUrl.isSwitchTradingView = Build.VERSION.SDK_INT > Build.VERSION_CODES.P
        if (activityViewModel.selectedOrderNo.isNotEmpty()) {
            KLineDataUtils.selectedOrderNo = activityViewModel.selectedOrderNo
        }
        mViewModel.isShowDraw = SpManager.getKlineShowDraw()
        mViewModel.selectedInterval = SpManager.getChartTypeText("1D")   // 默认1D
        mViewModel.isTimeShare = mViewModel.selectedInterval == "Tick"
        mViewModel.initKLineSettingData()
        mViewModel.initChartIndicatorList()
        mViewModel.initOrderData()
        mViewModel.initDrawData()
        if (KLineDataUtils.userDataKV == null) {
            KLineDataUtils.userDataKV = KLineViewSettingData.getHistoryData()
        }
        // 埋点
        SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_VIEW)
    }

    override fun initView() {
        addPerformance()
        mViewModel.loadingChange.dialogLiveData.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            if (it) { //显示弹框
                showLoadDialog()
            } else { //关闭弹窗
                hideLoadDialog()
            }
        }
        // 开盘价不变
        mBinding.tvBoardOpen.text = mViewModel.data?.open.numFormat(mViewModel.data?.digits.ifNull(), false)
        val drawable = ContextCompat.getDrawable(requireActivity(), R.drawable.icon2_close_36x36)
        drawable?.setBounds(0, 0, 12.dp2px(), 12.dp2px())
        if (LanguageHelper.isRtlLanguage()){
            mBinding.tvDragTip.setCompoundDrawables(drawable, null, null, null)
        }else {
            mBinding.tvDragTip.setCompoundDrawables(null, null, drawable, null)
        }
        mBinding.vsChartTipFloat.setOnInflateListener { _, inflated ->
            if (kLineChartTipFloatBinding == null) {
                kLineChartTipFloatBinding = IncludePortraitKlineChartTipsLayoutBinding.bind(inflated)
                if (LanguageHelper.isRtlLanguage()){
                    kLineChartTipFloatBinding?.clChartTipFloat?.layoutDirection = View.LAYOUT_DIRECTION_RTL
                }else {
                    kLineChartTipFloatBinding?.clChartTipFloat?.layoutDirection = View.LAYOUT_DIRECTION_LTR
                }
            }
        }

        mBinding.intervalRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.intervalRecyclerView.adapter = intervalAdapter
        intervalAdapter.setList(mViewModel.intervalList)
        selectInterval(mViewModel.selectedInterval, false)
        mBinding.chartTypeRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.chartTypeRecyclerView.adapter = chartTypeAdapter
        chartTypeAdapter.setList(mViewModel.chartTypeDataList)
        chartTypeAdapter.changeChartSelected(mViewModel.mainIndicatorList, mViewModel.subIndicatorList)
        mBinding.chartTypeRecyclerView.isVisible = !mViewModel.isTimeShare
        mBinding.root.post {
            // Firebase报 mBinding.intervalRecyclerView 空指针？？？
            mBinding ?: return@post
            mBinding.intervalRecyclerView ?: return@post
//            Log.i("wj", "initView: top:${mBinding.tvBoardDiff.top}  bottom:${mBinding.tvBoardDiff.bottom}")
            titleToggleTop = mBinding.tvBoardDiff.bottom
        }
        // KChartView设置
        mBinding.kCharView.let {
            it.setOverScrollRange(75f.dp2px())
            it.setKChartScaleX(SpManager.getKlineScale())
            it.font400 = FontResourceUtil.typefaceGilroyRegular(requireActivity())
            it.font500 = FontResourceUtil.typefaceGilroyMedium(requireActivity())
            it.font600 = FontResourceUtil.typefaceGilroySemiBold(requireActivity())
            it.setLogo(imgKlineLogo)
            it.setBackgroundColor(mainLayoutBg)
            it.setBuyPriceTextBgColor(mainLayoutBg)
            it.setSelectedLineColor(color_c1e1e1e_cebffffff)
            it.setSelectedBgColor(color_c1e1e1e_cebffffff)
            it.setSelectedValueTextColor(color_cebffffff_c1e1e1e)
            it.setGridLineColor(color_c0a1e1e1e_c262930)
            it.setPriceTextColor(color_ca61e1e1e_c99ffffff)
            it.setSimpleChartDrawTextColor(ChartUIParamUtil.candleMaxMinTextColor)
            it.setMaxMinTextColor(color_c1e1e1e_cebffffff)
            it.setPriceTextSize(9f.dp2px())
            it.setTextTimeColor(color_ca61e1e1e_c99ffffff)
            it.showFullScreen(true)
            it.setMainType(if (mViewModel.isTimeShare) MainDraw.TYPE_MAIN_MINUTE else MainDraw.TYPE_MAIN_CANDLE)
            updateKlineChild()
            updateKlineMainChild()
            it.setAdapter(mAdapter)
            it.dateTimeFormatter = DateFormatter()
            it.orderData = mViewModel.getOrderData()
            it.setGridRows(4)
            it.setGridColumns(4)
            it.setDigit(mViewModel.data?.digits.ifNull(2))
            it.setOrderRecordEntityList(mViewModel.getOrderRecordEntityList())
            it.isShowBuyLine = KLineDataUtils.userData?.askLineDisplay.ifNull()//买价线
            it.isShowSellLine = KLineDataUtils.userData?.bidLineDisplay.ifNull()//卖价线
            it.isShowTpLine = KLineDataUtils.userData?.tpLineDisplay.ifNull()//止盈线
            it.isShowSlLine = KLineDataUtils.userData?.slLineDisplay.ifNull()//止损线
            it.isDrawPositionLine = KLineDataUtils.userData?.positionLineDisplay.ifNull()//订单线
            it.setOnCrossMoveListener(IKChartView.OnCrossMoveListener { view, point, index, isUserSelected, isLeft, isTouch ->
                val data: KLineEntity? = point as? KLineEntity
                LogUtil.v("onSelectedChanged", "index:" + index + " closePrice:" + data?.closePrice)
                val timeZone = AppUtil.getTimeZoneRawOffsetToHour()
                val timeStamp = (data?.timestamp + "000").toLongCatching()
                val nowStamp = timeStamp - (timeZone * 3600 * 1000)
                mBinding.vsChartTipFloat.isVisible = isUserSelected
                if (kLineChartTipFloatBinding == null) {
                    return@OnCrossMoveListener
                }
                kLineChartTipFloatBinding?.clChartTipFloat?.isVisible = isUserSelected
                if (isUserSelected) {
                    val lp = kLineChartTipFloatBinding?.clChartTipFloat?.layoutParams as? FrameLayout.LayoutParams
                    if (isLeft.not()) {
                        lp?.gravity = Gravity.START
                    } else {
                        lp?.gravity = Gravity.END
                    }
                    kLineChartTipFloatBinding?.clChartTipFloat?.setLayoutParams(lp)
                }
                kLineChartTipFloatBinding?.tvChartTipTime?.text = TimeUtil.formatDateTime(nowStamp, "dd/MM/yyyy HH:mm")
                kLineChartTipFloatBinding?.tvChartTipOpenValue?.text = data?.open.numFormat(ChartUIParamUtil.digits, false)
                kLineChartTipFloatBinding?.tvChartTipHighValue?.text = data?.high.numFormat(ChartUIParamUtil.digits, false)
                kLineChartTipFloatBinding?.tvChartTipLowValue?.text = data?.low.numFormat(ChartUIParamUtil.digits, false)
                kLineChartTipFloatBinding?.tvChartTipCloseValue?.text = data?.close.numFormat(ChartUIParamUtil.digits, true)
                updateChartTip(data)
            })
            it.setOnScreenScrollChangeListener { startIndex, endIndex, timestamp ->
                if (mViewModel.isScreenPortrait && startIndex == 0 && !mViewModel.isRequestLoadMore && mViewModel.isEnableLoadMore) {
                    LogUtil.v(TAG, "竖屏分页请求开始")
                    mViewModel.isRequestLoadMore = true
                    mViewModel.symbolsChart(true, true, false, timestamp)
                }
            }
            it.onTakeProfitStopLossChanged = object : OnTakeProfitStopLossChanged {
                override fun onTakeProfitStopLossChanged(price: Float, type: Int) {
                    val isTP = type == OnTakeProfitStopLossChanged.TYPE_TP
                    val priceStr: String = price.numFormat(mViewModel.data?.digits ?: 2, true)
                    val content = if (isTP) getString(R.string.confirm_to_take_profit) + "\n" + getString(R.string.take_profit_price) + ":" + priceStr else
                        getString(R.string.confirm_to_stop_loss) + "\n" + getString(R.string.stop_loss_price) + ":" + priceStr

                    showCenterActionDialog(
                        title = getString(R.string.use_dragn_drop_loss),
                        content = content,
                        startText = getString(R.string.no),
                        endText = getString(R.string.yes_confirm),
                        startAction = {
                            it.resetMoveAbelLine()
                        },
                        endAction = {
                            mViewModel.shareOrderData?.let { order ->
                                if (isTP) {
                                    mViewModel.setTakeProfitOrStopLoss(price, order.stopLoss?.toFloatCatching() ?: 0f, order)
                                } else {
                                    mViewModel.setTakeProfitOrStopLoss(order.takeProfit?.toFloatCatching() ?: 0f, price, order)
                                }
                            }
                        })
                }

                override fun onTpSlMoving(price: Float, openPrice: Float, volume: String, orderType: String): String {
                    mBinding.tvDragTip.isVisible = false
                    return VAUSdkUtil.getProfitLoss(
                        mViewModel.data ?: ShareProductData(),
                        "$openPrice",
                        volume,
                        orderType,
                        "$price"
                    ).numCurrencyFormat()
                }

                override fun onCancelSp() {
                    showCenterActionDialog(
                        content = getString(R.string.cancel_stop_loss),
                        startText = getString(R.string.no),
                        endText = getString(R.string.yes_confirm),
                        endAction = {
                            mViewModel.shareOrderData?.let { order ->
                                mViewModel.setTakeProfitOrStopLoss(
                                    order.takeProfit?.toFloatCatching() ?: 0f, 0f, order
                                )
                            }
                            sensorsTrack("SL")
                        })
                }

                override fun onCancelTp() {
                    showCenterActionDialog(
                        content = getString(R.string.cancel_take_profit),
                        startText = getString(R.string.no),
                        endText = getString(R.string.yes_confirm),
                        endAction = {
                            mViewModel.shareOrderData?.let { order ->
                                mViewModel.setTakeProfitOrStopLoss(
                                    0f,
                                    order.stopLoss?.toFloatCatching() ?: 0f,
                                    order
                                )
                                sensorsTrack("TP")
                            }
                        })
                }

                override fun onPositionLineClick() {
                    showDragTip()
                }

                override fun onSLLineClick() {
                    showDragTip()
                }

                override fun onTPLineClick() {
                    showDragTip()
                }
            }
        }

        mBinding.drawView.let {
            it.isVisible = mViewModel.isShowDraw
            it.pairName = mViewModel.symbol
            it.setDataManager(mViewModel.drawDataManager)
            it.setKChartView(mBinding.kCharView)
            it.digits = (mViewModel.data?.digits ?: 2)
            it.setDrawToolsOpen(false)
        }

        //神策埋点，页面浏览事件
        sensorsTrack(false, "")
        changeKChartSize()
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
    }

    override fun initData() {
        super.initData()
        mViewModel.data?.let { dataBean ->
            // 保证原始价格不为0
            if (dataBean.originalAsk == 0f) dataBean.originalAsk = dataBean.ask
            if (dataBean.originalBid == 0f) dataBean.originalBid = dataBean.bid
        }
        updateProdInfo()
    }

    override fun createObserver() {
        super.createObserver()
        //横屏切换竖屏更新数据
        mViewModel.screenChangeRefreshLiveData.observe(viewLifecycleOwner) {
            if (isShareShow) {
                return@observe
            }
            LogUtil.v(TAG, "screenChangeRefreshLiveData 切换竖屏")
            mBinding.drawView.isVisible = mViewModel.isShowDraw
            mBinding.drawView.setDrawToolsOpen(false)
            mViewModel.initOrderData()
            mBinding.kCharView.orderData = mViewModel.getOrderData()
            mBinding.kCharView.resetMoveAbelLine()
            mBinding.kCharView.setKChartScaleX(mViewModel.klineScale)
            chartTypeAdapter.changeChartSelected(mViewModel.mainIndicatorList, mViewModel.subIndicatorList)
            mBinding.kCharView.isShowBuyLine = KLineDataUtils.userData?.askLineDisplay.ifNull()//买价线
            mBinding.kCharView.isShowSellLine = KLineDataUtils.userData?.bidLineDisplay.ifNull()//卖价线
            mBinding.kCharView.isShowTpLine = KLineDataUtils.userData?.tpLineDisplay.ifNull()//止盈线
            mBinding.kCharView.isShowSlLine = KLineDataUtils.userData?.slLineDisplay.ifNull()//止损线
            mBinding.kCharView.isDrawPositionLine = KLineDataUtils.userData?.positionLineDisplay.ifNull()//订单线
            if (it.isEmpty()) {
                selectInterval(mViewModel.selectedInterval, false)
            } else {
                setMoreInterval(mViewModel.selectedInterval)
                intervalAdapter.changeSelected(mViewModel.selectedInterval)
                //隐藏十字线和十字线tips
                mBinding.kCharView.setShowCrossLineUntilTouch(false)
                kLineChartTipFloatBinding?.clChartTipFloat?.isVisible = false
                handleNewData(it)
            }
        }
        // 空布局显示
        mViewModel.noDataLiveData.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "noDataLiveData 竖屏无数据 $it")
            mBinding.mVsNoData.isVisible = it
            firstLoaded = true
        }
        // K线数据
        mViewModel.newListLiveData.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "newListLiveData 竖屏首次或切换时间 ${it.size} isSwitching:${mViewModel.isSwitching}")
            mBinding.kCharView.setDigit(mViewModel.data?.digits.ifNull(2))
            updateProdInfo()//切换产品时，更新k线数据与设置买卖价数据使用同一时机触发
            handleNewData(it)
        }
        // 利用行情数据画出下一根蜡烛图
        mViewModel.drawNextDataLiveData.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "drawNextDataLiveData 竖屏行情数据画图")
            drawNextCandle()
        }
        // 分页加载请求数据
        mViewModel.addListLiveData.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            hideLoadDialog()
            val data = it
            mViewModel.isEnableLoadMore = data.isNotEmpty()
            LogUtil.v(TAG, "addListLiveData 竖屏分页请求完成 ${data.size}")
            mBinding.kCharView.setKlineOption(KLineDataUtils.userDataKV.getKlineSetting())
            mAdapter.step = mViewModel.getSecondFromInterval()
            mAdapter.addData(data)
        }
        // 自动刷新数据 5 条，只校验数据，不保存
        mViewModel.autoRefreshLiveData.observe(viewLifecycleOwner) { data ->
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "autoRefreshLiveData 竖屏自动刷新 ${data.size}")
            mBinding.kCharView.setKlineOption(KLineDataUtils.userDataKV.getKlineSetting())
            if (data.isNotEmpty()) {
                mAdapter.verifyData(data)
            }
        }
        mViewModel.tokenErrorLiveData.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            showTokenErrorDialog(it.ifNull())
        }
        mViewModel.resetLineLiveData.observe(viewLifecycleOwner) {isReloadOrderData ->
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            if(isReloadOrderData) {
                mViewModel.initOrderData()
                mBinding.kCharView.orderData = mViewModel.getOrderData()
            }
            mBinding.kCharView.resetMoveAbelLine()
        }

        // 行情刷新
        activityViewModel.refreshCallBack.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            updateProdInfo()
            refreshPriceChange()
            getPriceChangeDialog()?.refresh()
        }
        // 分享
        activityViewModel.shareLiveData.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            isShareShow = it.ifNull()
        }
        // 交易情绪
        mViewModel.tradeEmotionUiState.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            when (it) {
                is UIState.Success -> {
                    val bean = it.data
                    if (bean != null) {
                        mBinding.clTradeSentiment.isVisible = true
                        val sellMarkupOrder = bean.sellMarkupOrder.toIntCatching()
                        if (sellMarkupOrder >= 0 && sellMarkupOrder <= 100) {
                            val progress = (100 - sellMarkupOrder) / 100f
                            val constraintSet = ConstraintSet()
                            constraintSet.clone(mBinding.clPercent)
                            constraintSet.constrainPercentWidth(mBinding.viewSell.id, if(progress == 1f) 0.99f else progress)
                            constraintSet.applyTo(mBinding.clPercent)
                            mBinding.tvSentimentSellValue.text = "${100 - sellMarkupOrder}%"
                            mBinding.tvSentimentBuyValue.text = "$sellMarkupOrder%"
                        }
                    }
                }

                is UIState.Error -> {
                    mBinding.clTradeSentiment.isVisible = false
                    ToastUtil.showToast(it.errMsg)
                }

                else -> {}
            }
        }
        // 交易价格变动 - 更多
        mViewModel.priceChangeDataMore.observe(viewLifecycleOwner) {
            if (!mViewModel.isScreenPortrait) {
                return@observe
            }
            getPriceChangeDialog()?.fillData(it)
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvDragTip.clickNoRepeat {
            mBinding.tvDragTip.isVisible = false
        }
        // 价格提醒
        mBinding.llAlert.setOnClickListener {
            if (!UserDataUtil.isLogin()) {
                openActivity(LoginActivity::class.java)
                return@setOnClickListener
            }
            fun alert() {
                openActivity(PriceAlterListActivity::class.java, Bundle().apply {
                    putString(Constants.PARAM_PRODUCT_NAME, mViewModel.symbol)
                })
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { alert() }) return@setOnClickListener
            }
            alert()
        }
        // 监听NestedScrollView滑动高度 显示标题
        mBinding.mNestedScrollView.setOnScrollChangeListener(object : NestedScrollView.OnScrollChangeListener {
            override fun onScrollChange(v: NestedScrollView, scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int) {
                val isShow = scrollY >= titleToggleTop
                activityViewModel.showTitleLiveData.value = isShow
                isShowTitle = isShow
            }
        })
        mBinding.mVsNoData.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
                vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(requireContext(), R.attr.icNoConnection))
                vs.mNoDataView.setHintMessage(getString(R.string.something_went_wrong_try_again))
                vs.mNoDataView.setBottomBtnText(getString(R.string.try_again))
                vs.mNoDataView.setBottomBtnViewClickListener {
                    // 点击重试
                    resetRequest(true)
                }
            }
        })
        //下拉刷新
        mBinding.smartRefreshLayout.setOnRefreshListener { refreshLayout: RefreshLayout ->
            refreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
            resetRequest(false)
        }
        // 图表时间切换
        intervalAdapter.setOnItemClickListener { _, _, position ->
            val interval = intervalAdapter.getItem(position)
            intervalAdapter.changeSelected(interval)
            selectInterval(interval, true)
        }
        // 图表类型切换
        chartTypeAdapter.setOnItemClickListener { _, _, position ->
            val item = chartTypeAdapter.getItem(position)
            val type = item.mainType
            when (type) {
                is KlineMainEnum -> {// 主图
                    addOrRemoveKMain(type)
                    indicatorBuryPoint("Main", type.getShowName())
                }

                is KlineOtherEnum -> {// 副图
                    addOrRemoveKSub(type)
                    indicatorBuryPoint("Sub", type.getShowName())
                }
            }
        }
        // interval 更多
        footerView.root.setOnClickListener {
            mBinding.llMoreInterval.isVisible = !isShowMore
            footerView.ivArrow.rotation = if (isShowMore) {
                0f
            } else {
                mBinding.kCharView.resetMoveAbelLine()
                180f
            }
            isShowMore = !isShowMore
        }
        mBinding.tv5M.setOnClickListener {
            selectInterval("5m", true)
        }
        mBinding.tv30M.setOnClickListener {
            selectInterval("30m", true)
        }
        mBinding.tv4H.setOnClickListener {
            selectInterval("4h", true)
        }
        mBinding.tv1M.setOnClickListener {
            selectInterval("1M", true)
        }

        mBinding.taskView.setOnTouchListener(object : OnTouchListener {
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                if (event?.action == MotionEvent.ACTION_DOWN) {
                    retract()
                }
                return false
            }
        })

        mBinding.ivSetting.setOnClickListener {
            showKLineSettingPopUp()
        }

        mBinding.ivSwitchToDrawing.setOnClickListener {
            gotoHKLineScreen(isClickDrawSwitch = true)
        }

        mBinding.tvTradeSentiment.setOnClickListener {
            tradeSentimentTipDialog.show()
        }

        mBinding.tvPriceChangeMore.setOnClickListener {
            mBinding.kCharView.resetMoveAbelLine()
            fun more() {
                val priceChangeDialog = BottomKLinePriceChangeDialog.Builder(requireActivity())
                    .setData(mViewModel.data)
                    .build()
                priceChangeDialog.show()
                wrPriceChangeDialog = WeakReference<BottomKLinePriceChangeDialog>(priceChangeDialog)
                mViewModel.tradeOrderTradePriceChange(true)
                // 埋点
                SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_PRICE_CHANGE_CLICK)
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { more() }) return@setOnClickListener
            }
            more()
        }

        // SELL 下单
        mBinding.llBid.setOnClickListener {
            orderBuryPoint("sell")
            if ("0" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_is_untradable))
                return@setOnClickListener
            }
            if ("1" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_can_only_be_closed))
                return@setOnClickListener
            }
            if (mViewModel.data?.marketClose == true) {
                ToastUtil.showToast(getString(R.string.trading_disabled))
                return@setOnClickListener
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { startNewOrderActivity(1) }) return@setOnClickListener
            }
            startNewOrderActivity(1)
        }

        // BUY 下单
        mBinding.llAsk.setOnClickListener {
            orderBuryPoint("buy")
            if ("0" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_is_untradable))
                return@setOnClickListener
            }
            if ("1" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_can_only_be_closed))
                return@setOnClickListener
            }
            if (mViewModel.data?.marketClose == true) {
                ToastUtil.showToast(getString(R.string.trading_disabled))
                return@setOnClickListener
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { startNewOrderActivity(0) }) return@setOnClickListener
            }
            startNewOrderActivity(0)
        }
        mBinding.ivSwitch.clickNoRepeat {
            gotoHKLineScreen()
        }
    }

    private fun showDragTip() {
        if (mBinding.tvDragTip.isVisible.not() && isShowDragTip.not()) {
            isShowDragTip = true
            mBinding.tvDragTip.isVisible = true
        }
    }

    private fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(requireActivity())
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnSingleButtonListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    private fun gotoHKLineScreen(isClickDrawSwitch: Boolean = false) {
        noRepeat(2000) {
            KLineDataUtils.isJumpToHKLine = true
            KLineDataUtils.isStartActivity = true
            if (HttpUrl.isSwitchTradingView && SpManager.getSelectTradingViewMode(false) && !isClickDrawSwitch) {
                SpManager.putSelectTradingViewMode(true)
                val intent = Intent(context, ChartCandleLandscapeActivity::class.java)
                // 同步来自Lite模式的LineExtra设置
                val userDataTV = TradingViewSettingData.getHistoryData()
                val lineExtra = userDataTV.line
                if (lineExtra != null) {
                    lineExtra.ask?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.askLineDisplay) 1 else 0
                    lineExtra.bid?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.bidLineDisplay) 1 else 0
                    lineExtra.tp?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.tpLineDisplay) 1 else 0
                    lineExtra.sl?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.slLineDisplay) 1 else 0
                    lineExtra.position?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.positionLineDisplay) 1 else 0
                    userDataTV.save()
                }
                intent.putExtra(Constants.PARAM_PRODUCT_NAME, mViewModel.data?.symbol.ifNull())
                val data = mViewModel.data
                intent.putExtra("product_name_cn", data?.symbol)
                intent.putExtra("chart_period", mViewModel.period)
                intent.putExtra("chart_period_position", mViewModel.intervalToPosition(mViewModel.selectedInterval))
                intent.putExtra("chart_digits", ChartUIParamUtil.digits)
                intent.putExtra("chart_season", Constants.season)
                startActivityForResult(intent, 0)
            } else {
                SpManager.putSelectTradingViewMode(false)
                val maxSize = mViewModel.newListLiveData.value?.size ?: 0
                val landChartData = if (maxSize == 0) {
                    mAdapter.datas
                } else {
                    mAdapter.datas.takeLast(maxSize)
                }
                mViewModel.klineScale = mBinding.kCharView.getmScaleX()
                mViewModel.isScreenPortrait = false
                requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                mViewModel.landScreenChangeRefreshLiveData.postValue(landChartData)
            }
            KLineDataUtils.isFrontPortrait = false
            mViewModel.isClickDrawSwitch = isClickDrawSwitch

            // 埋点
            SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_SWITH_LANDSCAPE_BTN_CLICK)
        }
    }

    private fun handleNewData(data: List<KLineEntity>) {
        hideLoadDialog()
        mBinding.kCharView.setKlineOption(KLineDataUtils.userDataKV.getKlineSetting())
        mAdapter.step = mViewModel.getSecondFromInterval()
        mAdapter.setDatas(data, true)
        mBinding.kCharView.setMainType(if (mViewModel.isTimeShare) MainDraw.TYPE_MAIN_MINUTE else MainDraw.TYPE_MAIN_CANDLE)
        setSwitchIconPosition(mViewModel.isTimeShare)
        mBinding.chartTypeRecyclerView.isVisible = !mViewModel.isTimeShare
        mBinding.splitKLine.isVisible = !mViewModel.isTimeShare
        changeKChartSize()
        updateKlineChild()
        updateKlineMainChild()
        initTimeShareChart()
    }

    // 跳转下单页面
    private fun startNewOrderActivity(direction: Int) {
        if (!UserDataUtil.isLogin()) {
            openActivity(LoginActivity::class.java)
            return
        }
        val askLogBundle = Bundle()
        askLogBundle.putString("Trade", if (direction == 0) "Buy" else "Sell")
        askLogBundle.putString("Account", if (UserDataUtil.isDemoAccount()) "Demo" else "Live")
        LogEventUtil.setLogEvent("KLine_Trade", askLogBundle)
//        // 神策埋点，按钮点击事件
//        sensorsTrack(true, if (direction == 0) "Buy" else "Sell")

        if (UserDataUtil.isRebateAccount()) {
            activity?.let {
                CenterActionDialog.Builder(it)
                    .setContent(getString(R.string.you_must_be_trade))
                    .setEndText(getString(R.string.log_in))
                    .setOnEndListener {
                        val bundle = Bundle()
                        bundle.putInt(Constants.IS_FROM, 2)
                        startActivity(Intent(activity, AccountManagerActivity::class.java).putExtras(bundle))
                    }
                    .build()
                    .showDialog()
            }
            return
        }
        OrderViewModel.openOrder(requireActivity(),mViewModel.data?.symbol?:"",direction.toString(), checkReadOnly = SpManager.isV1V2().not())
        ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
    }

    private fun showKLineSettingPopUp() {
        mBinding.kCharView.resetMoveAbelLine()
        settingDialog = BottomKLineSettingsDialog.Builder(requireActivity()).build()
        settingDialog?.newGuideClick {
            showNewGuideDialog()
            val accountType = if (!UserDataUtil.isLogin()) {
                BuryPointConstant.AccountType.NOLOGIN
            } else if (UserDataUtil.isStLogin()) {
                BuryPointConstant.AccountType.COPY_TRADING
            } else if (UserDataUtil.isDemoAccount()) {
                BuryPointConstant.AccountType.DEMO
            } else {
                BuryPointConstant.AccountType.LIVE
            }

            LogEventUtil.setLogEvent(
                BuryPointConstant.V3474.TRADE_KLINE_USER_GUIDE_BUTTON_CLICK,
                bundleOf(Pair<String, String>("Type_of_account", accountType), Pair<String, String>("Mode", "Lite-vertical-settings"))
            )
        }

        settingDialog?.onSelectLineListener { position: Int, isSelect: Boolean ->
            var selectline = ""
            var toggle = ""
            val bundle = Bundle()
            when (position) {
                0 -> {
                    mBinding.kCharView.isShowBuyLine = isSelect
                    KLineDataUtils.userData?.askLineDisplay = isSelect

                    selectline = "Ask"
                    toggle = if (isSelect) "On" else "Off"
                }

                1 -> {
                    mBinding.kCharView.isShowSellLine = isSelect
                    KLineDataUtils.userData?.bidLineDisplay = isSelect

                    selectline = "Bid"
                    toggle = if (isSelect) "On" else "Off"
                }

                2 -> {
                    mBinding.kCharView.isDrawPositionLine = isSelect
                    KLineDataUtils.userData?.positionLineDisplay = isSelect

                    selectline = "Open"
                    toggle = if (isSelect) "On" else "Off"
                }

                3 -> {
                    mBinding.kCharView.isShowTpLine = isSelect
                    KLineDataUtils.userData?.tpLineDisplay = isSelect

                    selectline = "TP"
                    toggle = if (isSelect) "On" else "Off"
                }

                4 -> {
                    mBinding.kCharView.isShowSlLine = isSelect
                    KLineDataUtils.userData?.slLineDisplay = isSelect

                    selectline = "SL"
                    toggle = if (isSelect) "On" else "Off"
                }

                else -> {}
            }

            bundle.putString("Account_type", KLineActivity.getPointAccountType())
            bundle.putString("Mode", "Lite-vertical")
            bundle.putString("Line", selectline)
            bundle.putString("Toggle", toggle)
            LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_SETTINGS_LINE_BUTTON_CLICK, bundle)
        }

        settingDialog?.show()
        // 埋点
        SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_SETTING_BTN_CLICK)
    }

    private fun showNewGuideDialog() {
        bottomKLineNewGuideDialog =  BottomKLineNewGuideDialog.Builder(requireActivity()).build()
        bottomKLineNewGuideDialog?.show()
    }

    private fun changeChartInterval(interval: String, isIntervalItemClick: Boolean = false) {
        // 如果和上次点击条目一样直接返回
        if (isIntervalItemClick && interval == mViewModel.selectedInterval) {
            return
        }
        mViewModel.selectedInterval = interval
        mViewModel.isTimeShare = interval == "Tick"
        //隐藏十字线和十字线tips
        mBinding.kCharView.setShowCrossLineUntilTouch(false)
        kLineChartTipFloatBinding?.clChartTipFloat?.isVisible = false
        // 重置isEnableLoadMore状态
        mViewModel.isEnableLoadMore = true
        SpManager.putChartTypeText(interval)
        mViewModel.stopTimerRequest()
        mViewModel.switchChartPeriod(true)
    }

    private fun selectInterval(interval: String, isIntervalItemClick: Boolean = false) {
        mBinding.tv5M.setBackgroundResource(
            if (interval == "5m") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        mBinding.tv30M.setBackgroundResource(
            if (interval == "30m") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        mBinding.tv4H.setBackgroundResource(
            if (interval == "4h") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        mBinding.tv1M.setBackgroundResource(
            if (interval == "1M") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        setMoreInterval(interval)
        // 切换加载数据
        changeChartInterval(interval, isIntervalItemClick)
        // 埋点
        if (interval != "Tick") {
            SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_TIME_BTN_CLICK, JSONObject().apply {
                put("time_interval", interval)
                put(SensorsConstant.Key.SCREEN_ORIENTATION, "Portrait")
            })
        }
    }

    private fun retract() {
        // 收起
        mBinding.llMoreInterval.isVisible = false
        footerView.ivArrow.rotation = 0f
        isShowMore = false
    }

    private fun setMoreInterval(interval: String) {
        // 收起
        retract()
        // 赋值
        val isSelectedMore = interval in mViewModel.moreIntervalList
        intervalAdapter.changeSelected(interval)    // 同步选中状态
        if (isSelectedMore) {
            footerView.tvTab.text = interval
            footerView.tvTab.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff))
            footerView.tvTab.setFontG600()
        } else {
            footerView.tvTab.text = getString(R.string.more)
            footerView.tvTab.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_ca61e1e1e_c99ffffff))
            footerView.tvTab.setFontG500()
        }
    }

    private fun addOrRemoveKMain(klineMainEnum: KlineMainEnum) {
        if (mViewModel.mainIndicatorList.contains(klineMainEnum)) {
            mViewModel.mainIndicatorList.remove(klineMainEnum)
        } else {
            if (mViewModel.mainIndicatorList.size >= 2) {
                mViewModel.mainIndicatorList.removeAt(0)
            }
            mViewModel.mainIndicatorList.add(klineMainEnum)
        }
        chartTypeAdapter.changeMainSelected(mViewModel.mainIndicatorList)
        updateKlineMainChild()
    }

    private fun setSwitchIconPosition(isTimeShare: Boolean) {
        var pos = 260.dp2px()
        if (isTimeShare) {
            pos += 110.dp2px()
        }
        val lp = mBinding.ivSwitch.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topMargin = pos
        mBinding.ivSwitch.layoutParams = lp
    }

    private fun changeKChartSize() {
        val childH = resources.getDimension(com.example.myapplication.R.dimen.child_height)
        var contentHeight = 400.dp2px()
        val lp = mBinding.kCharView.layoutParams
        if (mViewModel.isTimeShare) {
            if (mViewModel.subIndicatorList.size > 0) {
                contentHeight += 30.5.dp2px().toInt()//分时线显示时会隐藏chartTypeRecyclerView，分时线与k线高度不一致，这里加上chartTypeRecyclerView的高度
            } else {
                contentHeight = lp.height + 30.5.dp2px().toInt()
            }
        } else {
            if (mViewModel.subIndicatorList.size > 1) {
                contentHeight += ((mViewModel.subIndicatorList.size - 1) * childH).toInt()
            } else if (mViewModel.subIndicatorList.size < 1) {
                contentHeight -= childH.toInt()
            }
        }
        lp.height = contentHeight
        mBinding.kCharView.layoutParams = lp
    }

    private fun addOrRemoveKSub(klineOtherEnum: KlineOtherEnum) {
        if (mViewModel.subIndicatorList.contains(klineOtherEnum)) {
            mViewModel.subIndicatorList.remove(klineOtherEnum)
        } else {
            if (mViewModel.subIndicatorList.size >= 4) {
                mViewModel.subIndicatorList.removeAt(0)
            }
            mViewModel.subIndicatorList.add(klineOtherEnum)
        }
        chartTypeAdapter.changeSubSelected(mViewModel.subIndicatorList)
        changeKChartSize()
        updateKlineChild()
    }

    private fun updateProdInfo() {
        //todo InitHelper.isNotSuccess()判断有问题，此处不应使用公共数据初始化完成进行判断。
        // 出现case：当网速慢，处于连接中，产品列表已经请求完成，但是后续步骤正在进行中。
        // 此时点击行情列表，进入k线页面，会在此处拦截，导致顶部价格数据未设置，公共数据中
        if (InitHelper.isNotSuccess() || mViewModel.isSwitching) return
        val dataBean = mViewModel.data ?: return
        mBinding.run {
            val bid = if (dataBean.bidUI == "-") Constants.DOUBLE_LINE else dataBean.bidUI
            tvBid.text = dataBean.bidUI
            tvAsk.text = dataBean.askUI
            tvSpread.text = OrderUtil.getSpread(dataBean.ask, dataBean.bid, dataBean.digits)

            kCharView.setCurrentPrice(dataBean.bid)
            if(dataBean.lasttime != "0") {
                //todo 解决case k线页面切换后台超过一分钟后，再次切到前台，k线最后一根蜡烛拉长的现象。
                // 出现原因：k线页后台超过一分钟回到前台，会触发重连socket，重新加载产品列表公共数据，请求成功后会clear老的公共数据，
                // 并addAll新请求的公共数据，此时接口返回的数据中，lasttime为0，并且originalBid也不是最新的（推测接口返回的是最近所选时间点的价格）。
                // 只有当socket连接成功后，会推送最新lasttime、originalBid等字段，对公共数据进行更新。
                // 而在收到推送前，就进行了400ms刷新k线，使用了错误数据，导致k线拉长。
                // 所以此处增加判断lasttime为0时代表数据有误，不更新最后一根k线
                mAdapter.updateLastData(dataBean.originalBid)
            }
            kCharView.setBuyPrice(dataBean.originalAsk)
            kCharView.setSellPrice(dataBean.originalBid)

            tvBoardSellPrice.text = bid
            tvBoardHigh.text = dataBean.maxprice.numFormat(dataBean.digits, false)
            tvBoardClose.text = bid
            tvBoardLow.text = dataBean.minprice.numFormat(dataBean.digits, false)
            val rose = dataBean.rose
            val add = if (rose > 0) "+" else ""
            val roseStr = " ($add${dataBean.roseUI}%)"
            val diff = dataBean.diff
            val add2 = if (diff > 0 && dataBean.diffUI != Constants.DOUBLE_LINE) "+" else ""
            tvBoardDiff.text = add2 + dataBean.diffUI + roseStr
            activityViewModel.setTextColorByDiff(context, tvBoardDiff, diff.toDouble())
            //交易时间
            if (dataBean.marketClose) { //闭市中
                llBid.background = draw_shape_c731e1e1e_c61ffffff_r8
                llAsk.background = draw_shape_c731e1e1e_c61ffffff_r8

                tvTradeTime.text = getString(R.string.market_closed)
            } else if ("1" == dataBean.enable) {    // 只可平仓
                llBid.background = draw_shape_c731e1e1e_c61ffffff_r8
                llAsk.background = draw_shape_c731e1e1e_c61ffffff_r8
            } else { //交易时间段内
                llBid.background = shape_cf44040_r8
                llAsk.background = shape_c00c79c_r8

                val realTime = dataBean.lasttime
                if ("0" == realTime) {
                    tvTradeTime.visibility = View.INVISIBLE     // 因右侧开、收盘指数要根据这个TextView位置对齐显示
                } else {
                    tvTradeTime.visibility = View.VISIBLE
                    val formatTime = TimeUtil.getKlineTradeDate(realTime, "MM/dd HH:mm:ss")
                    tvTradeTime.text = Formatter(Locale.ENGLISH).format(getString(R.string.trading) + " (%s GMT+%d)", formatTime, Constants.season).toString()
                }
            }
            initTimeShareChart()
        }
    }

    /**
     * 涨跌额、涨跌幅、振幅
     */
    private fun updateChartTip (chartsBean: KLineEntity?){
        if (chartsBean == null) {
            kLineChartTipFloatBinding?.tvChartTipChangeAmountValue?.text = "--"
            kLineChartTipFloatBinding?.tvChartTipChangePercentValue?.text = "--"
            kLineChartTipFloatBinding?.tvChartTipChangeAmountValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            kLineChartTipFloatBinding?.tvChartTipChangePercentValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            kLineChartTipFloatBinding?.tvChartTipAmplitudeValue?.text = "0.0"
            return
        }
        val closeValue = chartsBean.close.toString().ifNull()
        val prevCloseValue = chartsBean.prevClose?.toString().ifNull()
        val highValue = chartsBean.high.toString().ifNull()
        val lowValue = chartsBean.low.toString().ifNull()
        val openValue = chartsBean.open.toString().ifNull()
        if (prevCloseValue.isNotEmpty()) {
            val difference = closeValue.mathSub(prevCloseValue)//收盘价-前周期收盘价 或 最新价-前周期收盘价，**这里收盘价和最新价统一使用了close字段，drawNextCandle 和 updateProdInfo方法中有对close设置最新价逻辑**
            //涨跌额： 历史涨跌额：收盘价-前周期收盘价；当前涨跌额：最新价-前周期收盘价。
            val changeAmountValue = difference.numFormat(ChartUIParamUtil.digits, true)
            kLineChartTipFloatBinding?.tvChartTipChangeAmountValue?.text = changeAmountValue
            //涨跌幅： 历史涨跌幅：(收盘价-前周期收盘价)/前周期收盘价*100%； 当前涨跌幅：(最新价-前周期收盘价)/前周期收盘价*100%
            val changeNum = difference.mathDiv(prevCloseValue, 4).mathMul("100")
            val changeStr = changeNum.formatProductPrice(2, false, "0.00") + "%"
            kLineChartTipFloatBinding?.tvChartTipChangePercentValue?.text = changeStr
            val differenceNum = difference.toDoubleCatching()
            kLineChartTipFloatBinding?.tvChartTipChangeAmountValue?.setTextColorDiff(
                when {
                    differenceNum > 0 -> ChartUIParamUtil.candleUpColor
                    differenceNum < 0 -> ChartUIParamUtil.candleDownColor
                    else -> ChartUIParamUtil.candleMaxMinTextColor
                }
            )
            val changeNumDouble = changeNum.toDoubleCatching()
            kLineChartTipFloatBinding?.tvChartTipChangePercentValue?.setTextColorDiff(
                when {
                    changeNumDouble > 0 -> ChartUIParamUtil.candleUpColor
                    changeNumDouble < 0 -> ChartUIParamUtil.candleDownColor
                    else -> ChartUIParamUtil.candleMaxMinTextColor
                }
            )
        } else {
            kLineChartTipFloatBinding?.tvChartTipChangeAmountValue?.text = "--"
            kLineChartTipFloatBinding?.tvChartTipChangePercentValue?.text = "--"
            kLineChartTipFloatBinding?.tvChartTipChangeAmountValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            kLineChartTipFloatBinding?.tvChartTipChangePercentValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
        }
        //振幅： 历史振幅：(同一周期最高价-同一周期最低价)/同一周期开盘价*100%；  当前振幅：(当前周期最高价-当前周期最低价)/当前周期开盘价*100%
        val amplitude = if (openValue.toDoubleCatching() != 0.0) {
            (highValue.mathSub(lowValue)).mathDiv(openValue, 4)
        } else {
            "0.0"
        }
        val amplitudeStr = (amplitude.mathMul("100")).formatProductPrice(2, false, "0.00") + "%"
        kLineChartTipFloatBinding?.tvChartTipAmplitudeValue?.text = amplitudeStr
    }

    private fun refreshPriceChange() {
        val dataBean = mViewModel.data
        val data = mViewModel.priceChangeData
        val avaiable = dataBean != null && data != null
        if (avaiable) {
            // 1D
            val close1D = data.yesterdayClose.toDoubleCatching(-1.0)
            if (close1D == -1.0) {
                mBinding.tv1DValue.text = Constants.DOUBLE_LINE
                mBinding.tv1DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            } else {
                val diff = dataBean.bid - close1D
                val rate = diff.div(close1D).times(100)
                mBinding.tv1DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                activityViewModel.setTextColorByDiff(context, mBinding.tv1DValue, diff.toDouble())
            }
            // 7D
            val close7D = data.weekClose.toDoubleCatching(-1.0)
            if (close7D == -1.0) {
                mBinding.tv7DValue.text = Constants.DOUBLE_LINE
                mBinding.tv7DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            } else {
                val diff = dataBean.bid - close7D
                val rate = diff.div(close7D).times(100)
                mBinding.tv7DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                activityViewModel.setTextColorByDiff(context, mBinding.tv7DValue, diff.toDouble())
            }
            // 30D
            val close30D = data.monthClose.toDoubleCatching(-1.0)
            if (close30D == -1.0) {
                mBinding.tv30DValue.text = Constants.DOUBLE_LINE
                mBinding.tv30DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            } else {
                val diff = dataBean.bid - close30D
                val rate = diff.div(close30D).times(100)
                mBinding.tv30DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                activityViewModel.setTextColorByDiff(context, mBinding.tv30DValue, diff.toDouble())
            }
        } else {
            mBinding.tv1DValue.text = Constants.DOUBLE_LINE
            mBinding.tv1DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            mBinding.tv7DValue.text = Constants.DOUBLE_LINE
            mBinding.tv7DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            mBinding.tv30DValue.text = Constants.DOUBLE_LINE
            mBinding.tv30DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
        }
    }

    // 利用行情数据画出下一根蜡烛图
    private fun drawNextCandle() {
        val dataBean = mViewModel.data
        if (dataBean?.marketClose == false) {
            mAdapter.drawNextData(dataBean.originalBid, mViewModel.period)
        }
    }

    private fun resetRequest(loading: Boolean, isRequestChart: Boolean = true, isSwitchingSymbol: Boolean = false) {
        if (isRequestChart) {
            mViewModel.stopTimerRequest()
            mViewModel.switchChartPeriod(loading, isSwitchingSymbol)
        }
        mViewModel.tradeOrderTradeEmotion()
        mViewModel.tradeOrderTradePriceChange()
    }

    private fun getPriceChangeDialog(): BottomKLinePriceChangeDialog? {
        return wrPriceChangeDialog?.get()
    }

    private fun showCenterActionDialog(
        title: String? = null, content: CharSequence?, startText: CharSequence?,
        endText: CharSequence?, startAction: (() -> Unit)? = null, endAction: (() -> Unit)? = null
    ) {
        val builder = CenterActionDialog.Builder(requireActivity())
        if (!title.isNullOrEmpty()) {
            builder.setTitle(title)
        }
        builder.setContent(content)
            .setStartText(startText)
            .setEndText(endText)
            .setOnStartListener {
                startAction?.invoke()
            }.setOnEndListener {
                endAction?.invoke()
            }.build().showDialog()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventBus(tag: String) {
        LogUtil.v(TAG, "onEventBus 竖屏-tag:$tag")
        when {
            NoticeConstants.SWITCH_ACCOUNT == tag -> {// 切换账户时关闭页面(登陆)
                activity?.finish()
            }

            NoticeConstants.Init.DATA_SUCCESS_ORDER == tag -> {// 持仓列表更新
                LogUtil.v(TAG, "竖屏-刷新持仓订单列表 isScreenPortrait:${mViewModel.isScreenPortrait}")
                if (!mViewModel.isScreenPortrait) {
                    return
                }
                mViewModel.initOrderData()// 刷新持仓订单列表
                mBinding.kCharView.orderData = mViewModel.getOrderData()
                mBinding.kCharView.resetMoveAbelLine()
            }

            NoticeConstants.Init.DATA_SUCCESS_GOODS == tag -> {
                LogUtil.v(TAG, "竖屏-刷新产品数据 isScreenPortrait:${mViewModel.isScreenPortrait}")
                if (!mViewModel.isScreenPortrait) {
                    return
                }
                mViewModel.updateData()
            }

            NoticeConstants.APP_ON_RESUME == tag && !isShareShow -> {
                if (mViewModel.data?.symbol.isNullOrEmpty()) {
                    activity?.finish()
                }
            }

            // 切换产品
            NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT == tag -> {
                mBinding.tvDragTip.isVisible = false
                mBinding.kCharView.setShowCrossLineUntilTouch(false)
                kLineChartTipFloatBinding?.clChartTipFloat?.isVisible = false
                mViewModel.data = activityViewModel.data
                mViewModel.symbol = activityViewModel.symbol
                mViewModel.initKLineSettingData()
                mViewModel.initDrawData()
                firstLoaded = false
                resetRequest(true, isSwitchingSymbol = true)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshChart(event: KLineEvent) {
        if (isShareShow) {
            return
        }
        // 分时类型  （分时，1分，5分）
        val interval = mViewModel.positionToInterval(event.periodPosition)  // 因为这里涉及到 原生K线和TradingView共同返回的处理（position），所以先不改
        LogUtil.v(TAG, "refreshChart请求 interval:$interval")
        selectInterval(interval, false)
        // 同步来自TradingView的LineExtra的设置
        if (event.userDataTV != null) {
            val userDataTV = event.userDataTV
            val lineExtra = userDataTV.line
            if (KLineDataUtils.userData != null) {
                KLineDataUtils.userData.askLineDisplay = lineExtra?.ask?.status == 1
                KLineDataUtils.userData.bidLineDisplay = lineExtra?.bid?.status == 1
                KLineDataUtils.userData.tpLineDisplay = lineExtra?.tp?.status == 1
                KLineDataUtils.userData.slLineDisplay = lineExtra?.sl?.status == 1
                KLineDataUtils.userData.positionLineDisplay = lineExtra?.position?.status == 1
            }
        }
        // 因为TradingView与竖屏和Lite横屏主副图类型不匹配，所以TradingView返回会传空，在这里不进行赋值处理
    }

    override fun onResume() {
        super.onResume()
        LogUtil.v(TAG, "竖屏onResume")
        mViewModel.isShowDraw = SpManager.getKlineShowDraw()
        mBinding.drawView.isVisible = mViewModel.isShowDraw
        mBinding.kCharView.setAllowTimerInvalidate(true)
        if (isShareShow || mViewModel.isSwitching) {
            return
        }
        KLineDataUtils.isFrontPortrait = true
        resetRequest(!firstLoaded, isRequestChart = false)//这里不用请求k线数据，k线数据在横竖屏切换liveData或者activity onResume中有请求
    }

    override fun onPause() {
        super.onPause()
        LogUtil.v(TAG, "竖屏onPause")
        mBinding.kCharView.resetMoveAbelLine()
        mBinding.kCharView.setAllowTimerInvalidate(false)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        LogUtil.v(TAG, "竖屏onDestroyView")
        SpManager.putKlineScale(mBinding.kCharView.getmScaleX())
        EventBus.getDefault().unregister(this)
    }

    private fun indicatorBuryPoint(category: String?, indicator: String?) {
        SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_METRICS_TAB_CLICK, JSONObject().apply {
            put("metrics_tab", indicator)
        })
    }

    private fun orderBuryPoint(type: String) {
        SensorsDataUtil.track(
            if (type == "sell") {
                SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_SELL_BTN_CLICK
            } else {
                SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_BUY_BTN_CLICK
            }
        )
    }

    private fun sensorsTrack(type: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.ORDER_ID, mViewModel.shareOrderData?.order?:"")
        properties.put(SensorsConstant.Key.ORDER_TYPE, type)
        SensorsDataUtil.track(SensorsConstant.V3610.CANCELTPSL_CONFIRMCLICK, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     */
    private fun sensorsTrack(isClickBtnTrack: Boolean, buttonName: String?) {
        try {
            val properties = JSONObject()
            properties.put(SensorsConstant.Key.TRADE_TYPE, getTradeType()) // 交易类型
            properties.put(SensorsConstant.Key.IS_OPTIONAL, if (activityViewModel.isAddOptional) 1 else 0) // 是否自选
            properties.put(SensorsConstant.Key.PRODUCT_GROUP, "") // 交易产品组
            properties.put(SensorsConstant.Key.PRODUCT_SYMBOL, mViewModel.data?.symbol) // 交易产品
            if (isClickBtnTrack) {
                properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
                properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
                properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
                properties.put(SensorsConstant.Key.MKT_ID, "") // 素材id
                properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
                properties.put(SensorsConstant.Key.MKT_RANK, "") // 素材排序
                properties.put(SensorsConstant.Key.TARGET_URL, "") // 跳转链接
                // 交易产品详情页点击 -> 交易产品详情页点击时触发
                SensorsDataUtil.track(SensorsConstant.V3500.PRODUCT_DETAIL_PAGE_CLICK, properties)
            } else {
                // 交易产品详情页浏览 -> 交易产品详情页加载完成时触发
                SensorsDataUtil.track(SensorsConstant.V3500.PRODUCT_DETAIL_PAGE_VIEW, properties)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun updateKlineChild() {
        if (mViewModel.isTimeShare) {
            mBinding.kCharView.setKlineChild(mutableListOf<KlineOtherEnum>())
        } else {
            mBinding.kCharView.setKlineChild(mViewModel.subIndicatorList)
        }
    }

    private fun updateKlineMainChild() {
        if (mViewModel.isTimeShare) {
            mBinding.kCharView.setKlineMainChild(mutableListOf<KlineMainEnum>())
        } else {
            mBinding.kCharView.setKlineMainChild(mViewModel.mainIndicatorList)
        }
    }

    private fun initTimeShareChart() {
        if (!mViewModel.isTimeShare) {
            return
        }
        var rate = 0f
        val dataBean = mViewModel.data
        if (dataBean != null) {
            rate = dataBean.rose
        }
        val color = when {
            rate < 0 -> ce35728
            rate >= 0 -> c00c79c     // 这里先与iOS保持一致，后面由产品决定是否会展示灰色
            else -> color_c731e1e1e_c61ffffff
        }
        mBinding.kCharView.setMinuteWhiteColor(color)//分时线颜色
        mBinding.kCharView.setMinuterStartColor(color)
        mBinding.kCharView.setMinuteStopColor(transparent)
    }

    companion object {
        private const val TAG = "KLineChartNewFragment"
        fun instance(): KLineChartNewFragment = KLineChartNewFragment()
    }
}