package cn.com.vau.data.pricealtert

import android.os.Parcelable
import androidx.annotation.Keep
import cn.com.vau.R
import cn.com.vau.util.UtilApp
import kotlinx.parcelize.Parcelize

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 10 16:11
 * @updateUser:
 * @updateDate: 2024 10月 10 16:11
 */

@Keep
@Parcelize
data class ProduceAlterGroupListData(
    val groupName: String? = "",
    var count: String? = "",
    var list: List<ProduceAlterSymbolListData>? = listOf()
) : Parcelable

@Keep
@Parcelize
data class ProduceAlterSymbolListData(
    var list: List<ProduceAlterData>? = listOf(),
    val login: String? = "",
    val serverId: String? = "",
    val symbol: String? = "",
    val token: String? = "",
    val userId: String? = "",
    var bid: String? = "",
    var ask: String? = "",
    var rate: String? = "",
    var isShowList: Boolean = true
) : Parcelable

@Keep
@Parcelize
data class ProduceAlterData(
    val alertType: String? = null,
    val direction: String? = null,
    var enable: String? = null,
    val id: String? = null,
    val value: String? = null,
    val frequency: String? = null,
    val isExecute: String? = null,
    var isSelect: Boolean = false
) : Parcelable {
    fun getAlertName(): String {
        return when (alertType) {
            "0" -> if (direction == "0") {
                UtilApp.getApp().getString(R.string.buy_price_rises_above_x, value)
            } else {
                UtilApp.getApp().getString(R.string.sell_price_rises_above_x, value)
            }

            "1" -> if (direction == "0") {
                UtilApp.getApp().getString(R.string.buy_price_falls_to_x, value)
            } else {
                UtilApp.getApp().getString(R.string.sell_price_falls_to_x, value)
            }

            "2" -> UtilApp.getApp().getString(R.string._d_rise_exceeds_x, "$value%")
            else -> UtilApp.getApp().getString(R.string._d_fall_exceeds_x, "$value%")
        }
    }

    fun getFrequencyName(): String {
        return when (frequency) {
            "0" -> UtilApp.getApp().getString(R.string.once_only)
            "1" -> UtilApp.getApp().getString(R.string.once_every_24h)
            else -> UtilApp.getApp().getString(R.string.every_time)
        }
    }
}