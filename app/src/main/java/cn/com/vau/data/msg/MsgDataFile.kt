package cn.com.vau.data.msg

import android.os.Parcelable
import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import kotlinx.parcelize.Parcelize

// Type: 消息 / 客服 / 推送 / InApp / 通知

/**
 * 获取客服咨询分类
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSConsultBean : BaseBean() {
    var data: CSConsultData? = null
}

@Keep
class CSConsultData {
    var obj: CSConsultObj? = null
}

@Keep
data class CSConsultObj(
    var items: List<CSConsultItem>?,
    var extInfo: CSExtInfo?,
    var zendeskInfo: ZendeskInfo?,
    var supervision: String? = ""
)

@Keep
data class CSConsultItem(
    var code: String? = null,
    var name: String? = null
)

@Keep
data class CSExtInfo(
    var customerTime: String?,
    var contactUsTime: String?,
    var customWelcom: String?
)

@Keep
data class ZendeskInfo(
    var countryName: String? = "",
    var langCode: String? = "",
    var userJWT: String? = "",
)

@Keep
class ChannelKeyBean : BaseBean() {
    var data: ChannelKeyData? = null
}

@Keep
class ChannelKeyData {
    var obj: ChannelKeyInfo? = null
}

@Keep
data class ChannelKeyInfo(
    var id: String? = "",
    var countryCode: String? = "",
    var keyDetail: String? = "",
    var systemType: String? = ""
)

/**
 * 获取客服联系我们
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSContactusBean : BaseBean() {
    var data: CSContactusData? = null
}

@Keep
class CSContactusData {
    var obj: List<CSContactusObj>? = null
}

@Keep
class CSContactusObj {
    /**
     * area : 上海
     * phones : ************
     * phonelist : ["************"]
     * address : 上海市静安区
     * email : <EMAIL>
     */
    var area: String? = null
    var email: String? = null
    var phonelist: List<String>? = null
}

/**
 * 获取客服问题答案
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSAnswerData {
    var obj: CSAnswerObj? = null
}

@Keep
class CSAnswerObj {
    var quest: String? = null
    var answer: String? = null
}

/**
 * 获取客服问题列表
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSQuestsData {
    var obj: List<CSQuestsObj>? = null
}

@Keep
class CSQuestsObj {
    var id: String? = null
    var quest: String? = null
    var answer: String? = null
    var expanded: Boolean = false
}

/**
 * 其他消息
 * Created by zhy on 2018/12/18.
 */
@Keep
class OtherMsgData {
    var obj: List<OtherMsgObj>? = null
}

@Keep
class OtherMsgObj {
    var id = 0
    var title: String? = null
    var category: String? = null
    var jumpValue: PushBean? = null
    var replyNick: String? = null
    var createTime: String? = null
    var content: List<String>? = null
}

/**
 * 系统消息
 * Created by zhy on 2018/12/18.
 */
@Keep
class SystemMsgData {
    var obj: List<SystemMsgObj>? = null
}

@Keep
class SystemMsgObj {
    var id = 0
    var firstTile: String? = null
    var content: String? = null
    var category: String? = null
    var cateName: String? = null
    var img: String? = null
    var createTime: String? = null
    var jumpValue: PushBean? = null
    var secondTitles: List<String>? = null
}

/**
 * 公告查询
 */
@Keep
data class NoticeData(
    var obj: MutableList<Obj>? = null
) {
    @Keep
    data class Obj(
        var id: Int? = null, // 268
        var openType: Int? = null, // 0
        var params: String? = null, // {"test":"123"}
        var publishTime: String? = null, // 02/11/2023 09:58
        var refDataId: String? = null, // 500
        var title: String? = null, // Dividend Announcement
        var txtContent: String? = null, // Indices Dividends for Period of 5 October to 13 October 2023 Vantage
        var url: String? = null // 15
    )
}

@Keep
data class PriceAlertMsgData(
    var obj: List<Obj>? = null
) {
    @Keep
    data class Obj(
        val title: String? = null,
        val content: String? = null,
        val accountCd: String? = null,
        val createTime: String? = null
    )
}

@Keep
@Parcelize
data class MsgInAppTypeData(
    val obj: List<Obj?>? = null
) : Parcelable {
    @Keep
    @Parcelize
    data class Obj(
        val code: String? = null,
        var count: String? = null,
        val name: String? = null
    ) : Parcelable
}

@Keep
data class NewNoticeData(val obj: List<NoticeBean>)

@Keep
data class NoticeBean(
    val content: String? = null,
    val firstTitle: String? = null,
    val secondTitle: List<String?>? = null,
    val id: String? = null,
    val publishTime: String? = null,
    var read: Boolean? = null,
    val redirectInfo: PushBean? = null
)

@Keep
data class NoticeSettingData(val obj: List<NoticeSettingBean>?)

@Keep
data class NoticeSettingBean(
    val code: String? = null,
    var value: String? = null
)