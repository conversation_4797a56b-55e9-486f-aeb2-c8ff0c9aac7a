package cn.com.vau.data.init

import androidx.annotation.Keep
import java.io.Serializable

/**
 * 持仓订单 跟单
 * 先留着，方便溯源
 */
@Keep
class StShareOrderData : Serializable {

    var bid: Float = 0.0f
    var ask: Float = 0.0f

    var bidType: Int = 0
    var askType: Int = 0

    var isRefresh: Boolean = true
    var isUpdate = false
    var lasttime: String? = "0"
    // 盈亏
    var profit: Double = 0.0

    var digits: Int = 2


    var currentPriceUI: String? = ""
    var profitUI: String? = ""

    // ---------------------------

    var minvolume: Float = 0.0f

    var totalProfit: Double = 0.0

    // 行情推送时时价格，自己赋值
    var closePrice: String? = ""

    // -----------------------------------

    // 订单号
    var stOrder: String? = ""

    // 订单号(展示)
    var order: String? = ""
    var symbol: String? = ""

    /**
     * 0 buy market
     * 1 sell market
     * 2 buy limit
     * 3 sell limit
     * 4 buy stop
     * 5 sell stop
     * 6 buy stop limit
     * 7 sell stop limit
     *
     * 6/7 是 mt5
     */
    var cmd: String? = ""

    // 开仓价格
    var openPrice: String? = ""

    // 下单时间(MT4时区)
    var openTimeStr: String? = ""

    // 止损价格
    var stopLoss: String? = ""

    // 止盈价格
    var takeProfit: String? = ""

    // 手数
    var volume: String? = ""

    // 佣金
    var commission: String? = null

    // 倉息
    var swap: String? = null



    // 订单状态 跟随策略下持仓订单需要，自主交易不会出现 (待平仓：PENDING_CLOSE，待开仓：PENDING_OPEN，已开仓：OPEN)
    var status: String? = ""

}
