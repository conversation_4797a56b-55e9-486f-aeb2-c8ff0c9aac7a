package cn.com.vau.ui.deal.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.liveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.data.depositcoupon.UserAccountData
import cn.com.vau.data.trade.LossOrdersBean
import cn.com.vau.data.trade.TradeLossHistoryBean
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.widget.Transformations
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

class LossOrderViewModel : BaseViewModel() {

    // 账户列表
    var accountList = arrayListOf<UserAccountData.Account>()

    // 亏损订单集合
    var dataList: ArrayList<LossOrdersBean.Obj> = arrayListOf()
    var tradeDataList: ArrayList<TradeLossHistoryBean.TradeData> = arrayListOf()

    // 当前选择账户
    var isSt: Boolean = false
    var currentAccount: String = ""
    var currentAccountName: String = ""
    var currentSelect: Int = 0
    var currentCurrency = UserDataUtil.currencyType()
    var currentAccountServiceId: String = ""
    var socialTradingAccountText: String = ""

    // 优惠券数据
    var couponBean: DepositCouponDetail? = null

    private var accountsParamLiveData = MutableLiveData<String>()
    private var requestBodyParamLiveData = MutableLiveData<RequestBody>()
    private var userCouponParamLiveData = MutableLiveData<HashMap<String, Any>>()
    val tradeLossOrder = MutableLiveData<List<TradeLossHistoryBean.TradeData>?>()

    fun initData() {
        currentAccount = UserDataUtil.accountCd()
        if (UserDataUtil.isStLogin()) {
            currentAccountName = socialTradingAccountText
            currentAccountServiceId = UserDataUtil.serverId()
            isSt = true
            getTradeLossOrder(UserDataUtil.stAccountId())
        } else if (UserDataUtil.isLiveAccount()) {
            currentAccountName = UserDataUtil.accountCd()
            currentAccountServiceId = UserDataUtil.serverId()
            isSt = false
        }
    }

    val userCouponLiveData = Transformations.switchMap(userCouponParamLiveData) {
        liveData {
            val result = try {
                val data = baseService.usercouponUseLossCoupon(it)
                Result.success(data)
            } catch (e: Exception) {
                e.printStackTrace()
                Result.failure(e)
            }
            emit(result)
        }
    }

    val lossOrdersLiveData = Transformations.switchMap(requestBodyParamLiveData) {
        liveData {
            val result = try {
                val data = tradingService.tradeOrdersListV2Api(it)
                Result.success(data)
            } catch (e: Exception) {
                e.printStackTrace()
                Result.failure(e)
            }
            emit(result)
        }
    }

    val accountsLiveData = Transformations.switchMap(accountsParamLiveData) {
        liveData {
            val result = try {
                val data = baseService.getUserAccountData(it)
                Result.success(data)
            } catch (e: Exception) {
                Result.failure(e)
            }
            emit(result)
        }
    }

    fun initAccountList() {
        accountsParamLiveData.value = UserDataUtil.userId()
    }

    fun getTradeLossOrder(accountId: String) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("token", UserDataUtil.loginToken())
        jsonObject.addProperty("accountId", accountId)
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({
            stTradingService.accountLoginWithAccountApi(requestBody)
        }, onSuccess = {
            if (it.isSuccess().not()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            UserDataUtil.setStToken(it.data?.token)     //TODO: wj
            getStLossOrder(it.data?.accountId, it.data?.portfolioId, it.data?.token, couponBean?.receiveTime)
        }, isShowDialog = true)
    }

    private fun getStLossOrder(accountId: String?, portfolioId: String?, token: String?, receiveTime: String?) {
        val paramMap = hashMapOf<String, Any?>().apply {
            put("accountId", accountId.ifNull())
            put("from", "")
            put("portfolioId", portfolioId.ifNull())
            put("to", "")
            put("token", token.ifNull())
            put("couponReceivedDate", receiveTime.ifNull())
        }
        requestNet({
            stTradingService.tradeListDealHistoryLossApi(paramMap)
        }, onSuccess = {
            hideLoading()
            if (it.isSuccess().not()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            tradeLossOrder.value = it.data
        })
    }

    fun onRefreshLossOrders() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("serverId", currentAccountServiceId)
        jsonObject.addProperty("login", currentAccount)
        jsonObject.addProperty("token", UserDataUtil.loginToken())
        jsonObject.addProperty("orderType", "1")
        jsonObject.addProperty("profitType", "2")
        jsonObject.addProperty("excludeType", "1")
        jsonObject.addProperty("sortBy", "profit")
        jsonObject.addProperty("sortDire", "asc")
        jsonObject.addProperty("couponReceivedDate", couponBean?.receiveTime ?: "")
        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())
        val requestBody = dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestBodyParamLiveData.value = requestBody
    }

    fun useLossCoupon(orderNo: String?, orderPnl: String?) {
        val paramMap = hashMapOf<String, Any>().apply {
            put("userToken", UserDataUtil.loginToken())
            put("couponId", couponBean?.couponId ?: "")
            put("userCouponId", couponBean?.userCouponId ?: "")
            put("orderNo", orderNo ?: "")
            put("mt4AccountId", currentAccount)
            put("currency", currentCurrency)
            put("lossAmount", orderPnl ?: "")
        }
        userCouponParamLiveData.value = paramMap
    }

}