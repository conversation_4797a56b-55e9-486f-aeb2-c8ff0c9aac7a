package cn.com.vau.util.opt

import android.util.Log
import cn.com.vau.BuildConfig

class TestElapsedTimeUtil {

    companion object {
        //记录多少组数据
        private val TOTAL_COUNT = 1000.00

        var isPrint = true

        private val list = mutableListOf<Long>()

        //统计一段代码的耗时，调用此方法记录每次的耗时时间
        fun recordNanoTime(nanoTime: Long) {
            if (!BuildConfig.DEBUG) {
                return
            }
            if (list.size <= TOTAL_COUNT) {
                list.add(nanoTime)
                isPrint = true
            } else {
                if (isPrint) { //统计平局值
                    statisticsAve()
                    isPrint = false
                }
            }
        }

        private fun statisticsAve() {
            var sum = 0.0
            list.forEach {
                sum += it
            }
            val ave = sum / TOTAL_COUNT
            xhLoge("对 $TOTAL_COUNT 组数据统计，平局耗时为：${ave / 1000000} ms")
        }

        fun clear() {
            list.clear()
            isPrint = true
        }
    }
}