package cn.com.vau.util.opt;

import android.util.Log;
import android.view.Choreographer;

import cn.com.vau.BuildConfig;

public class FpsCounterUtil {
    private static long lastFrameTimeNanos = 0;
    private int frameCount = 0;
    private static Choreographer choreographer;
    private float fps;

    public static void init() {
        if(!BuildConfig.DEBUG){
            return;
        }
        choreographer = Choreographer.getInstance();
        lastFrameTimeNanos = System.nanoTime();
        choreographer.postFrameCallback(frameCallback);
    }

    private static final Choreographer.FrameCallback frameCallback = new Choreographer.FrameCallback() {
        @Override
        public void doFrame(long frameTimeNanos) {
            //上次回调时间
            if (lastFrameTimeNanos == 0) {
                lastFrameTimeNanos = frameTimeNanos;
                Choreographer.getInstance().postFrameCallback(this);
                return;
            }
            long diff = (frameTimeNanos - lastFrameTimeNanos) / 1_000_000;
            if (diff > 16.6f) {
                //掉帧数
                int droppedFrameCount = (int) (diff / 16.6);
                if (droppedFrameCount >= 5) {
                    LogExtKt.xhLogd(this,"刷新 doFrame: droppedFrameCount=" + droppedFrameCount);
                }
            }
            lastFrameTimeNanos = frameTimeNanos;
            choreographer.postFrameCallback(this);
        }
    };
//
//    public float getFps() {
//        return fps;
//    }
}