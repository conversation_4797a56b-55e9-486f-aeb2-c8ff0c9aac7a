package cn.com.vau.history.data

import android.text.SpannedString
import androidx.annotation.Keep

/**
 * Create data：2025/1/13 14:34
 * @author: Brin
 * Describe:
 */
@Keep
data class CloseDetails(
    var closeTimeStr: String? = "",
    var volume: String?,
    var closePrice: String?,
    var closedPnl: String?,
    val commission: String? = "",        //手续费
    val swap: String? = "",              //隔夜费
    var reason: String?,
    var comment: String?,
    var conditions: String?
)

@Keep
data class PositionDetailWrapper(
    var cmd: Int,
    var totalVolume: String,
    var symbol: String,
    var swap: String,
    var closedPnl: String,
    var openPrice: String,
    var openTimeStr: String,
    var avgClosePrice: String,
    var takeProfit: String,
    var closeTimeStr: String,
    var closeTime: Long,
    var volume: String,
    var closedNetPnl: String,
    var stopLoss: String,
    var commission: String,
    var order: String,
    var partClose: Boolean,
    var closeDetails: ArrayList<CloseDetails>?
)

@Keep
data class HistoryPositionDetailUIHeader(
    val orderType: String = "",
    val orderTypeBackGroundColorInt: Int = 0,
    val symbol: String = "",
    val orderId: String = "",
    val orderStatus: SpannedString = SpannedString(""),
    val closedVolumeTitle: String = "",
    val closedVolume: String = "",
    val closingPnlTitle: String = "",
    val closingPnl: SpannedString = SpannedString(""),
    val netPnlTitle: String = "",
    val netPnl: SpannedString = SpannedString(""),
    val avgClosePriceTitle: String = "",
    val avgClosePrice: String = "",
    val chargesTitle: String = "",
    val charge: String = "",
    val openedTimeTitle: String = "",
    val openedTime: String = "",
    val closedTimeTitle: String = "",
    val closedTime: String = "",
    val detailsTitle: String = "",
    val openPrice: String = "",
    val cmd: String = "",
    val closeTimeLong: String = "",
)

@Keep
data class HistoryPositionDetailItemUiData(
    val timeTitle: String = "",
    val time: String = "",
    val volumeTitle: String = "",
    val volume: String = "",
    val priceTitle: String = "",
    val price: String = "",
    val closingPnlTitle: String = "",
    val closingPnl: SpannedString = SpannedString(""),
    val chargesSwapTitle: String = "",
    val chargesSwap: String = "",
    val reasonTitle: String = "",
    val reason: String = "Manual close",
    val comment: String = "",
    val conditions: String = "",
)

