package cn.com.vau.signals.viewModel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.discover.WebTVObj
import cn.com.vau.util.AppUtil

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 15 10:39
 * @updateUser:
 * @updateDate: 2024 10月 15 10:39
 */
class WebTvViewModel : BaseViewModel() {

    var date: String = ""
    val uiListLiveData: MutableLiveData<ListUIState<List<WebTVObj>?>> by lazy { MutableLiveData() }

    val playDataLiveData: MutableLiveData<WebTVObj> by lazy { MutableLiveData() }

    init {
        refresh()
    }

    fun refresh() {
        date = ""
        queryWebTVList()
    }

    fun loadMore() {
        queryWebTVList()
    }

    private fun queryWebTVList() {
        requestNet({
            val params = HashMap<String, Any>()
            if (!TextUtils.isEmpty(date)) {
                params["date"] = date
            }
            params["timeZone"] = AppUtil.getTimeZoneRawOffsetToHour()
            baseService.webtvList(params)
        }, { dataBean ->
            if (!dataBean.isSuccess()) {
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }
            val objList = dataBean.data?.obj as? ArrayList<WebTVObj>
            if (objList.isNullOrEmpty() && date.isBlank()) {
                uiListLiveData.value = ListUIState.Empty
                return@requestNet
            }
            // 如果date为空 说明是第一次刷新
            if (date.isBlank()) {
                uiListLiveData.value = ListUIState.RefreshSuccess(objList)
            } else {
                if (objList.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.LoadEnd(objList)
                } else {
                    uiListLiveData.value = ListUIState.LoadMoreSuccess(objList)
                }
            }

            if (!objList.isNullOrEmpty()) {
                date = objList.lastOrNull()?.date.toString()
            }
        }, {
            uiListLiveData.value = ListUIState.Error()
        }, isShowDialog = false)
    }

    fun addRecord(videoId: String?) {
        requestNet({
            baseService.webtvAddrecord(map = hashMapOf("videoId" to videoId))
        }, {})
    }

}