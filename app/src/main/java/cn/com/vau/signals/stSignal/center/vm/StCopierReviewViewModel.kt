package cn.com.vau.signals.stsignal.center.vm

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.strategy.StProfileCopyResBean
import cn.com.vau.util.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * author：lvy
 * date：2024/04/16
 * desc：信号源中心 -> CopierReview
 */
class StCopierReviewViewModel : BaseViewModel() {

    var pageNum = 1 // 当前页码
    val pageSize = 10 // 每页请求数量

    private val stUserId by lazy { UserDataUtil.stUserId() }

    val copyPageListLiveData = MutableLiveData<StProfileCopyResBean?>() // 跟单审核列表
    val reqErrLiveData = MutableLiveData<String?>() // 请求失败

    val copyApproveLiveData = MutableLiveData<Int>() // 点击审核通过按钮结果
    val copyRejectLiveData = MutableLiveData<Any?>() // 审核拒绝

    /**
     * 跟单审核列表
     *
     * @param applyStatus PENDING-待审核   APPROVED-审核通过   REJECTED-审核拒绝
     */
    fun stProfileCopyPageListApi(isRefresh: Boolean, applyStatus: String?) {
        if (isRefresh) {
            pageNum = 1 // 下拉刷新，需要重置页数
        } else {
            pageNum++
        }
        requestNet({ stTradingService.strategyCopyMultiAccountPageApi(stUserId, applyStatus, pageNum, pageSize) }, {
            if (it.isSuccess()) {
                copyPageListLiveData.value = it.data
            } else {
                reqErrLiveData.value = it.getResponseMsg()
            }
        })
    }

    /**
     * 审核通过
     */
    fun stProfileCopyApproveApi(followerUserId: String?, followerAccountId: String?) {
        val map = hashMapOf<String, Any?>()
        map["signalUserId"] = stUserId // 信号源的UserId
        map["followerUserId"] = followerUserId // 通过的那个人的userId
        map["followerAccountId"] = followerAccountId // 跟单者的交易账号ID

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.strategyCopyMultiAccountApproveApi(requestBody) }, {
            if (it.isSuccess()) {
                copyApproveLiveData.value = 200
            } else {
                ToastUtil.showToast(it.getResponseMsg())
                when (it.getResponseCode()) {
                    // 全部失败，吐司并跳转到Rejected Tab
                    "10677" -> copyApproveLiveData.value = 10677
                    // 部分失败，吐司并跳转到Approved Tab
                    "10678" -> copyApproveLiveData.value = 10678
                }
            }
        }, isShowDialog = true)
    }

    /**
     * 审核拒绝
     */
    fun stProfileCopyRejectApi(followerUserId: String?, followerAccountId: String?) {
        val map = hashMapOf<String, Any?>()
        map["signalUserId"] = stUserId  // 信号源的UserId
        map["followerUserId"] = followerUserId // 拒绝的那个人的userId
        map["followerAccountId"] = followerAccountId // 跟单者的交易账号ID

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.strategyCopyMultiAccountRejectApi(requestBody) }, {
            if (it.isSuccess()) {
                copyRejectLiveData.value = it.data
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = true)
    }
}