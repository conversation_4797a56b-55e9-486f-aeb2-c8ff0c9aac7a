package cn.com.vau.signals.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.discover.Row
import cn.com.vau.util.LogUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

class LiveListViewModel : BaseViewModel() {

    var pageNum = 1
    var pageSize = 10

    val dataListTotalLiveData by lazy {
        MutableLiveData<Pair<Boolean, ArrayList<Row>>>()
    }
    var isFirstLive: Boolean = false
    var total = 0

    var totalPage = 0

    fun refreshLiveList() {
        LogUtil.v("LiveListFragment", "准备刷新")
        var tempRequestBeforePageNum = pageNum
        pageNum = 1
        viewModelScope.launch {
            val asyncLiveListState = async { getLiveStreamList() }
            val asyncHistoryLiveListState = async { getHistoryAwsLiveStreamListPage(1) }
            val liveListState  = asyncLiveListState.await()
            val historyLiveListState = asyncHistoryLiveListState.await()
            var isSuccess = true
            val resultList = ArrayList<Row>().apply{
                if(liveListState is ListUIState.Error || historyLiveListState is ListUIState.Error) {
                    //失败，展示缓存数据
                    isSuccess = false
                    pageNum = tempRequestBeforePageNum
                    dataListTotalLiveData.value?.second?.let { addAll(it) }
                } else {
                    isSuccess = true
                    //即将开始直播
                    if (liveListState is ListUIState.RefreshSuccess && liveListState.data?.isNotEmpty() == true) {
                        addAll(liveListState.data)
                    }
                    //历史直播
                    if (historyLiveListState is ListUIState.RefreshSuccess && historyLiveListState.data?.isNotEmpty() == true) {
                        addAll(historyLiveListState.data)
                    }
                }
            }
            LogUtil.v("LiveListFragment", "请求结束 resultList:${resultList.size}, isSuccess:$isSuccess")
            dataListTotalLiveData.value = Pair(isSuccess, resultList)
        }
    }

    fun loadMoreLiveList() {
        LogUtil.v("LiveListFragment", "准备加载更多 pageNum:$pageNum, totalPage:$totalPage")
        var currentList = dataListTotalLiveData.value?.second ?: ArrayList()
        if (canLoadMore()) {
            pageNum++
            viewModelScope.launch {
                var isSuccess = false
                try {
                    val historyLiveListState = getHistoryAwsLiveStreamListPage(pageNum)
                    when (historyLiveListState) {
                        is ListUIState.Error -> {
                            LogUtil.v("LiveListFragment", "加载更多-失败")
                            pageNum--
                            isSuccess = false
                        }
                        else -> {
                            if (historyLiveListState is ListUIState.RefreshSuccess && historyLiveListState.data?.isNotEmpty() == true) {
                                currentList = ArrayList(currentList).apply {
                                    addAll(historyLiveListState.data)
                                }
                                LogUtil.v("LiveListFragment", "加载更多-成功 pageNum:$pageNum, 新列表大小：${currentList.size}")
                            } else {
                                LogUtil.v("LiveListFragment", "加载更多-无数据，不处理使用缓存数据展示")
                            }
                            isSuccess = true
                        }
                    }
                    LogUtil.v("LiveListFragment", "加载更多-完成 pageNum:$pageNum, totalPage:$totalPage, currentList:${currentList.size}")
                } catch (e: Exception) {
                    e.printStackTrace()
                    isSuccess = false
                    LogUtil.e("LiveListFragment", "加载更多-异常")
                }
                dataListTotalLiveData.value = Pair(isSuccess, currentList)
            }
        } else {
            dataListTotalLiveData.value = Pair(false, currentList)
        }
    }

    fun canLoadMore(): Boolean {
        return pageNum <= totalPage
    }

    private suspend fun getLiveStreamList() = suspendCancellableCoroutine<ListUIState<ArrayList<Row>?>> { cont ->
        LogUtil.v("LiveListFragment", "准备请求-即将开始直播列表")
        requestNet({
            baseService.getLiveStreamList(UserDataUtil.userId())
        }, onSuccess = {
            LogUtil.v("LiveListFragment", "即将开始直播 请求完成 ${Thread.currentThread().name}")
            if (cont.isCancelled) {
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                cont.resume(ListUIState.Empty)
                return@requestNet
            }
            val resultLiveList = it.data?.obj
            if(resultLiveList.isNullOrEmpty()) {
                cont.resume(ListUIState.Empty)
                return@requestNet
            }
            val liveList = ArrayList<Row>()
            isFirstLive = false
            for (live in resultLiveList) {
                if (live.liveStatus != 1) {
                    if (!isFirstLive) {
                        var titleData = Row(
                            liveStatus = 10,
                            titleType = VauApplication.context.getString(R.string.upcoming_webinar),
                            itemType = 3
                        )
                        liveList.add(titleData)
                        isFirstLive = true
                    }
                    liveList.add(live)
                } else {
                    liveList.add(live)
                }
                live.itemType = 1
            }
            LogUtil.v("LiveListFragment", "即将开始直播 成功 resultLiveList:${resultLiveList.size}, liveList:${liveList.size}")
            cont.resume(ListUIState.RefreshSuccess(liveList))

        }, onError = {
            LogUtil.v("LiveListFragment", "即将开始直播 失败")
            if (cont.isCancelled) {
                return@requestNet
            }
            cont.resume(ListUIState.Error())
        })
    }


    private suspend fun getHistoryAwsLiveStreamListPage(pageNum: Int) = suspendCancellableCoroutine<ListUIState<ArrayList<Row>?>> { cont ->
        LogUtil.v("LiveListFragment", "准备请求-历史直播列表")
        requestNet({
            baseService.getHistoryAwsLiveStreamListPage(UserDataUtil.userId(), pageNum, pageSize)
        }, onSuccess = {
            LogUtil.v("LiveListFragment", "历史直播 请求完成")
            if (cont.isCancelled) {
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                cont.resume(ListUIState.Empty)
                return@requestNet
            }
            val historyData = it.data?.obj
            var resultHistoryList = historyData?.rows as? ArrayList<Row>
            if(historyData == null || resultHistoryList.isNullOrEmpty()) {
                cont.resume(ListUIState.Empty)
                return@requestNet
            }
            var historyList = ArrayList<Row>()
            if (pageNum == 1) {
                total = historyData.total.ifNull()
                getPage()
                var titleData = Row(
                    liveStatus = 10,
                    titleType = VauApplication.context.getString(R.string.past_webinar),
                    itemType = 3
                )
                historyList.add(titleData)
            }
            historyList.addAll(resultHistoryList)
            LogUtil.v("LiveListFragment", "历史直播  成功 pageNum:$pageNum, resultHistoryList:${resultHistoryList.size}, historyList:${historyList.size}")
            cont.resume(ListUIState.RefreshSuccess(historyList))
        }, onError = {
            LogUtil.v("LiveListFragment", "历史直播 失败 pageNum:$pageNum")
            if (cont.isCancelled) {
                return@requestNet
            }
            cont.resume(ListUIState.Error())
        })
    }

    private fun getPage(): Int {
        totalPage = if (total % pageSize == 0) {
            total / pageSize
        } else {
            total / pageSize + 1
        }
        return totalPage
    }
}