package cn.com.vau.signals.viewModel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.discover.NewsLetterObjData
import cn.com.vau.util.AppUtil
import cn.com.vau.util.ifNull

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 15 16:58
 * @updateUser:
 * @updateDate: 2024 10月 15 16:58
 */
class AnalysesViewModel : BaseViewModel() {

    var createTime: String = ""

    val bannerLiveData: MutableLiveData<List<NewsLetterObjData>?> by lazy { MutableLiveData() }
    val uiListLiveData: MutableLiveData<ListUIState<List<NewsLetterObjData>?>> by lazy { MutableLiveData() }

    init {
        refresh()
    }

    /**
     * 刷新
     */
    fun refresh() {
        createTime = ""
        analysesList()
    }

    /**
     * 加载更多
     */
    fun loadMore() {
        analysesList()
    }

    /**
     * 请求数据的方法，初次请求 展示弹窗
     */
    private fun analysesList() {

        requestNet({
            baseService.analysesList(hashMapOf("createTime" to createTime, "timeZone" to AppUtil.getTimeZoneRawOffsetToHour()))
        }, { dataBean ->
            if (!dataBean.isSuccess()) {
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }
            val objList = dataBean.data?.obj as? ArrayList<NewsLetterObjData>
            if (objList.isNullOrEmpty() && createTime.isBlank()) {
                uiListLiveData.value = ListUIState.Empty
                bannerLiveData.value = null
                return@requestNet
            }

            if (createTime.isBlank()) {
                if (objList?.size.ifNull() > 3) {
                    bannerLiveData.value = objList?.subList(0, 3)
                    uiListLiveData.value = ListUIState.RefreshSuccess(objList?.subList(3, objList.size))
                } else {
                    bannerLiveData.value = objList
                }
            } else {
                if (objList.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.LoadEnd(objList)
                } else {
                    uiListLiveData.value = ListUIState.LoadMoreSuccess(objList)
                }
            }
            if (!objList.isNullOrEmpty()) {
                createTime = objList.lastOrNull()?.createTime.toString()
            }
        }, {
            uiListLiveData.value = ListUIState.Error()
        }, isShowDialog = false)

    }

}