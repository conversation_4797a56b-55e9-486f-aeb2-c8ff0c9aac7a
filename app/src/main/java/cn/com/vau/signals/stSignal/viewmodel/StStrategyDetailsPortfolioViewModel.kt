package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.data.strategy.StrategyPositionListBean
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.json
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject

/**
 * author：lvy
 * date：2025/05/30
 * desc：策略详情->Portfolio
 */
class StStrategyDetailsPortfolioViewModel : BaseViewModel() {

    var mBean: StrategyBean? = null
    var portfolioShowIndex = 1

    // 策略持仓列表获取成功
    private val _strategyPositionLiveData = MutableLiveData<StrategyPositionListBean?>()
    val strategyPositionLiveData: LiveData<StrategyPositionListBean?> = _strategyPositionLiveData

    /**
     * 策略持仓列表
     */
    fun stStrategyPositionApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        if (UserDataUtil.isStLogin()) {
            map["accountId"] = UserDataUtil.stAccountId()
            map["stUserId"] = UserDataUtil.stUserId()
        }
        map["strategyId"] = strategyId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.strategyPositionAnalysisApi(requestBody) }, {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            _strategyPositionLiveData.value = it.data
        })
    }

    /**
     * 设置策略详情页展示内容
     */
    fun strategyPositionSetPortfolioShowApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        map["strategyId"] = strategyId
        map["portfolioSymbolShow"] = portfolioShowIndex

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.strategyPositionSetPortfolioShowApi(requestBody) }, {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // pnl 和 daily change 切换成功后展示吐司
            ToastUtil.showToast(getString(R.string.set_up_successfully))
            // 刷新页面数据
            stStrategyPositionApi(strategyId)
            // 埋点
            sensorsTrackChange()
        }, isShowDialog = true, isAutoDismissDialog = false)
    }

    /**
     * pnl 和 daily change 切换事件埋点
     */
    private fun sensorsTrackChange() {
        if (mBean == null) return
        SensorsDataUtil.track(SensorsConstant.V3710.COPY_TRADING_PORTFOLIO_COLUMN_SWITCH, JSONObject().apply {
            put(SensorsConstant.Key.TARGET_NAME, mBean?.strategyName.ifNull()) // 策略名称
            put(SensorsConstant.Key.STRATEGY_ID, mBean?.summaryData?.strategyId.ifNull()) // 策略id
            put(SensorsConstant.Key.COLUMN_NAME, if (portfolioShowIndex == 0) "Pnl" else "Daily Change") // 栏目名称
        })
    }
}