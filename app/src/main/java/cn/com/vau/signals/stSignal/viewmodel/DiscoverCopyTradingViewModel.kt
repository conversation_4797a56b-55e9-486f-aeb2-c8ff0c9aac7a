package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.discover.StrategyMostCopied
import cn.com.vau.data.discover.StrategyRecommendAllData
import cn.com.vau.data.init.ImgAdvertInfoObj
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

class DiscoverCopyTradingViewModel : BaseViewModel() {

    init {
        strategyDiscoverListAll()
        mainEventImgAdvertInfo()
    }

    val strategyRecommendLiveData = MutableLiveData<StrategyRecommendAllData?>()
    val advertImgLiveData = MutableLiveData<ImgAdvertInfoObj?>()
    val bannerLivaData by lazy { MutableLiveData<List<String?>>() }

    fun strategyDiscoverListAll() {
        requestNet({
            // 非跟单传空
            stTradingService.strategyDiscoverListAll("")
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                sendEvent(Constants.EVENT_REFRESHLAYOUT_FINISH)
                return@requestNet
            }
            strategyRecommendLiveData.value = it.data
        }, onError = {
            sendEvent(Constants.EVENT_REFRESHLAYOUT_FINISH)
        }, isShowDialog = true)
    }

    fun mainEventImgAdvertInfo() {
        val paramMap = hashMapOf<String, Any>(
            "userId" to UserDataUtil.userId(),
            "token" to UserDataUtil.loginToken(),
            "mt4AccountId" to UserDataUtil.accountCd(),
            "imgType" to 11,    // 0 启动页  7 广告位   21 首页运营广告位   11 跟单首页运营位
            "fitModel" to 0     // 适配手机型号: 0:通用, 1:iphoneX, 2:安卓16-9 3:安卓18-9 4:安卓19.5-9
        )
        requestNet({
            baseService.imgAdvertInfo(paramMap)
        }, onSuccess = {
            if (!it.isSuccess()) {
                return@requestNet
            }
            advertImgLiveData.value = it.data?.obj
            bannerLivaData.value = it.data?.obj?.eventsList?.map { data -> data.imgUrl }
        })
    }

    fun mainEventImgClose(idList: String) {
        val paramMap = hashMapOf<String, Any>(
            "imgType" to 21,     // 21:首页广告位
            "eventIdList" to idList,
            "userId" to UserDataUtil.userId(),
            "accountId" to UserDataUtil.accountCd(),
            "token" to UserDataUtil.loginToken()
        )
        requestNet({
            baseService.imgClose(paramMap)
        }, onSuccess = {
            if (it.isSuccess()) {
                advertImgLiveData.value = null
            }
        })
    }

    fun sensorsCopyTradingStrategyClick(data: StrategyMostCopied, typeForm: Int) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TARGET_NAME, data.nickname.ifNull()) // 策略名称
        properties.put(SensorsConstant.Key.STRATEGY_ID, data.strategyId.ifNull()) // 策略ID
        properties.put("section_name", when (typeForm) {
            -1 -> "Growth Shield"
            0 -> "Most Copied"
            1 -> "Highest Annual Return"
            2 -> "Low Risk and Stable Return"
            3 -> "High Win Rate"
            else -> "None"
        })  // 栏位名称
        properties.put("current_tab", "Discover") // 当前页面
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGSTRATEGY_CLICK, properties)
    }
}