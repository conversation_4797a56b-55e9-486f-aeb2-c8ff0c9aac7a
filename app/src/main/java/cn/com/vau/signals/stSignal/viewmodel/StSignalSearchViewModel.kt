package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.strategy.*
import cn.com.vau.util.*
import kotlinx.coroutines.flow.*

/**
 * Filename: StSignalSearchActivity.kt
 * Author: GG
 * Date: 2024/3/28
 * Description:
 */
class StSignalSearchViewModel : BaseViewModel() {

    private val map = mutableMapOf<String, String?>()
    val inputSearchLiveData: MutableLiveData<CharSequence> by lazy { MutableLiveData() }

    val topStrategiesLiveData: MutableLiveData<ListUIState<List<StTopBean>?>> by lazy { MutableLiveData() }
    val topSignalLiveData: MutableLiveData<ListUIState<List<StTopBean>?>> by lazy { MutableLiveData() }
    val searchStrategiesLiveData: MutableLiveData<ListUIState<List<SearchStrategyBean>?>> by lazy { MutableLiveData() }
    val searchSignalLiveData: MutableLiveData<ListUIState<List<SearchStrategyBean>?>> by lazy { MutableLiveData() }
    val searchStrategiesBySymbolsLiveData: MutableLiveData<ListUIState<List<SearchStrategyBean>?>> by lazy { MutableLiveData() }

    private val _clickEventFlow = MutableSharedFlow<Unit>()
    val clickEventFlow = _clickEventFlow.asSharedFlow()

    var selectTab: Int = 0

    var searchStrategiesPage = 1
    var searchSignalPage = 1
    var searchStrategiesBySymbolsPage = 1

    var searchText: String? = ""

    val isEditFocusFlow = MutableSharedFlow<Boolean>()
    val isSoftInputShowFlow = MutableSharedFlow<Boolean>()
    val hasInputFlow = MutableSharedFlow<Boolean>()

    val clearShowFlow = combine(isEditFocusFlow, isSoftInputShowFlow, hasInputFlow) { result1, result2, result3 ->
        result1 && result2 && result3
    }

    /**
     * 热门搜索 策略
     */
    fun topStrategies() {
        requestNet({
            stTradingService.strategyGetTopClickStrategies(stUserId = UserDataUtil.stUserId())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            topStrategiesLiveData.value = ListUIState.RefreshSuccess(it.data)
        })
    }

    /**
     * 热门搜索 信号源
     */
    fun topSignal() {
        requestNet({
            stTradingService.userGetTopClickSignals(stUserId = UserDataUtil.stUserId(), currentLoginAccountId = UserDataUtil.stAccountId())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            topSignalLiveData.value = ListUIState.RefreshSuccess(it.data)
        })
    }

    /**
     * 按策略名搜索
     */
    fun searchStrategies(showLoading: Boolean = false) {
        requestNet({
            map.clear()
            map["searchText"] = inputSearchLiveData.value?.toString()
            map["sortBy"] = "NICKNAME"
            map["pageNum"] = searchStrategiesPage.toString()
            map["accountId"] = if (UserDataUtil.isStLogin()) UserDataUtil.stAccountId() else ""
            map["pageSize"] = "20"
            stTradingService.signalListSearch(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val objList = it.data
            if (objList.isNullOrEmpty() && searchStrategiesPage == 1) {
                searchStrategiesLiveData.value = ListUIState.Empty
                return@requestNet
            }
            if (searchStrategiesPage == 1) {
                searchStrategiesLiveData.value = ListUIState.RefreshSuccess(objList)
            } else {
                if (objList.isNullOrEmpty()) {
                    searchStrategiesLiveData.value = ListUIState.LoadEnd(objList)
                } else {
                    searchStrategiesLiveData.value = ListUIState.LoadMoreSuccess(objList)
                }
            }
        }, onError = {
            searchStrategiesLiveData.value = ListUIState.Error()
        }, isShowDialog = showLoading)
    }

    /**
     * 按信号源昵称搜索
     */
    fun searchSignal(showLoading: Boolean = false) {
        requestNet({
            map.clear()
            map["searchText"] = inputSearchLiveData.value?.toString()
            map["sortBy"] = "NICKNAME"
            map["pageNum"] = searchSignalPage.toString()
            map["pageSize"] = "20"
            stTradingService.userSearch(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val objList = it.data
            if (objList.isNullOrEmpty() && searchSignalPage == 1) {
                searchSignalLiveData.value = ListUIState.Empty
                return@requestNet
            }
            if (searchSignalPage == 1) {
                searchSignalLiveData.value = ListUIState.RefreshSuccess(objList)
            } else {
                if (objList.isNullOrEmpty()) {
                    searchSignalLiveData.value = ListUIState.LoadEnd(objList)
                } else {
                    searchSignalLiveData.value = ListUIState.LoadMoreSuccess(objList)
                }
            }
        }, onError = {
            searchSignalLiveData.value = ListUIState.Error()
        }, isShowDialog = showLoading)
    }

    /**
     * 按交易产品名搜索
     */
    fun searchStrategiesBySymbols(showLoading: Boolean = false) {
        requestNet({
            map.clear()
            map["searchText"] = this.searchText
            map["sortBy"] = "PRODUCT"
            map["pageNum"] = searchStrategiesBySymbolsPage.toString()
            map["accountId"] = if (UserDataUtil.isStLogin()) UserDataUtil.stAccountId() else ""
            map["pageSize"] = "20"
            stTradingService.signalListSearch(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val objList = it.data
            if (objList.isNullOrEmpty() && searchStrategiesBySymbolsPage == 1) {
                searchStrategiesBySymbolsLiveData.value = ListUIState.Empty
                return@requestNet
            }
            if (searchStrategiesBySymbolsPage == 1) {
                searchStrategiesBySymbolsLiveData.value = ListUIState.RefreshSuccess(objList)
            } else {
                if (objList.isNullOrEmpty()) {
                    searchStrategiesBySymbolsLiveData.value = ListUIState.LoadEnd(objList)
                } else {
                    searchStrategiesBySymbolsLiveData.value = ListUIState.LoadMoreSuccess(objList)
                }
            }
        }, onError = {
            searchStrategiesBySymbolsLiveData.value = ListUIState.Error()
        }, isShowDialog = showLoading)
    }

    suspend fun sendClickEvent() {
        // 发送点击事件
        _clickEventFlow.emit(Unit)
    }

}