package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.common.view.popup.adapter.StCommunityFilterBean
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.signals.stsignal.fragment.StCommunityFragment.Companion.OVERALLSORT_RATING
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import kotlinx.coroutines.Job
import org.json.JSONObject

/**
 * Filename: StCommunityViewModel.kt
 * Author: GG
 * Date: 2024/3/26
 * Description:
 */
class StCommunityViewModel : BaseViewModel() {

    var overallSort: String = OVERALLSORT_RATING
    var dataTimeBean: StCommunityFilterBean? = null
    var dataRatingBean: StCommunityFilterBean? = null
    var dataReturnBean: StCommunityFilterBean? = null
    var dataRiskBandBean: StCommunityFilterBean? = null
    var dataWinRateBean: StCommunityFilterBean? = null
    var dataTradingBean: StCommunityFilterBean? = null

    val uiListLiveData: MutableLiveData<ListUIState<List<StrategyBean>?>> by lazy { MutableLiveData() }

    var pageNum = 1
    private val pageSize = 20
    var selectQuickIndex = 0

    private val requestMap = mutableMapOf<String, String?>()

    var totalScrollDistance = 0

    var requestJob: Job? = null

    init {
        refresh()
    }

    fun clearData() {
        dataTimeBean = null
        dataRatingBean = null
        dataReturnBean = null
        dataRiskBandBean = null
        dataWinRateBean = null
        dataTradingBean = null
    }

    fun refresh() {
        pageNum = 1
        filterSignalApi()
    }

    fun loadMore() {
        pageNum++
        filterSignalApi()
    }

    /**
     * 策略排行榜
     */
    private fun filterSignalApi() {
        requestJob?.cancel()
        requestJob = requestNet({
            requestMap.clear()
            requestMap["overallSort"] = overallSort
            requestMap["winRate"] = dataWinRateBean?.requestData
            requestMap["returnRate"] = dataReturnBean?.requestData
            requestMap["months"] = dataTimeBean?.requestData ?: "3"
            requestMap["riskBandLevel"] = dataRiskBandBean?.requestData
            requestMap["tradingCategories"] = dataTradingBean?.requestData
            requestMap["rating"] = dataRatingBean?.requestData ?: "0"
            requestMap["pageNum"] = pageNum.toString()
            requestMap["pageSize"] = pageSize.toString()
            stTradingService.strategyFilterSignalApi(GsonUtil.buildGson().toJsonTree(requestMap).asJsonObject)
        }, { dataBean ->
            if (!dataBean.isSuccess()) {
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }
            if (dataBean.data.isNullOrEmpty() && pageNum == 1) {
                uiListLiveData.value = ListUIState.Empty
                return@requestNet
            }

            if (pageNum == 1) {
                uiListLiveData.value = ListUIState.RefreshSuccess(dataBean.data)
            } else {
                if (dataBean.data.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.LoadEnd(dataBean.data)
                } else {
                    uiListLiveData.value = ListUIState.LoadMoreSuccess(dataBean.data)
                }
            }
        }, {
            uiListLiveData.value = ListUIState.Error()
        }, isShowDialog = false)

    }

    /**
     * 账户状态查询
     */
    fun queryMT4AccountTypeApi() {
        requestNet({
            baseService.crmGetMt4AccountApplyTypeApi(UserDataUtil.loginToken())
        }, onSuccess = { data ->
            val obj = data.data?.obj
            /*applyTpe: 申请类型(0:不能申请, 1:真实账户开通，
              2:同名账户，3:重新申请，
              4：身份证明或地址证明未通过，5：只读账户身份证明未通过，
              6：账户被拒绝，7：未上传身份证明)
              若applyTpe返回2表示Live帳號已完全開通完畢 則此時表示開通跟單帳戶可直接跳一頁式的頁面來開通
              否則遵照原本開通LIVE的判斷來決定下一步頁面
            openActivity(OpenStAccountActivity::class.java)*/
            sendEvent(obj)
        })
    }

    /**
     * 神策自定义埋点(v3710)
     * App_发现页面点击 -> 排序筛选
     */
    fun sensorsTrack(position: Int) {
        val properties = JSONObject()
        properties.put(
            SensorsConstant.Key.TAB_NAME, when (position) {
                0 -> "Rating"
                1 -> "Return"
                2 -> "Copiers"
                3 -> "Win Rate"
                4 -> "Risk Band"
                else -> ""
            }
        ) // 所属Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3710.COPY_TRADING_DISCOVER_SORT_CLICK, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}