package cn.com.vau.signals.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.discover.FxStreetBaseData
import kotlinx.coroutines.*

/**
 * Filename: FxStreetViewModel
 * Author: GG
 * Date: 2024/9/20
 * Description:
 */
class FxStreetViewModel : BaseViewModel() {

    private var pageNum = 1
    private var pageSize = 20
    private var timeCurrentTimeMillis = System.currentTimeMillis()
    private var isTaskRunning = true
    private val dataList: MutableList<FxStreetBaseData> by lazy { mutableListOf() }
    val uiListLiveData: MutableLiveData<ListUIState<List<FxStreetBaseData>?>> by lazy { MutableLiveData() }

    init {
        refresh()
        startPeriodicTask()
    }

    fun refresh() {
        pageNum = 1
        timeCurrentTimeMillis = System.currentTimeMillis()
        queryFxStreetHistoryList()
    }

    fun loadMore() {
        pageNum++
        queryFxStreetHistoryList()
    }

    /**
     * 获取 fx street 历史列表
     */
    private fun queryFxStreetHistoryList() {
        requestNet({
            baseService.newsGetHistorylist(startTime = timeCurrentTimeMillis.toString(), pageNum = pageNum, pageSize = pageSize)
        }, { dataBean ->
            if (!dataBean.isSuccess()) {
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }
            val objList = dataBean.data?.datas as? ArrayList<FxStreetBaseData>
            if (objList.isNullOrEmpty() && pageSize == 1) {
                uiListLiveData.value = ListUIState.Empty
                return@requestNet
            }
            if (pageNum == 1) {
                uiListLiveData.value = ListUIState.RefreshSuccess(objList)
                dataList.clear()
                dataList.addAll(objList.orEmpty())
            } else {
                if (objList.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.LoadEnd(objList)
                } else {
                    uiListLiveData.value = ListUIState.LoadMoreSuccess(objList)
                }
                dataList.addAll(objList.orEmpty())
            }
        }, {
            uiListLiveData.value = ListUIState.Error()
        }, isShowDialog = false)
    }

    /**
     * 设置定时任务，每 2 分钟调用一次接口，看是否有新的数据
     */
    fun startPeriodicTask() {
        // 启动一个协程来执行定时任务
        viewModelScope.launch {
            while (isTaskRunning) {
                // 延迟 2 分钟
                delay(120_000) // 120000 毫秒
                // 调用接口
                queryFxStreetLatestList()
            }
        }
    }

    /**
     * 获取最新数据，与本地数据对比，有不一样的就提示刷新
     */
    private fun queryFxStreetLatestList() {
        requestNet({
            baseService.newsGetLatestlist()
        }, { dataBean ->
            if (!dataBean.isSuccess()) {
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }
            viewModelScope.launch(Dispatchers.Default) {
                val fxStreetLatestList = dataBean.data as? ArrayList<FxStreetBaseData>
                val bigList = fxStreetLatestList
                    ?.filter { (it.pubTime ?: 0) > (dataList.getOrNull(0)?.pubTime ?: 0L) }
                    ?.filter { b -> !dataList.any { it.id == b.id } }.orEmpty()

                if (bigList.isNotEmpty()) {
                    sendEvent(Unit)
                }
            }

        }, isShowDialog = false)
    }

    override fun onCleared() {
        super.onCleared()
        isTaskRunning = false
    }

}