package cn.com.vau.signals.stsignal.viewmodel

import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.signals.stsignal.activity.StCreateAndEditStrategyActivity
import cn.com.vau.util.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.InputStream

/**
 * Filename: StCreateAndEditStrategyViewModel.kt
 * Author: GG
 * Date: 2024/3/30
 * Description:
 */
class StCreateAndEditStrategyViewModel : BaseViewModel() {

    /**
     * 页面类型
     */
    var type: String = StCreateAndEditStrategyActivity.TYPE_CREATE

    /**
     * 作为显示和最后上传的策略数据
     */
    var strategy = StrategyBean()

    /**
     * 用于接受 本地编辑时 外部列表传入的 策略数据 或者 编辑上架策略时 ，网络请求到的策略数据， 用来退出页面时 和 上传的策略进行对比
     */
    var oldStrategy: StrategyBean = StrategyBean()

    /**
     * 是否是编辑已上架的策略
     */
    fun isEditOpenStrategy(): Boolean = type == StCreateAndEditStrategyActivity.TYPE_OPEN_EDIT

    /**
     * 是否是编辑策略
     */
    fun isEditStrategy(): Boolean = type != StCreateAndEditStrategyActivity.TYPE_CREATE

    /**
     * 最小跟单金额
     */
    fun minInvestedValue(): Int {
        return when (strategy.loginAccountCurrency ?: UserDataUtil.currencyType()) {
            "HKD" -> 400
            "JPY" -> 7000
            "USC", "INR" -> 4000
            else -> 50
        }
    }

    /**
     * 选择头像上传
     */
    fun uploadAvatarApi(inputStream:  InputStream) {
        runCatching {
            val builder = MultipartBody.Builder()
                .setType(MultipartBody.FORM)//表单类型
                .addFormDataPart(
                    "token",
                    UserDataUtil.loginToken()
                ) // ParamKey.TOKEN 自定义参数key常量类，即参数名
            val byteArray = inputStream.readBytes()
            val requestBody = byteArray.toRequestBody("multipart/form-data".toMediaTypeOrNull(), 0, byteArray.size)
            builder.addFormDataPart("imgFile", "Avatar${System.currentTimeMillis()}.jpg", requestBody) // imgfile 后台接收图片流的参数名
            builder.build()
        }.onSuccess {
            requestNet({ baseService.fileFileUploadApi(it) }, {
                if (!it.isSuccess()) {
                    ToastUtil.showToast(it.getResponseMsg())
                    return@requestNet
                }
                strategy.avatar = it.data?.obj?.imgFileoos
                sendEvent(DataEvent(EVENT_UPLOAD_AVATAR_SUCCESS, strategy.avatar))
            }, isShowDialog = true)
        }.onFailure {
            LogUtil.e(it)
            hideLoading()
            ToastUtil.showToast("Failed to get the file")
        }
    }

    /**
     * 本地保存
     */
    fun save(): Boolean {
        val list: MutableList<StrategyBean> = SpManager.getStrategyListDraft() ?: mutableListOf()
        strategy.let { data ->
            if (data.localCreateTime == null) {
                data.localCreateTime = System.currentTimeMillis()
            }

            val index = list.indexOfFirst { it.localCreateTime == data.localCreateTime }
            if (index != -1 && index < list.size) {
                data.localCreateTime = System.currentTimeMillis()
                list.removeAt(index)
                list.add(data)
            } else {
                list.add(data)
            }
        }
        SpManager.putStrategyListDraft(list)
        return true
    }

    /**
     * 策略更新
     */
    fun updateStrategyApi(value: StrategyBean?) {
        requestNet({ stTradingService.strategyUpdateApi(GsonUtil.buildGson().toJsonTree(value).asJsonObject) }, {
            if (!it.isSuccess()) {
                parseData(it)
            } else {
                sendEvent(DataEvent(EVENT_UPDATE_STRATEGY_SUCCESS))
            }
        }, isShowDialog = true)
    }

    /**
     * 创建策略，提交后台
     */
    fun createStrategyApi() {
        strategy.loginAccountId = UserDataUtil.stAccountId() // ********新增参数
        requestNet({ stTradingService.strategyNewWithAccountApi(GsonUtil.buildGson().toJsonTree(strategy).asJsonObject) }, {
            if (!it.isSuccess()) {
                parseData(it)
            } else {
                clearCurrentStrategy()
                sendEvent(DataEvent(EVENT_CREATE_STRATEGY_SUCCESS))
            }
        }, isShowDialog = true)
    }

    /**
     * 解析数据 , 并处理创建和更新策略失败的情况
     */
    private fun parseData(data: ApiResponse<*>) {
        if (data.getResponseCode() == "10583") {
            sendEvent(DataEvent(EVENT_CONFIG_STRATEGY_FAIL_10583))
        } else if (data.getResponseCode() == "10585") {
            sendEvent(DataEvent(EVENT_CONFIG_STRATEGY_FAIL_10585))
        } else {
            ToastUtil.showToast(data.getResponseMsg())
        }
    }

    /**
     * 获取live账号列表
     */
    fun getLiveAccountApi(isShowLoad: Boolean = true) {
        requestNet({ baseService.getAccountsApi(UserDataUtil.loginToken()) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_GET_LIVE_ACCOUNT_LIST, it.data?.obj))
        }, isShowDialog = isShowLoad)
    }

    /**
     * 获取头像列表
     */
    fun getAllPicApi(isShowLoad: Boolean = true) {
        requestNet({ baseService.selectAllPicApi() }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // strategy 的 avatar 为空时，随机设置一个头像
            if (strategy.avatar.isNullOrBlank()) {
                it.data?.obj?.random()?.url?.let { url ->
                    strategy.avatar = url
                    oldStrategy.avatar = url
                }
            }
            sendEvent(DataEvent(EVENT_GET_AVATAR_LIST, it.data?.obj))
        }, isShowDialog = isShowLoad)
    }

    /**
     * 获取策略详情
     */
    fun strategyDetailApi(isShowLoad: Boolean = true) {
        val jsonObj = GsonUtil.buildGson().toJsonTree(mapOf("strategyId" to strategy.strategyId, "stUserId" to UserDataUtil.stUserId())).asJsonObject
        requestNet({ stTradingService.strategyLoadApi(jsonObj) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 后端返回的策略详情 后端数据比较混乱，所以这里格式化一下
            it.data?.apply {
                minInvestmentPerCopy = minInvestmentPerCopy.numFormat(0)
                minLotsPerOrder = minLotsPerOrder.numFormat(2)
                minLotsMultiplePerOrder = minLotsMultiplePerOrder.numFormat(1)
            }?.let { data ->
                oldStrategy = data.copy()
                strategy = data.copy()
            }
            sendEvent(DataEvent(EVENT_REFRESH_STRATEGY_UI))
        }, isShowDialog = isShowLoad)
    }

    /**
     * 创建策略成功，清除本地缓存
     */
    private fun clearCurrentStrategy() {
        val list: MutableList<StrategyBean> = SpManager.getStrategyListDraft() ?: mutableListOf()
        strategy.let { data ->
            val index = list.indexOfFirst { it.localCreateTime == data.localCreateTime }
            if (index != -1 && index < list.size) {
                list.removeAt(index)
            }
        }
        SpManager.putStrategyListDraft(list)
    }

    /**
     * 获取分润结算周期
     */
    fun strategyGetProfitShareCycleTypeApi() {
        requestNet({
            stTradingService.strategyGetProfitShareCycleTypeApi()
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val list = it.data?.split(",")?.map {
                createProfitCycleTypeData(it)
            }.orEmpty().toMutableList()
            sendEvent(DataEvent(EVENT_PROFIT_CYCLE_TYPE, list))
        })
    }

    /**
     * 传入结算周期类型 ， 构造结算周期数据
     */
    fun createProfitCycleTypeData(type: String): SelectBean = when (type) {
        KEY_DAILY -> SelectBean(title = StringUtil.getString(R.string.daily), id = KEY_DAILY)
        KEY_WEEKLY -> SelectBean(title = StringUtil.getString(R.string.weekly), id = KEY_WEEKLY)
        else -> SelectBean(title = StringUtil.getString(R.string.monthly), id = KEY_MONTHLY)
    }

    companion object {

        const val EVENT_REFRESH_STRATEGY_UI = "event_refresh_strategy_ui"
        const val EVENT_PROFIT_CYCLE_TYPE = "event_profit_cycle_type"
        const val EVENT_UPLOAD_AVATAR_SUCCESS = "event_upload_avatar_success"
        const val EVENT_UPDATE_STRATEGY_SUCCESS = "event_update_strategy_success"
        const val EVENT_CREATE_STRATEGY_SUCCESS = "event_create_strategy_success"
        const val EVENT_GET_AVATAR_LIST = "event_get_avatar_list"
        const val EVENT_GET_LIVE_ACCOUNT_LIST = "event_get_live_account_list"

        const val EVENT_CONFIG_STRATEGY_FAIL_10583 = "event_config_strategy_fail_10583"
        const val EVENT_CONFIG_STRATEGY_FAIL_10585 = "event_config_strategy_fail_10585"

        const val KEY_DAILY = "1"
        const val KEY_WEEKLY = "2"
        const val KEY_MONTHLY = "3"
    }
}