package cn.com.vau.signals.stsignal.adapter

import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.strategy.StrategyPositionListSymbolBean
import cn.com.vau.util.ifNull
import cn.com.vau.util.percent
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class StSignalInvestedAdapter : BaseQuickAdapter<StrategyPositionListSymbolBean, BaseViewHolder>(R.layout.item_st_signal_invested) {

    override fun convert(holder: BaseViewHolder, item: StrategyPositionListSymbolBean) {
        //产品
        holder.setText(R.id.tvSymbol, item.symbol)

        //方向
        holder.setText(R.id.tvDirection, item.tradeAction)
            .setBackgroundResource(
                R.id.tvDirection, if (item.tradeAction?.equals("Buy", true) == true) {
                    holder.setTextColor(R.id.tvDirection, ContextCompat.getColor(context, R.color.c00c79c))
                    R.drawable.shape_c1f00c79c_r100
                } else {
                    holder.setTextColor(R.id.tvDirection, ContextCompat.getColor(context, R.color.ce35728))
                    R.drawable.shape_c1fe35728_r100
                }
            )

        //dailyChange
        val dailyChange = item.dailyChange.ifNull().percent()
        holder.setText(R.id.tvDailyChange, "$dailyChange%")
            .setTextColorRes(
                R.id.tvDailyChange, if (dailyChange.contains("-")) {
                    R.color.ce35728
                } else {
                    R.color.c00c79c
                }
            )
    }
}