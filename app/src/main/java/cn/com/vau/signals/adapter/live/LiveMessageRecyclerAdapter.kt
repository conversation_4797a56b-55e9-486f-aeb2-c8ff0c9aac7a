package cn.com.vau.signals.adapter.live

import android.content.Context
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.page.AWSMessageData

/**
 * Created by liyang
 */
class LiveMessageRecyclerAdapter(
    var mContext: Context,
    var dataList: List<AWSMessageData>
) : RecyclerView.Adapter<LiveMessageRecyclerAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_recycler_live_message, null))

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val dataBean = dataList.elementAtOrNull(position) ?: return

        if (dataBean.messageType == 0) {
            if ((dataBean?.Sender?.Attributes?.userName ?: "") == "Assistant" || (dataBean?.Sender?.Attributes?.userName ?: "") == "Vantage") {
                holder.tvContent.text = Html.fromHtml(
                    "${"Vantage"} : ${dataBean?.Content}"
                )
            } else {
                holder.tvContent.text = Html.fromHtml(
                    "<font color='#ff8e5c'>${dataBean?.Sender?.Attributes?.userName ?: ""} : </font>${dataBean?.Content}"
                )
            }
        } else {
            holder.tvContent.text = Html.fromHtml(
                "${"Vantage"}${" " + mContext.getString(R.string.reply) + " "} " +
                        "<font color='#ff8e5c'>${"@"}${dataBean?.Attributes?.replyUserName ?: ""} : </font>${dataBean?.Content}"
            )
        }
    }

    override fun getItemCount(): Int = dataList.size

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvContent = view.findViewById<TextView>(R.id.tvContent)
    }

    private var monItemClickListener: onItemClickListener? = null

    interface onItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: onItemClickListener) {
        monItemClickListener = onItemClickListener
    }

}