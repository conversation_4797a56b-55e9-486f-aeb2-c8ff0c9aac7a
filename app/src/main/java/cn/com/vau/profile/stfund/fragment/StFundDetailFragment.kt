package cn.com.vau.profile.stfund.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.depositcoupon.FundHistoryBean
import cn.com.vau.data.depositcoupon.FundHistoryData
import cn.com.vau.databinding.FragmentStFundDetailBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.depositNew.DepositDetailsActivity
import cn.com.vau.profile.activity.manageFundsDetails.FundsDetailsActivity
import cn.com.vau.profile.stfund.adapter.ManageStFundsAdapter
import cn.com.vau.util.ToastUtil
import io.reactivex.disposables.Disposable
import org.greenrobot.eventbus.*

private const val ARG_PARAM1 = "accountType"

class StFundDetailFragment : BaseFragment() {

    private val mBinding by lazy { FragmentStFundDetailBinding.inflate(layoutInflater) }

    val stShareAccountBean = VAUSdkUtil.stShareAccountBean()
    var accountType = ""

    private var manageStFundsAdapter: ManageStFundsAdapter? = null
    private var fundDetailList: List<FundHistoryData>? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? = mBinding.root

    override fun initParam() {
        super.initParam()
        arguments?.let {
            accountType = it.getString(ARG_PARAM1) ?: ""
        }
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun initView() {
        super.initView()
        initFundDetailAdapter()
        mBinding.mRefreshLayout.setEnableLoadMore(false)
        mBinding.mRefreshLayout.setOnRefreshListener {
            stTradeListFundHistory()
        }
    }

    override fun initData() {
        super.initData()
        stTradeListFundHistory()
    }

    private fun stTradeListFundHistory() {
//        showNetDialog()
        val params = hashMapOf<String, Any>()
        params["accountId"] = UserDataUtil.stAccountId()
        when (accountType) {
            "Manual" -> {
                params["type"] = "0"
            }

            "Copy" -> {
                params["type"] = "1"
            }
        }

        HttpUtils.loadData(
            RetrofitHelper.getStHttpService().tradeListFundHistory(params),
            object : BaseObserver<FundHistoryBean>() {
                override fun onNext(dataBean: FundHistoryBean?) {
                    hideNetDialog()
                    if (dataBean?.code == "200") {
                        fundDetailList = dataBean.data
                        fundDetailList?.let { manageStFundsAdapter?.updateData(it) }
                        mBinding.mRefreshLayout?.finishRefresh(Constants.finishRefreshOrMoreTime)
                    } else {
                        ToastUtil.showToast(dataBean?.msg)
                        mBinding.mRefreshLayout?.finishRefresh(false)
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    rxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    hideNetDialog()
                    mBinding.mRefreshLayout?.finishRefresh(false)
                }
            })
    }

    @SuppressLint("SetTextI18n")
    private fun initFundDetailAdapter() {

        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener{
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setHintMessage(getString(R.string.no_records_found))
            }
        })

        val linearLayoutManager = WrapContentLinearLayoutManager(context)
        linearLayoutManager.orientation = RecyclerView.VERTICAL
        mBinding.mRecyclerView.layoutManager = linearLayoutManager

        manageStFundsAdapter = ManageStFundsAdapter(requireContext(), fundDetailList, accountType)
        mBinding.mRecyclerView.adapter = manageStFundsAdapter
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll)

    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    override fun initListener() {
        super.initListener()
        manageStFundsAdapter?.setOnItemClickListener(object : ManageStFundsAdapter.OnItemClickListener {
            override fun onItemClick(view: View?, position: Int) {
                if (fundDetailList?.isEmpty() == true)
                    return
                val bundle = Bundle()
                val orderType = fundDetailList?.elementAtOrNull(position)?.actionCode   //明细交易代码，00入金，01出金，10转账，20收益
                bundle.putString("orderNo", fundDetailList?.elementAtOrNull(position)?.orderNo)
                bundle.putString("orderType", orderType)
                openActivity(
                    if ("00" == orderType) DepositDetailsActivity::class.java else FundsDetailsActivity::class.java, bundle
                )
            }

        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 跟單資金變動、出入金，IB佣金申请
            NoticeConstants.Init.DATA_SUCCESS_FOLLOWERS_ORDER_ST, NoticeConstants.WS.CHANGE_OF_FUNDS -> {
                stTradeListFundHistory()
            }
        }
    }

    companion object {

        @JvmStatic
        fun newInstance(accountType: String) = StFundDetailFragment().apply {
            arguments = Bundle().apply {
                putString(ARG_PARAM1, accountType)
            }
        }

    }

}