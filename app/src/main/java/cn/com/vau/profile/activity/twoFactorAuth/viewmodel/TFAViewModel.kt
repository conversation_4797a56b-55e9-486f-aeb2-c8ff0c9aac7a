package cn.com.vau.profile.activity.twoFactorAuth.viewmodel

import androidx.lifecycle.*
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.data.account.*
import cn.com.vau.data.account.TFASettingData
import cn.com.vau.data.profile.AuthConfigObjBean
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.page.user.loginPwd.LoginVeriParam
import cn.com.vau.util.*
import cn.com.vau.util.base64.ParseUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*

/**
 * Filename: TwoFactorAuthViewModel.kt
 * Author: GG
 * Date: 2024/2/18
 * Description:
 */
class TFAViewModel : BaseViewModel() {

    val pageFromLiveData: MutableLiveData<String> by lazy { MutableLiveData() }

    var pageType: String = ""

    var tCode: String = ""

    val paramLiveData: MutableLiveData<LoginVeriParam?> by lazy { MutableLiveData() }

    /**
     * 当前页面
     */
    val currentPageLiveData: MutableLiveData<Int> by lazy { MutableLiveData(0) }

    /**
     * 验证码发送类型
     */
    val sendTypeLiveData by lazy { MutableLiveData("2") }

    /**
     * 密码
     */
    var password: String? = null

    /**
     * 获取验证otp的类型 1 email 2 2fa 3 phone
     */
    var verificationType: Int? = null

    var otpValidateCode = ""

    /**
     * 换绑2fa 接收otp数据的livedata
     */
    var txId: String? = ""
    var smsCodeId: String? = ""

    /**
     * 获取2fa的设置信息
     */
    val tfaSettingLiveData by lazy { MutableLiveData<TFASettingData.Obj>(null) }

    /**
     * 2fa的绑定结果
     */
    var tfaBindResult: ApiResponse<Any>? = null

    /**
     * 换绑2fa的绑定结果
     */
    var tfaChangeResult: ApiResponse<TFAResultData>? = null

    /**
     * 倒计时的livedata
     */
    val countDownLiveData: MutableLiveData<Int?> by lazy { MutableLiveData(null) }

    var countDownJob: Job? = null

    var signUpRequestBean: SignUpRequestBean? = null
    val getAuthConfigSuccessLiveData = MutableLiveData<Pair<String, AuthConfigObjBean?>>() // 获取安全中心配置信息成功

    /**
     * 换绑2fa的时候 获取验证码
     */
    fun changeGetTelSmsApi(
        smsSendType: String? = sendTypeLiveData.value,
        recaptcha: String = "",
    ) {
        sendTypeLiveData.value = smsSendType
        val map = hashMapOf<String, Any?>()
        map["phoneCountryCode"] = UserDataUtil.countryCode()
        map["code"] = UserDataUtil.areaCode()
        map["userTel"] = UserDataUtil.userTel()
        map["type"] = "18"
        map["recaptcha"] = recaptcha
        map["smsCodeId"] = null
        map["smsSendType"] = sendTypeLiveData.value
        map["userPassword"] = null
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({
            baseService.getTelSmsApi(paramMap)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            countDownLiveData.value = null
            smsCodeId = it.data?.obj?.smsCodeId
            sendEvent("countDown")
        }, isShowDialog = true)
    }

    /**
     * 邮件验证码发送
     */
    fun emailSendEmailCodeApi() {
        requestNet({
            baseService.emailSendEmailCodeApi(token = UserDataUtil.loginToken(), bizType = "1")
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            countDownLiveData.value = null
            txId = it.data?.obj?.txId
            sendEvent("countDown")
        }, isShowDialog = true)
    }

    /**
     * 验证邮件验证码
     */
    fun emailPreValidateEmailCodeApi() {
        requestNet({
            baseService.emailPreValidateEmailCodeApi(token = UserDataUtil.loginToken(), bizType = "1", code = otpValidateCode, txId = txId)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(tag = PAGE_OPT.toString(), true))
        }, isShowDialog = true)
    }

    /**
     * 验证手机验证码
     */
    fun smsValidateSmsCodeApi() {
        requestNet({
            baseService.smsValidateSmsCodeApi(validateCode = otpValidateCode, code = "18", nationalCode = UserDataUtil.areaCode(), phoneNum = UserDataUtil.userTel())
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(tag = PAGE_OPT.toString(), true))
        }, isShowDialog = true)
    }

    /**
     * 检查用户密码是否正确
     */
    fun verifyPasswordApi() {
        val map = hashMapOf<String, Any?>()
        map["token"] = UserDataUtil.loginToken()
        map["password"] = password
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({
            baseService.userVerifyPasswordApi(paramMap)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(tag = PAGE_PWD.toString(), true))
        }, isShowDialog = true)
    }

    /**
     * 获取2fa配置
     */
    fun getUserAccountDataApi() {
        requestNet({
            baseService.twoFactorSettingsApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val dataStr = ParseUtil.aes128Decrypt(it.data?.obj.ifNull(), "da4756119d5e7e0f")
            val dataBean = GsonUtil.fromJson(dataStr, TFASettingData.Obj::class.java)
            tfaSettingLiveData.value = dataBean
        })
    }

    /**
     * 判断用户是否更换设备
     */
    fun twoFactorJudgeUserChangeDeviceApi() {
        requestNet({
            baseService.twoFactorJudgeUserChangeDeviceApi(token = UserDataUtil.loginToken(), bizType = 1)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            if (it.data?.obj?.deviceChange == true) {
                sendEvent(it.data.obj.popUpMsg)
                return@requestNet
            }
            sendEvent(false)
        }, isShowDialog = true)
    }

    /**
     * 2fa验证方式列表
     */
    fun twoFactorGetVerificationListApi() {
        requestNet({
            baseService.twoFactorGetVerificationListApi(token = UserDataUtil.loginToken(), bizType = 1)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            if (it.data?.obj?.authType == 1) {
                emailSendEmailCodeApi()
            } else {
                changeGetTelSmsApi()
            }
            verificationType = it.data?.obj?.authType
            sendEvent(DataEvent(tag = PAGE_LINK.toString(), true))
        }, isShowDialog = true)
    }

    /**
     * 绑定
     */
    fun twoFactorEnableApi(authCode: String) {
        val map = hashMapOf<String, Any?>()
        map["token"] = UserDataUtil.loginToken()
        map["authCode"] = authCode
        map["password"] = password
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({
            baseService.twoFactorEnableApi(paramMap)
        }, {
            if (it.isSuccess() || it.getResponseCode() == "V50006") {
                tfaBindResult = it
                sendEvent(DataEvent(tag = PAGE_VERIFY.toString(), true))
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }

        }, isShowDialog = true)
    }

    /**
     * 换绑
     */
    fun twoFactorChangeBindApi(newTCode: String) {
        val map = hashMapOf<String, Any?>()
        map["token"] = UserDataUtil.loginToken()
        map["tCode"] = tCode
        map["newTCode"] = newTCode
        if (verificationType == 1) {
            map["emailTxId"] = txId
            map["emailCode"] = otpValidateCode
        } else {
            map["phoneCode"] = otpValidateCode
            map["nationalCode"] = UserDataUtil.areaCode()
            map["phoneNum"] = UserDataUtil.userTel()
            map["phoneSendType"] = sendTypeLiveData.value
        }
        requestNet({ baseService.twoFactorChangeBindApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            tfaChangeResult = it
            sendEvent(DataEvent(tag = PAGE_VERIFY.toString(), true))
        }, isShowDialog = true)
    }

    /**
     * kyc-获取安全中心配置
     *
     * @param functionCode modify-email = 修改邮箱；enable-auth-2fa = 启用2FA；modify-auth-2fa = 修改2FA；modify-password = 修改密码；
     *                     modify-phone = 修改手机号；add-fund-code = 添加资金安全码；modify-fund-code = 修改资金安全码；
     *                     reset-fund-code = 忘记资金安全码
     */
    fun getAuthConfigApi(functionCode: String) {
        val map = hashMapOf<String, Any?>()
        map["functionCode"] = functionCode
        requestNet({ baseService.getAuthConfigApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            getAuthConfigSuccessLiveData.value = Pair(functionCode, it.data?.obj)
        }, isShowDialog = true)
    }

    fun countDown(seconds: Int) {
        countDownJob?.cancel()
        countDownJob = viewModelScope.launch {
            channelFlow {
                for (remainingSeconds in seconds downTo 0) {
                    send(remainingSeconds)
                    countDownLiveData.postValue(remainingSeconds)
                    delay(1000) // 每秒延迟一次
                }
            }.collect()
        }
    }

    companion object {
        const val TYPE_BIND = "bind"
        const val TYPE_RESET = "reset"
        const val TYPE_CHANGE = "change"

        const val PAGE_BIND_PROMPT = 11
        const val PAGE_RESET_PROMPT = 13
        const val PAGE_LINK = 1
        const val PAGE_PWD = 2
        const val PAGE_OPT = 3
        const val PAGE_VERIFY = 4
        const val PAGE_RESULT = 5
    }
}