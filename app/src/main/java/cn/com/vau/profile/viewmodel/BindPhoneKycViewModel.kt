package cn.com.vau.profile.viewmodel

import androidx.lifecycle.*
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageUtil

/**
 * author：lvy
 * date：2025/03/22
 * desc：kyc-绑定手机号
 */
class BindPhoneKycViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null

    // 国家区号设置成功
    private val _areaCodeLiveData = MutableLiveData<String>()
    val areaCodeLiveData: LiveData<String> = _areaCodeLiveData

    /**
     * 初始化国家区号
     */
    fun initAreaCode() {
        val countryCode = SpManager.getCountryCode(Constants.defaultCountryCode)
        val countryNum = SpManager.getCountryNum()
        val countryName = SpManager.getCountryName(
            if (countryNum == Constants.defaultCountryNum) Constants.defaultCountryName else ""
        )
        if (countryNum.isBlank()) {
            getAreaCodeApi()
            return
        }
        // 刷新UI
        signUpRequestBean?.countryCode = countryCode
        signUpRequestBean?.countryNum = countryNum
        signUpRequestBean?.countryName = countryName
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(countryNum)
    }

    /**
     * 国家区号
     */
    private fun getAreaCodeApi() {
        runCatching {
            val systemCountry = LanguageUtil.getSystemLocal(UtilApp.getApp())?.country // 获取手机系统语言代码
            if (systemCountry.isNullOrBlank()) {
                setDefaultAreaCode()
                return
            }
            requestNet({ baseService.selectCountryNumberClassifyScreeningApi() }, onSuccess = {
                if (!it.isSuccess()) {
                    setDefaultAreaCode()
                    return@requestNet
                }
                val objList = it.getResponseData()?.obj
                out@ for (countryNumberObj in objList ?: ArrayList()) {
                    for (countryBean in countryNumberObj.list ?: ArrayList()) {
                        if (countryBean.countryCode == systemCountry) {
                            setAreaCodeData(countryBean)
                            break@out
                        }
                    }
                }
                // 没有匹配到接口返回的，就赋默认值
                if (signUpRequestBean?.countryCode.isNullOrBlank()) {
                    setDefaultAreaCode()
                }
            }, {
                setDefaultAreaCode()
            }, isShowDialog = true)
        }.onFailure {
            setDefaultAreaCode()
        }
    }

    /**
     * 设置国家区号，在选择国家区号页面选择回调后会调用
     */
    fun setAreaCodeData(countryBean: SelectCountryNumberObjDetail) {
        signUpRequestBean?.countryCode = countryBean.countryCode
        signUpRequestBean?.countryNum = countryBean.countryNum
        signUpRequestBean?.countryName = countryBean.countryName
        SpManager.putCountryNum(countryBean.countryNum.ifNull())
        SpManager.putCountryName(countryBean.countryName.ifNull())
        SpManager.putCountryCode(countryBean.countryCode.ifNull())
        // 这俩值是登录后使用，退出登录会清空，sp值是未登录使用，其实可以全部用sp的值
        UserDataUtil.setCountryCode(countryBean.countryCode)
        UserDataUtil.setAreaCode(countryBean.countryNum)
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }

    /**
     * 设置默认的国家区号，一些特殊情况会走这里，如获取不到用户的 country，getAreaCode() 接口请求失败等
     */
    private fun setDefaultAreaCode() {
        signUpRequestBean?.countryCode = Constants.defaultCountryCode
        signUpRequestBean?.countryNum = Constants.defaultCountryNum
        signUpRequestBean?.countryName = Constants.defaultCountryName
        SpManager.putCountryNum(Constants.defaultCountryNum)
        SpManager.putCountryName(Constants.defaultCountryName)
        SpManager.putCountryCode(Constants.defaultCountryCode)
        UserDataUtil.setCountryCode(Constants.defaultCountryCode)
        UserDataUtil.setAreaCode(Constants.defaultCountryNum)
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }
}