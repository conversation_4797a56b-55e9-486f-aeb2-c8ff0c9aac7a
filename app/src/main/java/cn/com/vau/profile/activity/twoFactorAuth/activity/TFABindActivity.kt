package cn.com.vau.profile.activity.twoFactorAuth.activity

import android.content.*
import android.content.res.ColorStateList
import android.os.Bundle
import androidx.activity.*
import androidx.core.view.*
import androidx.lifecycle.*
import androidx.navigation.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.ActivityTfaBindBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity.Companion.FROM_LOGIN
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity.Companion.FROM_SETTING
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity.Companion.FROM_TELEGRAM_BIND
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity.Companion.FROM_TELEGRAM_LOGIN
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity.Companion.FROM_WITHDRAWALS
import cn.com.vau.profile.activity.twoFactorAuth.viewmodel.TFAViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import com.netease.nis.captcha.Captcha
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collectLatest
import org.greenrobot.eventbus.EventBus

/**
 * Filename: TwoFactorAuthActivity.kt
 * Author: GG
 * Date: 2024/2/18
 * Description:
 */
class TFABindActivity : BaseMvvmActivity<ActivityTfaBindBinding, TFAViewModel>() {

    private val navigator: NavController by lazy { findNavController(R.id.fragment) }
    private val color_c1e1e1e_cebffffff: ColorStateList by lazy { ColorStateList.valueOf(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff)) }
    private val color_c731e1e1e_c61ffffff: ColorStateList by lazy { ColorStateList.valueOf(AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)) }

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.pageFromLiveData.value = intent.getStringExtra(KEY_FROM_TYPE)
        mViewModel.pageType = TFAViewModel.TYPE_BIND
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
        if (mViewModel.signUpRequestBean == null) {
            mViewModel.signUpRequestBean = SignUpRequestBean() // 为空先构建一个，方便使用
        }
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
        // 本地存储当前用户已进入过2fa绑定流程
        val userid = UserDataUtil.userId()
        SpManager.putUser2faBindEd(userid, true)
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        mBinding.mHeaderBar.setStartBackIconClickListener {
            back()
        }
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }

        LogEventUtil.setLogEvent(BuryPointConstant.V347.PROFILE_ACCOUNT_SECURITY_2FA_ENABLE_PAGE_VIEW, Bundle().apply {
            putString(
                "Position", when (mViewModel.pageFromLiveData.value) {
                    FROM_LOGIN, FROM_TELEGRAM_LOGIN, FROM_TELEGRAM_BIND -> "Log-in"
                    FROM_WITHDRAWALS -> "Withdrawal"
                    FROM_SETTING -> "Account_security"
                    else -> "Log-in"
                }
            )
        })
    }

    /**
     * 根据当前页面设置不同的返回页面
     */
    private fun back() = MainScope().launch {
        noRepeat {
            when (mViewModel.currentPageLiveData.value) {
                TFAViewModel.PAGE_BIND_PROMPT, TFAViewModel.PAGE_RESULT -> {
                    finish()
                }

                TFAViewModel.PAGE_LINK -> {
                    navigator.navigate(R.id.action_global_TFABindPromptFragment)
                }

                TFAViewModel.PAGE_PWD -> {
                    navigator.navigate(R.id.action_global_bind_TFALinkFragment)
                }

                TFAViewModel.PAGE_VERIFY -> {
                    navigator.popBackStack()
//                    navigator.navigate(R.id.action_global_bind_TFAPwdFragment)
                }
            }
        }
    }

    override fun initListener() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                back()
            }
        })
    }

    /**
     * 关闭时设置不同的类型
     * 出金过来的，需要弹窗提示
     */
    override fun finish() {
        if (mViewModel.pageFromLiveData.value == FROM_LOGIN) {
            gotoAccountManager()
        }

        if (mViewModel.pageFromLiveData.value == FROM_TELEGRAM_LOGIN || mViewModel.pageFromLiveData.value == FROM_TELEGRAM_BIND) {
            gotoAccountManager()
            EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
        }

        if (mViewModel.currentPageLiveData.value == TFAViewModel.PAGE_RESULT) {
            if (mViewModel.pageFromLiveData.value == TFAVerifyActivity.FROM_KYC_UPDATE_EMAIL) {
//                sendEmailCode() // 修改邮箱开启OTP流程保持和iOS一样退出页面，刷新数据，如果需要继续流程这里代码放开即可
                EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
            } else {
                EventBus.getDefault().post(DataEvent(tag = NoticeConstants.TFA_BIND_SUCCESS, data = mViewModel.tfaBindResult?.getResponseCode() == "V00000"))
                EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
            }
            super.finish()
        }

        if (mViewModel.pageFromLiveData.value == FROM_WITHDRAWALS && mViewModel.currentPageLiveData.value == TFAViewModel.PAGE_BIND_PROMPT) {
            CenterActionWithIconDialog.Builder(this)
                .setTitle(getString(R.string.are_you_sure_up_2fa))
                .setContent(getString(R.string.you_will_not_up_2fa))
                .setSingleButton(true)
                .setSingleButtonText(getString(R.string.setup_2fa))
                .setLinkText(getString(R.string.use_other_payment_method))
                .setLinkListener {
                    super.finish()
                }.build().showDialog()
        } else {
            super.finish()
        }
    }

    /**
     * 跳转账户选择页面
     */
    private fun gotoAccountManager() {
        openActivity(AccountManagerActivity::class.java, Bundle().apply {
            putInt(Constants.IS_FROM, 1)
        })
    }

    override fun initData() {
        super.initData()
        mViewModel.currentPageLiveData.observe(this) {
            mBinding.groupTop.isGone = it == TFAViewModel.PAGE_BIND_PROMPT || it == TFAViewModel.PAGE_LINK || it == TFAViewModel.PAGE_RESULT
            mBinding.mHeaderBar.setEndIconVisible(it == TFAViewModel.PAGE_PWD || it == TFAViewModel.PAGE_OPT)
            when (it) {
                TFAViewModel.PAGE_BIND_PROMPT -> {
                    mBinding.mHeaderBar.isGone = mViewModel.pageFromLiveData.value == FROM_LOGIN || mViewModel.pageFromLiveData.value == FROM_TELEGRAM_LOGIN || mViewModel.pageFromLiveData.value == FROM_TELEGRAM_BIND
                    mBinding.mHeaderBar.setTitleText(getString(R.string.two_factor_authentication))
                    mBinding.mHeaderBar.setEndIconVisible(false)
                }

                TFAViewModel.PAGE_LINK -> {
                    mBinding.mHeaderBar.isVisible = true
                    mBinding.mHeaderBar.setTitleText(getString(R.string.link_authenticator))
                    mBinding.mHeaderBar.setEndIconVisible(false)
                }

                TFAViewModel.PAGE_PWD -> {
                    mBinding.mHeaderBar.isVisible = true
                    mBinding.mHeaderBar.setTitleText(getString(R.string.enable_authenticator))
                    mBinding.mHeaderBar.setEndIconVisible(true)
                    setTopState(true)
                }

                TFAViewModel.PAGE_VERIFY -> {
                    mBinding.mHeaderBar.isVisible = true
                    mBinding.mHeaderBar.setTitleText(getString(R.string.enable_authenticator))
                    mBinding.mHeaderBar.setEndIconVisible(true)
                    setTopState(false)
                }

                TFAViewModel.PAGE_RESULT -> {
                    mBinding.mHeaderBar.isVisible = true
                    mBinding.mHeaderBar.setEndIconVisible(false)
                    if (mViewModel.tfaBindResult?.isSuccess() == true) {
                        mBinding.mHeaderBar.setTitleText(getString(R.string.two_factor_authentication))
                    } else {
                        mBinding.mHeaderBar.setTitleText(getString(R.string.enable_authenticator))
                    }
                }
            }
            if (SpManager.isV1V2()) { // 黄金开户的功能隐藏上面的布局
                mBinding.groupTop.isVisible = false
            }
        }

        mViewModel.getUserAccountDataApi()
    }

    override fun createObserver() {
        super.createObserver()
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    TFAViewModel.PAGE_PWD.toString() -> {
                        navigator.navigate(R.id.action_global_bind_TFAVerifyFragment)
                    }

                    TFAViewModel.PAGE_VERIFY.toString() -> {
                        navigator.navigate(R.id.action_global_bind_TFAResultFragment)
                    }
                }
            }
        }
        // 发送验证码时触发滑块验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            VerifyEmailCodeActivity.open(this, mViewModel.signUpRequestBean) // kyc修改邮箱
        }
    }

    /**
     * 设置顶部状态
     */
    private fun setTopState(isChoosePwd: Boolean) {
        if (isChoosePwd) {
            mBinding.ivPwd.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.img2faPwdSelect))
            mBinding.tvPwd.setTextColor(color_c1e1e1e_cebffffff)
            mBinding.iv2FA.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.img2faUnselect))
            mBinding.tv2FA.setTextColor(color_c731e1e1e_c61ffffff)
        } else {
            mBinding.ivPwd.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.img2faPwdUnselect))
            mBinding.tvPwd.setTextColor(color_c731e1e1e_c61ffffff)
            mBinding.iv2FA.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.img2faSelect))
            mBinding.tv2FA.setTextColor(color_c1e1e1e_cebffffff)
        }
    }

    /**
     * 发送验证码时触发滑块验证
     */
    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            sendEmailCode(it)
        }
        mCaptcha?.validate()
    }

    /**
     * 发送邮箱验证码
     */
    private fun sendEmailCode(validate: String? = null) {
        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.EMAIL
        sendCodeViewModel.sendEmailCodeApi("27", validate)
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        mCaptcha?.destroy()
    }

    companion object {

        private const val KEY_FROM_TYPE = "from_type"

        fun open(context: Context, fromType: String, signUpRequestBean: SignUpRequestBean? = null) {
            context.startActivity(Intent(context, TFABindActivity::class.java).apply {
                putExtra(KEY_FROM_TYPE, fromType)
                putExtra("signUpRequestBean", signUpRequestBean)
            })
        }
    }
}