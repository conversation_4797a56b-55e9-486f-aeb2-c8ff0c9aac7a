package cn.com.vau.profile.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2025/04/23
 * desc：kyc-修改手机号
 */
class UpdatePhoneKycViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null

    val areaCodeLiveData = MutableLiveData<String>() // 国家区号设置成功

    val getWithdrawRestrictionMsgSuccessLiveData = MutableLiveData<String?>() // 出金限制横幅文案获取成功

    /**
     * 获取出金限制横幅文案
     */
    fun getWithdrawRestrictionMsgApi(type: Int) {
        val map = hashMapOf<String, Any?>()
        map["userToken"] = UserDataUtil.loginToken()
        map["type"] = type // 1=更新手机号；2=更改登录密码；3=重置密码；5=修改邮箱
        requestNet({ baseService.fundWithdrawRestrictionMessageApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            getWithdrawRestrictionMsgSuccessLiveData.value = it.data?.obj
        })
    }

    /**
     * 设置国家区号，在选择国家区号页面选择回调后会调用
     */
    fun setAreaCodeData(countryBean: SelectCountryNumberObjDetail) {
        signUpRequestBean?.countryCode = countryBean.countryCode
        signUpRequestBean?.countryNum = countryBean.countryNum
        signUpRequestBean?.countryName = countryBean.countryName
        SpManager.putCountryNum(countryBean.countryNum.ifNull())
        SpManager.putCountryName(countryBean.countryName.ifNull())
        SpManager.putCountryCode(countryBean.countryCode.ifNull())
        areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }

    /**
     * 设置默认的国家区号
     */
    fun setDefaultAreaCode() {
        signUpRequestBean?.countryCode = SpManager.getCountryCode()
        signUpRequestBean?.countryNum = SpManager.getCountryNum()
        signUpRequestBean?.countryName = SpManager.getCountryName()
        areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }
}