package cn.com.vau.profile.activity.kycAuth

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.account.PrimaryDependOnLevel
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * Filename: AuthViewModel.kt
 * Author: GG
 * Date: 2024/1/12
 * Description:
 */
class KycAuthViewModel : BaseViewModel() {

    private val _statusStandard: MutableLiveData<List<PrimaryDependOnLevel>> by lazy {
        MutableLiveData(listOf(PrimaryDependOnLevel(level = 1), PrimaryDependOnLevel(level = 2), PrimaryDependOnLevel(level = 3)))
    }
    val statusStandard: LiveData<List<PrimaryDependOnLevel>> get() = _statusStandard

    private val _statusPlus: MutableLiveData<List<PrimaryDependOnLevel>> by lazy { MutableLiveData() }
    val statusPlus: LiveData<List<PrimaryDependOnLevel>> get() = _statusPlus

    /**
     * 获取kyc验证状态
     */
    fun userMyVerificationCenterApi() {
        requestNet({
            baseService.userMyVerificationCenterApi()
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            _statusStandard.value = it.data?.obj?.primaryDependOnLevels
            _statusPlus.value = it.data?.obj?.independenceDependOnLevels
        })
    }

    fun userQueryUserLevelApi() {
        requestNet({ baseService.userQueryUserLevelApi() }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            UserDataUtil.setKycLevel(it.data?.obj?.level.ifNull(0).toString())
            sendEvent(DataEvent(KEY_LEVEL, it.data?.obj?.level.ifNull(0)))
        })
    }

    /**
     * 神策自定义埋点(v3700)
     */
    fun sensorsTrack(type: String) {
        val properties = JSONObject()
        properties.put("verification_type", type) // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3700.AUTHENTICATIONCENTER_PAGEVIEW, properties)
    }

    companion object {

        const val KEY_LEVEL = "level"
        const val KEY_STANDARD = "standard"
        const val KEY_PLUS = "plus"
    }
}