package cn.com.vau.profile.activity.pricealert.viewmodel

import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.pricealtert.*
import cn.com.vau.util.*
import kotlinx.coroutines.*

/**
 * @description:
 * @author: GG
 * @createDate: 2024 9月 27 14:30
 * @updateUser:
 * @updateDate: 2024 9月 27 14:30
 */
class PriceAlertsManageViewModel : BaseViewModel() {

    val isEditLiveData: MutableLiveData<Boolean> by lazy { MutableLiveData() }

    val priceAlertListLiveData: MutableLiveData<List<ProduceAlterGroupListData>?> by lazy { MutableLiveData() }
    var oldData: List<String?>? = null
    private val dataList: ArrayList<ShareProductData> by lazy { ArrayList() }

    /**
     * 获取价格提醒列表
     */
    fun getPriceWarn(symbol: String = "", isShowDialog: Boolean = false) {
        requestNet({
            val map = mapOf(
                "data" to mapOf<String, Any?>(
                    "login" to UserDataUtil.accountCd(),
                    "serverId" to UserDataUtil.serverId(),
                    "symbol" to symbol
                ).json
            )
            tradingService.tradeProductGetPriceWarn(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            priceAlertListLiveData.value = it.data
        }, onError = {
            priceAlertListLiveData.value = null
        }, isShowDialog = isShowDialog)
    }

    /**
     * 开启子线程，匹配公共数据里的产品，刷新买卖价格、涨跌幅
     */
    fun updatePrice() {
        viewModelScope.launch(Dispatchers.Default) {
            while (true) {
                priceAlertListLiveData.value?.forEach {
                    it.list?.forEach loop@{ symbolData ->
                        var symbol: ShareProductData? = dataList.firstOrNull { it.symbol == symbolData.symbol }
                        if (symbol != null) {
                            symbolData.ask = symbol.askUI
                            symbolData.bid = symbol.bidUI
                            symbolData.rate = symbol.roseUI
                            return@loop
                        }
                        symbol = VAUSdkUtil.symbolList().firstOrNull { symbolData.symbol == it.symbol } ?: return@loop
                        symbolData.ask = symbol.askUI
                        symbolData.bid = symbol.bidUI
                        symbolData.rate = symbol.roseUI
                        dataList.add(symbol)
                    }
                }
                delay(400)
            }
        }
    }

    /**
     * 批量删除价格提醒
     */
    fun deletePriceWarn(index: Int) {
        viewModelScope.launch(Dispatchers.Default) {
            val list = if (index == 0) {
                // all tab 的时候 过滤 所有选中item 使用asSequence提升效率
                priceAlertListLiveData.value?.asSequence()?.flatMap { it.list.orEmpty() }
            } else {
                // 其他页面 过滤对应index 的选中item
                priceAlertListLiveData.value?.getOrNull(index - 1)?.list?.asSequence()
            }?.flatMap { it.list.orEmpty() }?.filter { it.isSelect }?.toList()?.map { it.id }
            requestNet({
                val map = mapOf(
                    "data" to mapOf<String, Any?>(
                        "serverId" to UserDataUtil.serverId(),
                        "ids" to list
                    ).json
                )
                tradingService.tradeProductDeletePriceWarn(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
            }, onSuccess = {
                if (!it.isSuccess())
                    return@requestNet
                val alertList = priceAlertListLiveData.value?.toMutableList() ?: return@requestNet
                // 遍历删除对应 id 的 item
                if (index == 0) {
                    alertList.forEach { produceData ->
                        produceData.list?.forEach { groupData ->
                            groupData.list = groupData.list?.filterNot { list?.contains(it.id) == true }?.toMutableList()
                        }
                        produceData.count = produceData.list?.size.ifNull().toString()
                    }
                } else {
                    alertList.getOrNull(index - 1)?.let {
                        it.list?.forEach { groupData ->
                            groupData.list = groupData.list?.filterNot { list?.contains(it.id) == true }?.toMutableList()
                        }
                        it.count = it.list?.size.ifNull().toString()
                    }
                }

                // 删除空的 produceData
                alertList.forEach { produceData ->
                    produceData.list = produceData.list?.filterNot { it.list.isNullOrEmpty() }?.toMutableList()
                }

                // 删除空的 groupData
                alertList.removeAll { it.list.isNullOrEmpty() }
                ToastUtil.showToast(StringUtil.getString(R.string.alert_deleted))
                isEditLiveData.value = false
                // 更新 LiveData
                priceAlertListLiveData.value = alertList.toMutableList()
            })
        }

    }

    /**
     * 开启或关闭价格提醒
     */
    fun enableAndDisablePriceWarn(data: ProduceAlterData?) {
        requestNet(
            {
                val map = mapOf(
                    "data" to mapOf<String, Any?>(
                        "serverId" to UserDataUtil.serverId(),
                        "enable" to data?.enable,
                        "id" to data?.id,
                    ).json
                )
                tradingService.tradeProductEnableAndDisablePriceWarn(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
            },
            onSuccess = {
                if (!it.isSuccess())
                    return@requestNet

            })
    }

    companion object {
        const val ADAPTER_SELECT = "select"
        const val ADAPTER_ENABLE = "enable"
        const val ADAPTER_PRICE = "price"
        const val ADAPTER_EDIT = "edit"
        const val ADAPTER_SHOW_LIST = "show_list"
    }
}