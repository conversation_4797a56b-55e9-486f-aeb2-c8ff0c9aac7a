package cn.com.vau.profile.activity.passkey.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.account.*
import cn.com.vau.data.account.CheckPassKeyAnd2fa
import cn.com.vau.util.ToastUtil

class PasskeySettingViewModel : BaseViewModel() {
    val liveDataPasskeyList by lazy {
        MutableLiveData<PasskeyBeanList>()
    }
    val liveDataPasskeyRemoveOrRename by lazy {
        MutableLiveData<String>()
    }
    val liveDataCheckPassKeyAnd2fa by lazy {
        MutableLiveData<CheckPassKeyAnd2fa.Obj>()
    }

    fun getPasskeyListApi() {
        requestNet({
            val map = mutableMapOf(
                "token" to UserDataUtil.loginToken()
            )
            baseService.passKeyListApi(map)
        },
            {
                if (it.isSuccess()) {
                    liveDataPasskeyList.value = it.data
                }
            })
    }

    fun passkeyRemoveApi(passkeyBean: PasskeyBean) {
        requestNet({
            val map = mutableMapOf(
                "passKeyId" to passkeyBean.passKeyId,
                "token" to UserDataUtil.loginToken()
            )
            baseService.passKeyDeleteApi(map)
        },
            {
                if (it.isSuccess()) {
                    liveDataPasskeyRemoveOrRename.value = DELETE_PASSKEY
                } else {
                    ToastUtil.showToast(it.getResponseMsg())
                }
            }, isShowDialog = true)
    }

    fun passkeyRenameApi(passkeyBean: PasskeyBean, displayName: String) {
        requestNet({
            val map = mutableMapOf(
                "passKeyId" to passkeyBean.passKeyId,
                "displayName" to displayName,
                "token" to UserDataUtil.loginToken()
            )
            baseService.passKeyUpdateDisplayNameApi(map)
        },
            {
                if (it.isSuccess()) {
                    liveDataPasskeyRemoveOrRename.value = RENAME_PASSKEY
                } else {
                    ToastUtil.showToast(it.getResponseMsg())
                }
            }, isShowDialog = true)
    }

    fun checkPasskeyAnd2faApi() {
        requestNet({
            val map = mutableMapOf("token" to UserDataUtil.loginToken())
            baseService.passKeyHasSetApi(map)
        }, {
            if (it.isSuccess()) {
                liveDataCheckPassKeyAnd2fa.value = it.data?.obj
            }else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = true)
    }

    companion object {
        const val DELETE_PASSKEY = "delete_passkey"
        const val RENAME_PASSKEY = "rename_passkey"
    }

}