package cn.com.vau.profile.activity.twoFactorAuth.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.page.user.loginPwd.LoginVeriParam
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity
import cn.com.vau.util.*

/**
 * Filename: TFAVerifyViewModel.kt
 * Author: GG
 * Date: 2024/2/21
 * Description:
 */
class TFAVerifyViewModel : BaseViewModel() {

    val paramLiveData: MutableLiveData<LoginVeriParam?> by lazy { MutableLiveData() }

    val verifyResultLiveData by lazy { MutableLiveData<ApiResponse<LoginDataBean>>() }

    var smsSendType: String? = null

    // 发送密码 并校验密码，成功就发送验证码
    val getSmsResultLiveData by lazy { MutableLiveData<ApiResponse<ChangeTFAOtpData>>() }

    val verify2FAResultLiveData by lazy { MutableLiveData<Any>() }

    var signUpRequestBean: SignUpRequestBean? = null

    /**
     * 登录
     */
    fun loginNewApi(code: String?, recaptcha: String = "") {
        val map = hashMapOf<String, Any?>()
        map["countryCode"] = if (paramLiveData.value?.nextType == 0) paramLiveData.value?.countryCode ?: UserDataUtil.countryCode() else null
        map["code"] = if (paramLiveData.value?.nextType == 0) paramLiveData.value?.areaCode ?: UserDataUtil.areaCode() else null
        map["count"] = if (paramLiveData.value?.nextType == 0) paramLiveData.value?.mobile else paramLiveData.value?.email
        map["userPassword"] = paramLiveData.value?.userPassword ?: UserDataUtil.userPwd()
        map["recaptcha"] = recaptcha
        map["type"] = "16" // validateCode 如果是2FA的验证码，type全部传16
        map["validateCode"] = code
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({
            baseService.loginNewApi(paramMap)
        }, {
            verifyResultLiveData.value = it
        }, isShowDialog = true)
    }

    /**
     * 发送验证码 通过sms或者whatsapp
     */
    fun getTelSmsApi(recaptcha: String = "") {
        val map = hashMapOf<String, Any?>()
        map["phoneCountryCode"] = paramLiveData.value?.countryCode ?: UserDataUtil.countryCode()
        map["code"] = paramLiveData.value?.areaCode ?: UserDataUtil.areaCode()
        map["userTel"] = paramLiveData.value?.mobile ?: UserDataUtil.userTel()
        map["type"] = "10"
        map["recaptcha"] = recaptcha
        map["smsCodeId"] = SpManager.getSmsCodeId("").ifNull()
        map["smsSendType"] = smsSendType
        map["userPassword"] = paramLiveData.value?.userPassword
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({
            baseService.getTelSmsApi(paramMap)
        }, {
            getSmsResultLiveData.value = it
        }, isShowDialog = true)
    }

    /**
     * 第三方绑定
     */
    fun thirdBindApi(code: String?, recaptcha: String = "") {

        val map = hashMapOf<String, Any?>()
        map["userPassword"] = paramLiveData.value?.userPassword
        // 易盾
        map["recaptcha"] = recaptcha
        // 2fa的code
        map["validateCode"] = code
        // 2fa验证的type 都是16
        map["type"] = "16"
        if (paramLiveData.value?.nextType == 0) {
            map["code"] = paramLiveData.value?.areaCode
            map["userTel"] = paramLiveData.value?.mobile
        } else {
            map["userEmail"] = paramLiveData.value?.email
        }
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({
            baseService.thirdPartyBindApi(paramMap)
        }, {
            verifyResultLiveData.value = it
        }, isShowDialog = true)
    }

    /**
     * 第三方登录
     */
    fun thirdLoginApi(code: String?, recaptcha: String = "") {
        val map = hashMapOf<String, Any?>()
        map["validateCode"] = code.ifNull()
        map["type"] = "16"
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
        if (recaptcha.isNotBlank()) {
            map["recaptcha"] = recaptcha.ifNull()
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({
            baseService.thirdpartyLoginApi(paramMap)
        }, {
            verifyResultLiveData.value = it
        }, isShowDialog = true)
    }

    /**
     * 2fa验证
     */
    fun twoFactorValidateCodeApi(authCode: String?, pageFrom: String?) {
        requestNet({
            //新增参数- 业务类型
            //1: 2fa换绑（2fa换绑校验原验证器code时添加）
            //2: passkey
            //3: 出入金
            //4: 登录（预留，暂时未使 用）
            //5: 修改邮箱
            baseService.twoFactorValidateCodeApi(
                authCode = authCode, token = UserDataUtil.loginToken(), bizType = when (pageFrom) {
                    TFAVerifyActivity.FROM_CHANGE_TFA -> "1"
                    TFAVerifyActivity.FROM_WITHDRAWALS -> "3"
                    TFAVerifyActivity.FROM_KYC_UPDATE_EMAIL -> "5"
                    else -> "4"
                }
            )
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            verify2FAResultLiveData.value = it.data
        }, isShowDialog = true)
    }
}