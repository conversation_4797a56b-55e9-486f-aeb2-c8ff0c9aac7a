package cn.com.vau.profile.activity.pricealert.viewmodel

import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.pricealtert.ProduceAlterData
import cn.com.vau.util.*
import kotlin.math.pow

class CreatePriceAlertViewModel : BaseViewModel() {

    var groupName: String? = null
    var data: ShareProductData? = null

    var productName: String = ""
    var isEdit: Boolean = false

    /**
     * 价格提醒的数据类，默认为空，如果是编辑，则有值
     */
    var alterData: ProduceAlterData? = null

    val percentLimitCount = 2

    /**
     * 方向 0：buy 1：sell
     */
    var direction: String = "1"

    /**
     * 提醒类型
     */
    var alertType: Int = 0

    /**
     * 提醒频率
     * 0 -> Once only（默认）：第一次达到触发条件后发送通知，只发一次
     * 1 -> Once every 24h：每24小时内达到触发条件，发送一次通知。每次通知后，在接下来的24小时内，即使达到触发条件，也不会发送新的通知
     * 2 -> Everytime：每次达到触发条件后发送通知
     */
    var frequency: Int = 0

    fun getDigits(): Int {
        return data?.digits.ifNull()
    }

    fun getMinProfit(): String {
        data?.let {
            return "${1 / 10.toDouble().pow((it.digits).toDouble())}"
        }
        return "0.0"

    }

    fun getMinPercent(): String {
        return "0.01"
    }

    /**
     * 添加 或 修改 价格提醒 ，添加不传id
     */
    fun addOrUpdatePriceAlert(value: String) {
        if (value.mathCompTo("0") == 0) {
            ToastUtil.showToast(StringUtil.getString(R.string.input_value_must_be_greater_than_0))
            return
        }

        requestNet({
            val map = mapOf(
                "data" to mapOf<String, Any?>(
                    "login" to UserDataUtil.accountCd(),
                    "serverId" to UserDataUtil.serverId(),
                    "userId" to UserDataUtil.userId(),
                    "symbol" to data?.symbol,
                    "groupName" to groupName,
                    "points" to if (data?.pips == 0) 0 else data?.pips.ifNull() / 10.0.pow(data?.digits.ifNull().toDouble()),
                    "direction" to direction,
                    "alertType" to alertType,
                    "value" to value,
                    "frequency" to frequency,
                    "enable" to "1",
                    "id" to alterData?.id
                ).json
            )
            tradingService.tradeProductAddOrUpdatePriceWarn(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
        }, onSuccess = {
            if (!it.isSuccess()) {
                sendEvent(DataEvent(it.getResponseCode(), it.getResponseMsg()))
                return@requestNet
            }
            if (isEdit)
                ToastUtil.showToast(StringUtil.getString(R.string.alert_updated))
            else
                ToastUtil.showToast(StringUtil.getString(R.string.alert_added))
            sendEvent(DataEvent("success"))
        }, onError = {

        }, isShowDialog = true)
    }

    /**
     * 删除价格提醒
     */
    fun deletePriceWarn() {
        requestNet({
            val map = mapOf(
                "data" to mapOf<String, Any?>(
                    "serverId" to UserDataUtil.serverId(),
                    "ids" to listOf(alterData?.id)
                ).json
            )
            tradingService.tradeProductDeletePriceWarn(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            ToastUtil.showToast(StringUtil.getString(R.string.alert_deleted))
            sendEvent(DataEvent("success"))
        }, onError = {

        })
    }

}