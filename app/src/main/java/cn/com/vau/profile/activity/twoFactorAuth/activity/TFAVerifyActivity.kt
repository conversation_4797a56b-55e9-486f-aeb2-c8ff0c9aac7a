package cn.com.vau.profile.activity.twoFactorAuth.activity

import android.content.*
import android.os.*
import android.text.TextUtils
import androidx.activity.*
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.PasswordView
import cn.com.vau.data.account.LoginDataBean
import cn.com.vau.databinding.ActivityTfaVerifyBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.*
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity.VerifyEmailCodeType
import cn.com.vau.page.login.activity.VerifySmsCodeActivity.VerifySmsCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.page.user.loginPwd.LoginVeriParam
import cn.com.vau.profile.activity.twoFactorAuth.viewmodel.TFAVerifyViewModel
import cn.com.vau.profile.adapter.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.FirebaseManager
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.netease.nis.captcha.*
import kotlinx.coroutines.*
import org.greenrobot.eventbus.EventBus

/**
 * 身份验证器验证
 * Author: GG
 * Date: 2024/2/21
 * Description:
 */
class TFAVerifyActivity : BaseMvvmActivity<ActivityTfaVerifyBinding, TFAVerifyViewModel>() {

    private var captcha: Captcha? = null

    private val pageFrom: String? by lazy { intent.getStringExtra(KEY_FROM_TYPE) }

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()

    private val switchAdapter: SwitchVerifyAdapter by lazy {
        if (SpManager.isV1V2()) {
            SwitchVerifyAdapter(
                arrayListOf<SwitchVerifyData>().apply {
                    val list = mViewModel.paramLiveData.value?.needAuthOptList
                    if (list?.contains("phone") == true) {
                        add(SwitchVerifyData(getString(R.string.send_otp_via_whatsapp), false))
                        add(SwitchVerifyData(getString(R.string.send_otp_via_sms), list.size == 1))
                    }
                    if (list?.contains("email") == true) {
                        add(SwitchVerifyData(getString(R.string.send_otp_via_email), true))
                    }
                    add(SwitchVerifyData(getString(R.string.reset_two_factor_authentication), false))
                }
            )
        } else {
            SwitchVerifyAdapter(
                arrayListOf<SwitchVerifyData>().apply {
                    add(SwitchVerifyData(getString(R.string.send_otp_via_whatsapp), false))
                    add(SwitchVerifyData(getString(R.string.send_otp_via_sms), true))
                    add(SwitchVerifyData(getString(R.string.reset_two_factor_authentication), false))
                }
            )
        }
    }

    private val selectPopup by lazy {
        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.switch_authentication_method))
            .setAdapter(switchAdapter)
            .build()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.paramLiveData.value = intent.getSerializableExtra(KEY_DATA) as? LoginVeriParam
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean") ?: SignUpRequestBean()
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        mBinding.mHeaderBar.setStartBackIconClickListener {
            onBackPressed()
        }
        // 在Activity中处理返回手势
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                EventBus.getDefault().post(DataEvent(NoticeConstants.TFA_VERIFY_BACK))
                finish()
            }
        })

        when (pageFrom) {
            FROM_LOGIN, FROM_TELEGRAM_LOGIN -> {
                if (SpManager.isV1V2() && mViewModel.paramLiveData.value?.needAuthOptList.isNullOrEmpty()) {
                    mBinding.layoutVerify.tvButton.text = getString(R.string.reset_two_factor_authentication)
                } else {
                    mBinding.layoutVerify.tvButton.text = getString(R.string.switch_authentication_method)
                }
            }

            else -> {
                mBinding.layoutVerify.tvButton.text = getString(R.string.reset_two_factor_authentication)
            }
        }
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }

        mBinding.layoutVerify.tvButton.isVisible = true
        mBinding.layoutVerify.tvButton.set(mBinding.layoutVerify.tvButton.text.toString())
        mBinding.layoutVerify.tvButton.typeface = ResourcesCompat.getFont(this, R.font.gilroy_regular)
        mBinding.layoutVerify.tvButton.clickNoRepeat {
            when (pageFrom) {
                FROM_LOGIN, FROM_TELEGRAM_LOGIN -> {
                    mBinding.layoutVerify.passwordView.hiddenSoftInputFromWindow()
                    if (SpManager.isV1V2() && mViewModel.paramLiveData.value?.needAuthOptList.isNullOrEmpty()) {
                        TFAResetActivity.open(this, mViewModel.paramLiveData.value)
                    } else {
                        selectPopup.showDialog()
                    }
                }

                else -> {
                    TFAResetActivity.open(this, mViewModel.paramLiveData.value)
                }
            }
        }

        mBinding.layoutVerify.passwordView.setPasswordListener(object : PasswordView.PasswordListener {
            override fun passwordChange(changeText: String?) {

            }

            override fun passwordComplete() {
                //根据来源不同，请求不同的接口
                when (pageFrom) {
                    FROM_LOGIN -> mViewModel.loginNewApi(mBinding.layoutVerify.passwordView.getPassword())
                    // 入金、换绑、修改邮箱 走验证的接口
                    FROM_WITHDRAWALS, FROM_CHANGE_TFA, FROM_KYC_UPDATE_EMAIL -> mViewModel.twoFactorValidateCodeApi(mBinding.layoutVerify.passwordView.getPassword(), pageFrom)
                    // telegram 登录
                    FROM_TELEGRAM_LOGIN -> mViewModel.thirdLoginApi(mBinding.layoutVerify.passwordView.getPassword(), "")
                    FROM_TELEGRAM_BIND -> mViewModel.thirdBindApi(mBinding.layoutVerify.passwordView.getPassword(), "")
                }
                mBinding.layoutVerify.passwordView.hiddenSoftInputFromWindow()
            }

            override fun keyEnterPress(password: String?, isComplete: Boolean) {
            }

        })
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                delay(300)
                mBinding.layoutVerify.passwordView.showSoftInput()
            }
        }
    }

    override fun onBackPressed() {
        EventBus.getDefault().post(DataEvent(NoticeConstants.TFA_VERIFY_BACK))
        super.onBackPressed()
    }

    override fun initListener() {
        super.initListener()

        //更多重置选项
        switchAdapter.setOnItemClickListener { _, _, position ->
            when (switchAdapter.data.getOrNull(position)?.name) {
                getString(R.string.send_otp_via_whatsapp) -> {
                    if (SpManager.isV1V2()) {
                        mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.WHATSAPP
                        mViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.LOGIN_CHANGE_DEVICE
                        sendCodeViewModel.sendPhoneCodeApi("10")
                    } else {
                        mViewModel.smsSendType = "2"
                        mViewModel.getTelSmsApi()
                    }
                }

                getString(R.string.send_otp_via_sms) -> {
                    if (SpManager.isV1V2()) {
                        mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
                        mViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.LOGIN_CHANGE_DEVICE
                        sendCodeViewModel.sendPhoneCodeApi("10")
                    } else {
                        mViewModel.smsSendType = "1"
                        mViewModel.getTelSmsApi()
                    }
                }

                getString(R.string.send_otp_via_email) -> { // 邮箱OTP
                    if (SpManager.isV1V2()) {
                        mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.EMAIL
                        mViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.LOGIN_CHANGE_DEVICE
                        sendCodeViewModel.sendEmailCodeApi("10")
                    }
                }

                getString(R.string.reset_two_factor_authentication) -> {
                    TFAResetActivity.open(this, mViewModel.paramLiveData.value)
                }
            }
            selectPopup.dismissDialog()
        }

        //登录的接口结果校验
        mViewModel.verifyResultLiveData.observe(this) {
            it?.let {
                if (it.data?.obj?.crmUserId.isNullOrBlank().not()) {
                    SpManager.putCrmUserId(it.data.obj?.crmUserId.ifNull())
                }
                when (it.getResponseCode()) {
                    // 16,17 账户列表
                    "V10017", "V10016" -> { // 用户登陆成功
                        SpManager.putSuperviseNum(it.data?.obj?.regulator ?: "0")
                        saveUserData(it)
                        // Fcm上报推送设备号
                        val localFcmToken = SpManager.getTokenFcm()
                        FirebaseManager.bindFcmToken(localFcmToken)
                        val bundle = Bundle()
                        bundle.putInt(Constants.IS_FROM, 1)
                        openActivity(AccountManagerActivity::class.java, bundle)
                        finish()

                    }

                    "V10060" -> {
                        showCaptcha()
                    }

                    else -> {
                        it.getResponseMsg().let { msg ->
                            ToastUtil.showToast(msg)
                        }
                    }
                }
            }
        }
        //发送短信的接口结果
        mViewModel.getSmsResultLiveData.observe(this) {
            it?.let {
                when (it.getResponseCode()) {
                    "V00000" -> {
                        it.getResponseMsg().let { msg ->
                            ToastUtil.showToast(msg)
                        }
                        if (mViewModel.smsSendType == "1") {
                            VerificationActivity.openActivity(
                                this, VerificationActivity.TYPE_LOGIN, VerificationActivity.TYPE_SEND_SMS, mViewModel.paramLiveData.value
                            )
                        } else {
                            VerificationActivity.openActivity(
                                this, VerificationActivity.TYPE_LOGIN, VerificationActivity.TYPE_SEND_WA, mViewModel.paramLiveData.value
                            )
                        }
                    }

                    "V10060" -> {
                        SpManager.putSmsCodeId(it.data?.obj?.smsCodeId ?: "")
                        showCaptcha()
                    }

                    "V50005" -> {
                        CenterActionDialog.Builder(this)
                            .setContent(it.getResponseMsg())
                            .setSingleButton(true)
                            .setSingleButtonText(getString(R.string.ok))
                            .build().showDialog()
                    }

                    else -> {
                        it.getResponseMsg()?.let { msg ->
                            ToastUtil.showToast(msg)
                        }
                    }
                }
            }
        }

        //2fa验证的接口结果 ， 出金页面使用
        mViewModel.verify2FAResultLiveData.observe(this) {
            it?.let {
                when (pageFrom) {
                    FROM_WITHDRAWALS -> {
                        EventBus.getDefault().post(DataEvent(NoticeConstants.TFA_VERIFY_SUCCESS, mBinding.layoutVerify.passwordView.getPassword()))
                        finish()
                    }

                    FROM_KYC_UPDATE_EMAIL -> { // 修改邮箱，先验证旧邮箱OTP
                        sendCodeViewModel.signUpRequestBean?.tfaCode = mBinding.layoutVerify.passwordView.getPassword()
                        sendCodeViewModel.sendEmailCodeApi("27")
                    }

                    else -> {
                        sendCodeViewModel.signUpRequestBean?.tfaCode = mBinding.layoutVerify.passwordView.getPassword()
                        TFAChangeActivity.open(this, mBinding.layoutVerify.passwordView.getPassword(), sendCodeViewModel.signUpRequestBean)
                    }
                }
            }
        }
    }

    override fun createObserver() {
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            sendCodeViewModel.signUpRequestBean?.fromPage = FromPageType.FROM_PAGE_2FA
            if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
                VerifyEmailCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
            } else {
                VerifySmsCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
            }
        }
    }

    private fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    private fun initCaptcha() {
        //易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
                    sendCodeViewModel.sendEmailCodeApi("10", validate)
                } else {
                    if (SpManager.isV1V2()) {
                        sendCodeViewModel.sendPhoneCodeApi("10", validate)
                    } else {
                        mViewModel.getTelSmsApi(recaptcha = validate)
                    }
                }
            }

            //建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {
            }

            override fun onClose(closeType: Captcha.CloseType) {
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        //成功 + 关闭
                    }
                }
            }
        }
        captcha = CaptchaUtil.getCaptcha(this, loginCaptchaListener)
    }

    /**
     * 登录后保存用户信息
     */
    private fun saveUserData(loginBean: ApiResponse<LoginDataBean>) {
        val userTel = loginBean.data?.obj?.userTel
        val areaCode = loginBean.data?.obj?.code
        val countryCode = loginBean.data?.obj?.countryCode
        val email = loginBean.data?.obj?.email
        UserDataUtil.setUserTel(userTel)
        UserDataUtil.setCountryCode(countryCode)
        UserDataUtil.setAreaCode(areaCode)
        UserDataUtil.setUserId(loginBean.data?.obj?.userId)
        UserDataUtil.setUserType(if (loginBean.getResponseCode() == "V10017") 1 else 0)
        UserDataUtil.setLoginToken(loginBean.data?.obj?.token)
        UserDataUtil.setXToken(loginBean.data?.obj?.xtoken)
        val fastCloseState = loginBean.data?.obj?.fastCloseState
        UserDataUtil.setFastCloseState(if (TextUtils.isEmpty(fastCloseState)) "2" else fastCloseState)
        val fastCloseCopyOrder = loginBean.data?.obj?.fastCloseCopyOrder // 快速停止跟单
        UserDataUtil.setFastStopCopyState(if (TextUtils.isEmpty(fastCloseCopyOrder)) "2" else fastCloseCopyOrder)
        val orderConfirmation = loginBean.data?.obj?.orderConfirmation
        UserDataUtil.setOrderConfirmState(if (TextUtils.isEmpty(orderConfirmation)) "2" else orderConfirmation)
        UserDataUtil.setEmail(email)
        UserDataUtil.setUserNickName(loginBean.data?.obj?.userNick)
        UserDataUtil.setUserPic(loginBean.data?.obj?.pic)
        UserDataUtil.setUserPassword(mViewModel.paramLiveData.value?.userPassword)

        LogEventUtil.mFirebaseAnalytics.setUserId(areaCode + userTel)

        // 手机号
        if (mViewModel.paramLiveData.value?.nextType == 0) {
            val userPhoneHistory = UserPhoneHistory()
            userPhoneHistory.phoneNumber = userTel
            DbManager.getInstance().saveUserPhoneHistory(userPhoneHistory)
            SpManager.putUserTel(userTel.ifNull())
            SpManager.putCountryCode(countryCode.ifNull()) // 国家code
            SpManager.putCountryNum(areaCode.ifNull())     // 区号
            //            SPUtil.saveData(context, StoreConstants.COUNTRY_NAME, areaCodeData.countryName ?: "")
        } else {
            // 邮箱
            val userEmailHistory = UserEmailHistory()
            userEmailHistory.email = email
            DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
        }

        // 绑定神策业务ID，场景4、场景2
        val userBean = loginBean.data?.obj
        SensorsDataUtil.bindBusinessIdForLogin(
            userBean?.userTel, userBean?.email, userBean?.emailEventID, userBean?.crmUserId
        )

        // 登录firebase
        FirebaseManager.userLogin()

        if (mViewModel.paramLiveData.value?.handleType == 1) EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
    }

    companion object {

        private const val KEY_FROM_TYPE = "from_type"
        private const val KEY_DATA = "data"

        const val FROM_LOGIN = "login"

        const val FROM_WITHDRAWALS = "withdrawals"

        const val FROM_CHANGE_TFA = "change_tfa"

        const val FROM_SETTING = "setting"

        const val FROM_TELEGRAM_LOGIN = "telegram_login" // telegram登录

        const val FROM_TELEGRAM_BIND = "telegram_bind" // telegram 绑定

        const val FROM_KYC_UPDATE_EMAIL = "kyc_update_email" // kyc修改邮箱

        /**
         * fromType为FROM_LOGIN时 需要传 bean: LoginVeriParam
         * FROM_WITHDRAWALS 时不需要
         */
        fun open(context: Context, fromType: String, bean: LoginVeriParam? = null, signUpRequestBean: SignUpRequestBean? = null) {
            context.startActivity(Intent(context, TFAVerifyActivity::class.java).apply {
                putExtra(KEY_FROM_TYPE, fromType)
                putExtra(KEY_DATA, bean)
                putExtra("signUpRequestBean", signUpRequestBean)
            })
        }

    }
}