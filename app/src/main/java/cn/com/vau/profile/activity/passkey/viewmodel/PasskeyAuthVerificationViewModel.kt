package cn.com.vau.profile.activity.passkey.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.data.account.VerificationResult
import cn.com.vau.util.ToastUtil


class PasskeyAuthVerificationViewModel: BaseViewModel() {
    val liveDataVerification by lazy {
        MutableLiveData<VerificationResult>()
    }
    val liveDataTwoFactorVerification by lazy {
        MutableLiveData<ApiResponse<*>>()
    }
    val liveDataSendEmailCode by lazy {
        MutableLiveData<ApiResponse<*>>()
    }

    fun verificationPasskeyApi(jsonData:String, passKeyId:String) {
        requestNet({
            val map = mutableMapOf(
                "jsonData" to jsonData,
                "passKeyId" to passKeyId,
                "token" to UserDataUtil.loginToken()
            )
            baseService.passkeyValidateApi(map)
        },{
            if (it.isSuccess()){
                liveDataVerification.value = it.data
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = true)
    }

    fun twoFactorValidateCodeApi(authCode: String?) {
        requestNet({
            //新增参数- 业务类型
            //1: 2fa换绑（2fa换绑校验原验证器code时添加）
            //2: passkey
            //3: 出入金
            //4: 登录（预留，暂时未使用）
            baseService.twoFactorValidateCodeApi(authCode, UserDataUtil.loginToken() , bizType =  "2")
        },{
            if (it.isSuccess()) {
                liveDataTwoFactorVerification.value = it
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = true)
    }

    fun passKeyValidateEmailCodeApi(authCode: String?) {
        requestNet({
            baseService.passKeyValidateEmailCodeApi(authCode, UserDataUtil.loginToken())
        },
            {
                if (it.isSuccess()) {
                    liveDataTwoFactorVerification.value = it
                } else {
                    ToastUtil.showToast(it.getResponseMsg())
                }
            }, isShowDialog = true)
    }

    fun passKeySendEmailCodeApi() {
        requestNet({
            baseService.passKeySendEmailCodeApi(UserDataUtil.loginToken())
        },
            {
                if (it.isSuccess()) {
                    liveDataSendEmailCode.value = it
                } else {
                    ToastUtil.showToast(it.getResponseMsg())
                }

            }, isShowDialog = true)
    }
}