package cn.com.vau.home.adapter

import android.app.Activity
import androidx.appcompat.widget.AppCompatImageView
import cn.com.vau.R
import cn.com.vau.common.view.popup.NewVersionGuideBean
import cn.com.vau.util.ImageLoaderUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class NewVersionGuideAdapter : BaseQuickAdapter<NewVersionGuideBean, BaseViewHolder>(R.layout.item_new_version_guide) {

    override fun convert(holder: BaseViewHolder, item: NewVersionGuideBean) {
        holder.setText(R.id.tvGuideTitle, item.name)
            .setText(R.id.tvGuideContent, item.description)
        val img = holder.getView<AppCompatImageView>(R.id.ivGuideImage)
        // firebase 有报Fatal Exception: java.lang.IllegalArgumentException You cannot start a load for a destroyed activity
        // 所以使用前进行判断
        if (img.context is Activity) {
            val activity = img.context as Activity
            if (activity.isDestroyed || activity.isFinishing) {
                return
            }
        }
        if (!item.imgUrl.isNullOrEmpty()) {
            ImageLoaderUtil.loadImage(img, item.imgUrl, img, R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r10)
        }

    }
}