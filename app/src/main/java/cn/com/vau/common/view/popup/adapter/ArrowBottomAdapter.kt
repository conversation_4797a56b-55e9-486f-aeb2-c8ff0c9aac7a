package cn.com.vau.common.view.popup.adapter

import cn.com.vau.R
import cn.com.vau.profile.adapter.SelectBean
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class ArrowBottomAdapter : BaseQuickAdapter<SelectBean, BaseViewHolder>(R.layout.item_recycler_text_with_right_arrow) {
    override fun convert(holder: BaseViewHolder, item: SelectBean) {
        holder.setText(R.id.tvContent, item.getShowItemValue())
    }
}