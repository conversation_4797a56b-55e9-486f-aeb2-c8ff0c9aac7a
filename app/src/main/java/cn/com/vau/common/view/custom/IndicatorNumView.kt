package cn.com.vau.common.view.custom

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.databinding.ViewIndicatorNumberBinding
import cn.com.vau.util.AttrResourceUtil

class IndicatorNumView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayoutCompat(context, attrs, defStyleAttr) {

    private var mBinding: ViewIndicatorNumberBinding? = null

    init {
        mBinding = ViewIndicatorNumberBinding.inflate(LayoutInflater.from(context), this, true)
    }
    private var indicatorCount = 0

    fun initIndicatorCount(count:Int){
        this.indicatorCount = count
        changeIndicator(0)
    }

    fun changeIndicator(position: Int) {
        if (isVisible) {
            val current = position + 1
            mBinding?.tvCurrent?.text = buildSpannedString {
                color(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)) {
                    append("$current")
                }
                color(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff)) {
                    append("/$indicatorCount")
                }
            }
        }
    }
}