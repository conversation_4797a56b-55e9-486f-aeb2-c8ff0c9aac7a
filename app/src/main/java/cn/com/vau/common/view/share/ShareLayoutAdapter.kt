package cn.com.vau.common.view.share

import android.graphics.Outline
import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewOutlineProvider
import android.view.ViewStub
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.text.buildSpannedString
import androidx.core.text.inSpans
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.databinding.VsShareStrategy1Binding
import cn.com.vau.databinding.VsShareStrategy2Binding
import cn.com.vau.databinding.VsShareStrategy3Binding
import cn.com.vau.util.AppUtil
import cn.com.vau.util.CustomTypefaceSpan
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.widget.WaterView
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: ShareLayoutAdapter
 * Author: GG
 * Date: 2023/9/12 0012 10:48
 * Description:
 */
class ShareLayoutAdapter(private val isIB: Boolean, var isShowIBAccount: Boolean = true, var shareTheme: Int? = null, var isShowWatermark: Boolean = false, var isShowUserName: Boolean = true) : BaseMultiItemQuickAdapter<ShareData, BaseViewHolder>() {

    private var qrCodeLoadListener: ((Boolean) -> Unit)? = null

    init {
        addItemType(SharePopup.TYPE_KLINE, R.layout.layout_share_k_line)
        addItemType(SharePopup.TYPE_STRATEGY_DETAIL, R.layout.layout_share_strategy_detail)
        addItemType(SharePopup.TYPE_STRATEGY_ORDER, R.layout.layout_share_strategy_order)
        addItemType(SharePopup.TYPE_STRATEGY_ORDER_HISTORY, R.layout.layout_share_strategy_order)
        addItemType(SharePopup.TYPE_ORDER, R.layout.layout_share_order)
        addItemType(SharePopup.TYPE_ORDER_HISTORY, R.layout.layout_share_order)
        addItemType(SharePopup.TYPE_RAF, R.layout.layout_share_invite)
        addItemType(SharePopup.TYPE_SCREENSHOT, R.layout.layout_share_screenshot)
    }

    private val customTypeface by lazy { ResourcesCompat.getFont(context, R.font.gilroy_bold) }

    override fun convert(holder: BaseViewHolder, item: ShareData) {
        val qrView = holder.getView<ImageView>(R.id.ivQrCode)
        item.qrCodeUrl?.let {
            ImageLoaderUtil.loadImageWithListener(
                context, item.qrCodeUrl, qrView, RequestOptions()
                    .placeholder(R.drawable.img_invite_default_code)
                    .error(R.drawable.img_invite_default_code), object : RequestListener<Drawable> {
                    override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable?>, isFirstResource: Boolean): Boolean {
                        qrCodeLoadListener?.invoke(false)
                        return false
                    }

                    override fun onResourceReady(resource: Drawable, model: Any, target: Target<Drawable?>?, dataSource: DataSource, isFirstResource: Boolean): Boolean {
                        qrCodeLoadListener?.invoke(true)
                        return false
                    }
                })
        }

        if (UserDataUtil.isLogin()) {
            holder.setText(
                R.id.tvBottomTitle, if (holder.itemViewType != SharePopup.TYPE_RAF && holder.itemViewType != SharePopup.TYPE_SCREENSHOT && isIB && isShowIBAccount) {
                    context.getString(R.string.account_no) + ": ${item.shareAccount ?: ""}"
                } else {
                    context.getString(R.string.referral_code) + ": ${item.shareCode ?: ""}"
                }
            )
        }
        when (holder.itemViewType) {
            SharePopup.TYPE_KLINE -> {
                if (item.kLineBitmap == null) {
                    return
                }
                configKLine(holder, item)
                configTime(holder)
            }

            SharePopup.TYPE_STRATEGY_DETAIL -> {
                configStrategyTrade(holder, item)
                configImage(holder.getViewOrNull(R.id.viewTop))
                configTime(holder)
                configCard(holder)
            }

            SharePopup.TYPE_STRATEGY_ORDER, SharePopup.TYPE_STRATEGY_ORDER_HISTORY -> {
                configStrategyOrder(holder, item)
                configImage(holder.getViewOrNull(R.id.viewTop))
                configTime(holder)
                configCard(holder)
            }

            SharePopup.TYPE_ORDER, SharePopup.TYPE_ORDER_HISTORY -> {
                configOrder(holder, item)
                configImage(holder.getViewOrNull(R.id.viewTop))
                configTime(holder)
                configCard(holder)
            }

            SharePopup.TYPE_RAF -> {
                if (item.topImgUrl.isNullOrBlank()) {
                    holder.setImageResource(R.id.ivTop, R.drawable.img_share_raf)
                } else {
                    ImageLoaderUtil.loadImage(context, item.topImgUrl, holder.getView(R.id.ivTop))
                }
                configImage(holder.getViewOrNull(R.id.ivTop))
                configCard(holder)
            }

            SharePopup.TYPE_SCREENSHOT -> {
                if (item.screenshotBitmap == null) {
                    return
                }
                configScreenshot(holder, item)
                configTime(holder)
            }
        }
    }

    /**
     * k线图页面填充
     */
    private fun configKLine(holder: BaseViewHolder, item: ShareData) {
        holder.setImageBitmap(R.id.ivChart, item.kLineBitmap)
            .setText(R.id.tvHint, context.getString(R.string.check_out_x_latest_price_x, item.symbol, context.getString(R.string.app_name)))
            .setBackgroundColor(R.id.viewBottom, ContextCompat.getColor(context, if (AppUtil.isLightTheme()) R.color.c1e1e1e else R.color.cffffff))
    }

    /**
     * 截屏图页面填充
     */
    private fun configScreenshot(holder: BaseViewHolder, item: ShareData) {
        holder.setImageBitmap(R.id.ivScreenshot, item.screenshotBitmap)
        if (UserDataUtil.isLogin()) {
            holder.setText(R.id.tvHint, context.getString(R.string.scan_the_qr_x_trading_journey, context.getString(R.string.app_name)))
        } else {
            holder.setText(R.id.tvBottomTitle, context.getString(R.string.trade_smarter_with_x, context.getString(R.string.app_name)))
                .setText(R.id.tvHint, context.getString(R.string.scan_the_qr_app_today))
        }
    }

    /**
     * 策略详情 最多交易胜率 分享 策略详情分享的时候 会同时构造这个类型的分享
     */
    private fun configStrategyTrade(holder: BaseViewHolder, item: ShareData) {
        ImageLoaderUtil.loadImageWithOptionCenterCrop(
            context,
            item.avatarUrl,
            holder.getView(R.id.ivAvatar),
            RequestOptions.bitmapTransform(CircleCrop()),
            R.mipmap.ic_launcher
        )

        holder.setText(R.id.tvStrategyName, item.strategyName)
            .setText(R.id.tvId, "ID: ${item.strategyNo}")

        val tvHint = holder.getView<AppCompatTextView>(R.id.tvHint)
        tvHint.text = buildSpannedString {
            append(context.getString(R.string.follow_at_x, context.getString(R.string.app_name)))
            append(" ")
            customTypeface?.let { font ->
                inSpans(CustomTypefaceSpan(font)) {
                    append(item.strategyName.ifNull())
                }
            }
        }

        val viewStub1 = holder.getView<ViewStub>(R.id.viewStub1)
        val viewStub2 = holder.getView<ViewStub>(R.id.viewStub2)
        val viewStub3 = holder.getView<ViewStub>(R.id.viewStub3)

        viewStub1.setOnInflateListener { _, view ->
            val binding = VsShareStrategy1Binding.bind(view)
            binding.tvDetail1.text = item.returnRate3M
            binding.tvDetail1.setTextColor(item.returnRate3MColor)
            binding.tvDetail2.text = item.totalCopiersCount
            binding.tvDetail3.text = item.currentCopiersCount
        }

        viewStub2.setOnInflateListener { _, view ->
            val binding = VsShareStrategy2Binding.bind(view)
            binding.tvDetail1.text = item.returnYTD
            binding.tvDetail1.setTextColor(item.returnYTDColor)
            binding.tvDetail2.text = item.maxMonthlyReturn
            binding.tvDetail2.setTextColor(item.maxMonthlyReturnColor)
        }

        viewStub3.setOnInflateListener { _, view ->
            val binding = VsShareStrategy3Binding.bind(view)
            binding.tvTotalTrade.text = item.totalDealCount
            binding.tvWinRate.text = item.winRate
            binding.tvWinRate.setTextColor(item.winRateColor)
            item.dealInfoList?.onEachIndexed { index, entry ->

                when (index) {
                    0 -> {
                        binding.tvTitle1.text = entry.first
                        binding.tvDetail1.text = entry.second
                        setTextColor(binding.tvDetail1, entry.second)
                    }

                    1 -> {
                        binding.tvTitle2.text = entry.first
                        binding.tvDetail2.text = entry.second
                        setTextColor(binding.tvDetail2, entry.second)
                    }

                    2 -> {
                        binding.tvTitle3.text = entry.first
                        binding.tvDetail3.text = entry.second
                        setTextColor(binding.tvDetail3, entry.second)
                    }

                    else -> {
                        return@onEachIndexed
                    }
                }
            }
        }

        viewStub1.isVisible = item.shareItemType == SharePopup.ITEM_TYPE_1
        viewStub2.isVisible = item.shareItemType == SharePopup.ITEM_TYPE_2
        viewStub3.isVisible = item.shareItemType == SharePopup.ITEM_TYPE_3
        when (item.shareItemType) {
            SharePopup.ITEM_TYPE_3 -> {
                holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_ferrari_d)
            }

            else -> {
                holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_ferrari)
            }
        }
    }

    /**
     * 设置文案颜色
     */
    private fun setTextColor(textView: AppCompatTextView, detail: String) {
        if (detail.mathCompTo("0") == -1) {
            textView.setTextColor(ContextCompat.getColor(context, R.color.ce35728))
        } else {
            textView.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
        }
    }

    /**
     * 跟单策略订单数据填充 跟单策略订单历史数据填充
     */
    private fun configStrategyOrder(holder: BaseViewHolder, item: ShareData) {
        ImageLoaderUtil.loadImageWithOptionCenterCrop(context, item.avatarUrl, holder.getView(R.id.ivAvatar), RequestOptions.bitmapTransform(CircleCrop()), R.mipmap.ic_launcher)
        holder.setText(R.id.tvUserName, item.strategyName)
            .setText(R.id.tvHint, context.getString(R.string.check_out_this_on_x_app, context.getString(R.string.app_name)))
            .setText(R.id.tvTitle, context.getString(R.string.unrealised_roi))
            .setText(R.id.tvCopyTime, item.copyTime)
            .setGone(R.id.tvCopyTime, holder.itemViewType == SharePopup.TYPE_STRATEGY_ORDER)
            .setText(R.id.tvTitle, if (holder.itemViewType == SharePopup.TYPE_STRATEGY_ORDER) R.string.unrealised_roi else R.string.roi)
        when (item.shareItemType) {
            SharePopup.ITEM_TYPE_1 -> {
                holder.setText(R.id.tvDetail1, item.profitRate)
                    .setTextColor(R.id.tvDetail1, item.profitColor)
                    .setGone(R.id.tvDetail2, true)
            }

            else -> {
                holder.setText(R.id.tvDetail1, item.profitRate)
                    .setTextColor(R.id.tvDetail1, item.profitColor)
                    .setText(R.id.tvDetail2, "${item.profit} ${UserDataUtil.currencyType()}")
                    .setTextColor(R.id.tvDetail2, item.profitColor)
                    .setGone(R.id.tvDetail2, false)
            }
        }

        if (shareTheme == ShareSettingData.TYPE_THEME_FERRARI) {
            holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_ferrari)
        } else {
            if (item.profitColor == ContextCompat.getColor(context, R.color.c00c79c)) {
                holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_up)
            } else {
                holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_down)
            }
        }
    }

    /**
     * live 订单以及历史订单 数据填充
     */
    private fun configOrder(holder: BaseViewHolder, item: ShareData) {
        holder.getViewOrNull<AppCompatImageView>(R.id.ivAvatar)?.let {
            ImageLoaderUtil.loadImageWithOptionCenterCrop(context, UserDataUtil.userPic(), it, RequestOptions.bitmapTransform(CircleCrop()), R.mipmap.ic_launcher)
        }
        holder.setVisible(R.id.ivAvatar, isShowUserName)
            .setVisible(R.id.tvUserName, isShowUserName)
            .setVisible(R.id.viewAvatarBg, isShowUserName)

        holder.setVisible(R.id.waterView, isShowWatermark)
        val waterView = holder.getViewOrNull<WaterView>(R.id.waterView)
        waterView?.setWaterName(if (UserDataUtil.nickname().length > 20) UserDataUtil.nickname().take(17) + "..." else UserDataUtil.nickname())
        holder.setVisible(R.id.tvAccountType, UserDataUtil.isDemoAccount())
            .setText(R.id.tvUserName, UserDataUtil.nickname())
            .setText(R.id.tvSymbol, item.symbol)
            .setText(R.id.tvEntryPrice, item.entryPrice)
            .setText(R.id.tvCurrentPrice, item.currentPrice)
            .setText(R.id.tvHint, context.getString(R.string.keep_the_returns_trade_x_app, context.getString(R.string.app_name)))
            .setText(R.id.tvType, OrderUtil.getOrderTypeName(item.direction).uppercase())
        when (item.shareItemType) {
            SharePopup.ITEM_TYPE_1 -> {
                holder.setText(R.id.tvDetail1, item.profitRate)
                    .setTextColor(R.id.tvDetail1, item.profitColor)
                    .setGone(R.id.tvDetail2, true)
            }

            SharePopup.ITEM_TYPE_2 -> {
                holder.setText(R.id.tvDetail1, item.profitRate)
                    .setTextColor(R.id.tvDetail1, item.profitColor)
                    .setText(R.id.tvDetail2, item.profit)
                    .setTextColor(R.id.tvDetail2, item.profitColor)
                    .setGone(R.id.tvDetail2, false)
            }

            else -> {
                holder.setText(R.id.tvDetail1, item.profit)
                    .setTextColor(R.id.tvDetail1, item.profitColor)
                    .setGone(R.id.tvDetail2, true)
            }
        }
        if (holder.itemViewType == SharePopup.TYPE_ORDER) {
            holder.setText(R.id.tvTitleCurrentPrice, context.getString(R.string.current_price))
            when (item.shareItemType) {
                SharePopup.ITEM_TYPE_1 -> {
                    holder.setText(R.id.tvTitle, context.getString(R.string.unrealised_roi))
                }

                SharePopup.ITEM_TYPE_2 -> {
                    holder.setText(R.id.tvTitle, context.getString(R.string.unrealised_roi))
                }

                else -> {
                    holder.setText(R.id.tvTitle, "${context.getString(R.string.unrealised_pl)} (${UserDataUtil.currencyType()})")
                }
            }
        } else {
            holder.setText(R.id.tvTitleCurrentPrice, context.getString(R.string.close_price))
            when (item.shareItemType) {
                SharePopup.ITEM_TYPE_1 -> {
                    holder.setText(R.id.tvTitle, context.getString(R.string.roi))
                }

                SharePopup.ITEM_TYPE_2 -> {
                    holder.setText(R.id.tvTitle, context.getString(R.string.roi))
                }

                else -> {
                    holder.setText(R.id.tvTitle, "P&L(${UserDataUtil.currencyType()})")
                }
            }
        }

        if (shareTheme == ShareSettingData.TYPE_THEME_FERRARI) {
            holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_ferrari)
        } else {
            if (item.profitColor == ContextCompat.getColor(context, R.color.c00c79c)) {
                holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_up)
            } else {
                holder.setBackgroundResource(R.id.viewTop, R.drawable.img_share_down)
            }
        }
        when (item.direction) {
            "1", "3", "5", "7" -> {
                holder.setBackgroundResource(R.id.tvType, R.drawable.shape_cf44040_r100)
            }

            else -> {
                holder.setBackgroundResource(R.id.tvType, R.drawable.shape_c00c79c_r100)
            }
        }
    }

    /**
     * 左右翻转view ，用于适配阿拉伯语
     */
    private fun configImage(view: View?) {
        if (LanguageHelper.isRtlLanguage()) {
            view?.scaleX = -1f
        }
    }

    /**
     * 外层card裁切圆角
     */
    private fun configCard(holder: BaseViewHolder) {
        val card = holder.getView<ConstraintLayout>(R.id.clCard)
        card.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(
                    0,
                    0,
                    view.width,
                    view.height,
                    8f.dp2px()
                )
            }
        }
        card.clipToOutline = true
    }

    fun setQrCodeLoadListener(qrCodeLoadListener: ((Boolean) -> Unit)? = null) {
        this.qrCodeLoadListener = qrCodeLoadListener
    }

    /**
     * 更新时间
     */
    private fun configTime(holder: BaseViewHolder) {
        val tvTime = holder.getView<AppCompatTextView>(R.id.tvTime)
        tvTime.text = context.getString(R.string.shared_on_x, TimeUtil.getNowTime("dd/MM/yyyy"))
    }

}