package cn.com.vau.common.mvvm.network

import androidx.annotation.Keep
import cn.com.vau.util.ifNull

/**
 * author：lvy
 * date：2024/6/18
 * desc：
 */
@Keep
data class ApiResponse<T>(
    // 全部改为private，避免外部调用到造成不统一，直接使用处理好的如：getResponseMsg() 即可
    private val resultCode: String?,
    private val code: String?,
    private val resultType: String? = null,

    private val msgInfo: String?,
    private val msg: String?,
    private val info: String? = null,

    val data: T?,
    val obj: T?, // 有的接口返回结构是obj，后续接口会统一成data
) : BaseResponse<T>() {

    override fun isSuccess(): Boolean {
        val resCode = getResponseCode()
        return "200" == resCode || "V00000" == resCode || "V10000" == resCode || "00000001" == resCode
                || "00000000" == resCode || "H00000000" == resCode || "0" == resCode
    }

    override fun getResponseData() = data ?: obj

    override fun getResponseCode() = if (!resultCode.isNullOrBlank()) {
        resultCode
    } else if (!code.isNullOrBlank()) {
        code
    } else {
        resultType.ifNull()
    }

    override fun getResponseMsg() = if (!msgInfo.isNullOrBlank()) {
        msgInfo
    } else if (!msg.isNullOrBlank()) {
        msg
    } else {
        info.ifNull()
    }
}