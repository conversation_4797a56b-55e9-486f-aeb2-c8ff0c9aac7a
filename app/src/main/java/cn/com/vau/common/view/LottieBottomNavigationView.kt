package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.DrawableRes
import androidx.annotation.Keep
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.databinding.LayoutLottieBottomNavigationBinding
import cn.com.vau.util.*
import cn.com.vau.util.LottieHelper.lottieRawIdsForD
import cn.com.vau.util.LottieHelper.lottieRawIdsForL
import com.airbnb.lottie.LottieAnimationView

/**
 * Filename: LottieBottomNavigationView.kt
 * Author: GG
 * Date: 2023/11/21
 * Description:
 */
class LottieBottomNavigationView @JvmOverloads constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding: LayoutLottieBottomNavigationBinding by lazy { LayoutLottieBottomNavigationBinding.inflate(LayoutInflater.from(context), this) }

    private val tabDataList: MutableList<TabData> by lazy {
        //动效
        val lottieRawIds = if (AppUtil.isLightTheme()) {
            lottieRawIdsForL
        } else {
            lottieRawIdsForD
        }
        //配置icon和动效
        mutableListOf(
            TabData(lottieRawIds.getOrNull(0), R.attr.mainTabHome),
            TabData(lottieRawIds.getOrNull(1), R.attr.mainTabTrades),
            TabData(lottieRawIds.getOrNull(2), R.attr.mainTabDiscover),
            TabData(lottieRawIds.getOrNull(3), R.attr.mainTabPromo),
            TabData(lottieRawIds.getOrNull(4), R.attr.mainTabProfile),
        )
    }

    private var itemClick: ((Int) -> Boolean)? = null

    private var oldPosition = -1

    init {
        initView()
    }

    private fun initView() {
        mBinding.run {
            tabDataList.forEachIndexed { index, _ ->
                setDefaultTab(index)
            }

            viewTrade.setOnClickListener {
                itemClick?.invoke(0)
            }
            viewOrder.setOnClickListener {
                itemClick?.invoke(1)
            }
            viewDiscover.setOnClickListener {
                itemClick?.invoke(2)
            }
            viewPromo.setOnClickListener {
                itemClick?.invoke(3)
            }
            viewProfile.setOnClickListener {
                itemClick?.invoke(4)
            }
        }
    }

    /**
     * 设置选项卡点击监听器
     * @param click 选项卡点击事件回调函数，接收当前选项卡索引作为参数，返回一个布尔值表示是否消费点击事件
     */
    fun setTabClickListener(click: ((Int) -> Boolean)?) {
        itemClick = click
    }

    /**
     * 选中指定索引的选项卡
     * @param index 要选中的选项卡索引
     */
    fun selectItem(index: Int) {
        if (oldPosition != index) {
            setDefaultTab(oldPosition)
            setSelectTab(index)
            oldPosition = index
        }
    }

    /**
     * 设置默认样式
     */
    private fun setDefaultTab(index: Int) {
        when (index) {
            0 -> {
                loadDefaultIcon(mBinding.lavTrade, tabDataList.getOrNull(index)?.unSelectRes)
                mBinding.tvHome.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
            }

            1 -> {
                loadDefaultIcon(mBinding.lavOrder, tabDataList.getOrNull(index)?.unSelectRes)
                mBinding.tvTrade.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
            }

            2 -> {
                loadDefaultIcon(mBinding.lavDiscover, tabDataList.getOrNull(index)?.unSelectRes)
                mBinding.tvDiscover.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
            }

            3 -> {
                loadDefaultIcon(mBinding.lavPromo, tabDataList.getOrNull(index)?.unSelectRes)
                mBinding.tvPromo.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
            }

            4 -> {
                loadDefaultIcon(mBinding.lavProfile, tabDataList.getOrNull(index)?.unSelectRes)
                mBinding.tvProfile.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
            }
        }
    }

    /**
     * 设置未选中图标
     */
    private fun loadDefaultIcon(lottieView: LottieAnimationView, @DrawableRes unSelectRes: Int?) {
        if (lottieView.isAnimating)
            lottieView.cancelAnimation()
        unSelectRes?.let {
            lottieView.setImageResource(AttrResourceUtil.getDrawable(context, it))
        }
    }

    /**
     * 设置选中tab样式
     */
    private fun setSelectTab(index: Int) {
        when (index) {
            0 -> {
                loadSelectIcon(mBinding.lavTrade, tabDataList.getOrNull(index)?.selectRes)
                mBinding.tvHome.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            }

            1 -> {
                loadSelectIcon(mBinding.lavOrder, tabDataList.getOrNull(index)?.selectRes)
                mBinding.tvTrade.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            }

            2 -> {
                loadSelectIcon(mBinding.lavDiscover, tabDataList.getOrNull(index)?.selectRes)
                mBinding.tvDiscover.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            }

            3 -> {
                loadSelectIcon(mBinding.lavPromo, tabDataList.getOrNull(index)?.selectRes)
                mBinding.tvPromo.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            }

            4 -> {
                loadSelectIcon(mBinding.lavProfile, tabDataList.getOrNull(index)?.selectRes)
                mBinding.tvProfile.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            }
        }
    }

    /**
     * 加载选中json动画
     */
    private fun loadSelectIcon(lottieView: LottieAnimationView, res: Int?) {
        res?.let {
            LottieHelper.setAnimation(lottieView, res)
            lottieView.playAnimation()
        }
    }

    /**
     * 获取当前选中的选项卡索引
     * @return 当前选中的选项卡索引，如果未找到匹配的选项卡则返回 0
     */
    fun getSelect(): Int {
        if (oldPosition == -1) {
            resetView()
            return 0
        } else {
            return oldPosition
        }
    }

    private fun resetView() {
        tabDataList.forEachIndexed { index, _ ->
            setDefaultTab(index)
        }
        setSelectTab(0)
    }

    /**
     * 修改指定位置的选项卡标题
     * @param title 新的选项卡标题
     */
    fun changeItemData(title: String?) {
        mBinding.tvPromo.text = title
    }

    /**
     * 设置指定位置的选项卡红点状态
     * @param position 要设置红点状态的选项卡位置
     * @param isShow 是否显示红点
     * @param count 红点上显示的数量（可选参数）
     */
    fun setRedDotState(position: Int, isShow: Boolean) {
        when (position) {
            0 -> {
                if (isShow) {
                    mBinding.vbMsgCountTrade.visibility = VISIBLE
                } else {
                    mBinding.vbMsgCountTrade.visibility = GONE
                }
            }

            1 -> {
                if (isShow) {
                    mBinding.vbMsgCountOrder.visibility = VISIBLE
                } else {
                    mBinding.vbMsgCountOrder.visibility = GONE
                }
            }

            2 -> {
                if (isShow) {
                    mBinding.vbMsgCountDiscover.visibility = VISIBLE
                } else {
                    mBinding.vbMsgCountDiscover.visibility = GONE
                }
            }

            3 -> {
                if (isShow) {
                    mBinding.vbMsgCountPromo.visibility = VISIBLE
                } else {
                    mBinding.vbMsgCountPromo.visibility = GONE
                }
            }

            4 -> {
                if (isShow) {
                    mBinding.vbMsgCountProfile.visibility = VISIBLE
                } else {
                    mBinding.vbMsgCountProfile.visibility = GONE
                }
            }
        }
    }

    @Keep
    data class TabData(var selectRes: Int?, var unSelectRes: Int, var isShowRedDot: Boolean = false, var redDotCount: String? = null)

}