package cn.com.vau.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.databinding.ViewCustomSeekbarBinding
import cn.com.vau.util.KeyboardUtil
import kotlin.let

class CustomSeekBar: ConstraintLayout {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) : super(context, attrs, defStyleAttr) { initView(context, attrs, defStyleAttr) }

    private val mBinding by lazy { ViewCustomSeekbarBinding.inflate(LayoutInflater.from(context), this, true) }
    private val apiAboveO by lazy { Build.VERSION.SDK_INT >= Build.VERSION_CODES.O }
    private var mMin: Int = 0 // min
    private var mMax: Int = 100 // max
    private var mMinScale: String = mMin.toString() // min scale
    private var mMaxScale: String = mMax.toString() // max scale
    private var mMostSelectedScale: String = "35%" // most selected scale
    private var mProgress: Int = 0 // progress value
    private var mProgressDrawable: Drawable? = null // progress drawable
    private var mThumb: Drawable? = null            // thumb drawable
    private var mTextColor: Int = -1    // "Most Selected" color
    private var offset: Int = 0 // 偏移量（由于低版本不支持设置min）
    private var progressCallback: ((Int) -> Unit)? = null

    private fun initView(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) {
        initXmlAttrs(context, attrs, defStyleAttr)
        setValue()
        if (mProgressDrawable != null) {
            mBinding.seekBar.progressDrawable = mProgressDrawable
        }
        if (mThumb != null) {
            mBinding.seekBar.thumb = mThumb
        }
        if (mTextColor != -1) {
            mBinding.tvMostSelected.setTextColor(mTextColor)
        }
        mBinding.seekBar.thumbOffset = 0
        mBinding.seekBar.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val final = progress + offset
                if (fromUser) {
                    progressCallback?.invoke(final)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                if (seekBar != null) {
                    KeyboardUtil.hideSoftInput(seekBar)
                }
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {

            }
        })
    }

    private fun initXmlAttrs(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) {
        attrs?.let {
            val attr = context.obtainStyledAttributes(it, R.styleable.CustomSeekBar, defStyleAttr, 0)
            mMin = attr.getInt(R.styleable.CustomSeekBar_seek_min, 0)
            mMax = attr.getInt(R.styleable.CustomSeekBar_seek_max, 100)
            mProgress = attr.getInt(R.styleable.CustomSeekBar_seek_progress, mMin)
            mProgressDrawable = attr.getDrawable(R.styleable.CustomSeekBar_seek_progress_drawable)
            mThumb = attr.getDrawable(R.styleable.CustomSeekBar_seek_thumb)
            mTextColor = attr.getInteger(R.styleable.CustomSeekBar_most_color, -1)
        }
    }

    @SuppressLint("NewApi")
    private fun setValue() {
        if (mMax - mMin >= 0 && (mProgress in mMin..mMax)) {
            mBinding.tvMin.text = mMinScale
            mBinding.tvMax.text = mMaxScale
            mBinding.tvMostSelected.text = mMostSelectedScale
            mBinding.seekBar.progress = mProgress
            if (apiAboveO) {
                mBinding.seekBar.max = mMax
                mBinding.seekBar.min = mMin
                offset = 0
            } else {
                if (mMin > 0) {
                    offset = mMin - 0
                } else {
                    offset = 0
                }
                mBinding.seekBar.max = mMax - offset
            }
        }
    }

    fun setMinMax(min: Int, max: Int, process: Int = min, minScale: String = min.toString(), maxScale: String = max.toString(), mostSelectedScale: String = "35%") {
        mMin = min
        mMax = max
        mProgress = process
        mMinScale = minScale
        mMaxScale = maxScale
        mMostSelectedScale = mostSelectedScale
        setValue()
    }

    fun setProgressCallBack(callback: ((Int) -> Unit)) {
        this.progressCallback = callback
    }

    fun setProgress(progress: Int) {
        val final = if ((progress - offset) < 0) 0 else (progress - offset)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mBinding.seekBar.setProgress(final, true)
        } else {
            mBinding.seekBar.progress = final
        }
    }

    fun setEnable(enabled: Boolean) {
        mBinding.seekBar.isEnabled = enabled
    }
}