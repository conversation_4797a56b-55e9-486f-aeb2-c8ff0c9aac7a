package cn.com.vau.common.vm

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.mvvm.BaseViewModel
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.init.ImgAdvertInfoObj

/**
 * 此类旨在MainActivity 与其各Fragment之间的通讯服务
 */
class MainViewModel : BaseViewModel() {

    val isDeposited = MutableLiveData<Boolean>()
    val bannerData = MutableLiveData<ImgAdvertInfoObj>()
    val loginStatus = MutableLiveData<Int>()
    var showFragmentIndex = 0

    // st 自主交易顶部 发布策略数量使用
    val strategyCountLiveData = MutableLiveData<String>()

    //用来接收传递首页跳转的筛选类型字段
    val communityLiveData: MutableLiveData<String> by lazy { MutableLiveData() }

    fun setUserDeposited(userDeposited: Boolean) {
        this.isDeposited.value = userDeposited
    }

    fun setBannerData(bannerData: ImgAdvertInfoObj?) {
        this.bannerData.value = bannerData
    }

    fun loginTokenExpire() {
        this.loginStatus.value = Constants.TOKEN_ERROR
    }
}