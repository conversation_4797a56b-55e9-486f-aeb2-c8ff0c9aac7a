<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="DialogBottomTitleStyle" parent="gilroy_700">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:gravity">start</item>
        <item name="android:textSize">20dp</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
    </style>

    <style name="DialogBottomIconTitleStyle" parent="DialogBottomTitleStyle">
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="DialogCenterTitleStyle" parent="DialogBottomIconTitleStyle"/>

    <style name="DialogBottomContentStyle" parent="gilroy_400">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <style name="DialogCenterContentStyle" parent="DialogBottomContentStyle">
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="DialogBottomIconContentStyle" parent="DialogBottomContentStyle">
        <item name="android:lineSpacingExtra">6dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="DialogBottomSubTitleStyle" parent="gilroy_600">
        <item name="android:textSize">16dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:gravity">start</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
    </style>


    <style name="DialogBottomSubContentStyle" parent="DialogBottomContentStyle">
        <item name="android:textColor">?attr/color_ca61e1e1e_c99ffffff</item>
    </style>

    <style name="DialogButtonStyle" parent="gilroy_600">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">1</item>
        <item name="android:padding">14dp</item>
        <item name="android:textSize">16dp</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="DialogBottomLeftButtonStyle" parent="DialogButtonStyle">
        <item name="android:background">@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100</item>
        <item name="android:text">@string/cancel</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:layout_marginEnd">12dp</item>
    </style>

    <style name="DialogCenterLeftButtonStyle" parent="DialogBottomLeftButtonStyle">
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">10dp</item>
    </style>

    <style name="DialogBottomRightButtonStyle" parent="DialogButtonStyle">
        <item name="android:background">@drawable/draw_shape_c1e1e1e_cebffffff_r100</item>
        <item name="android:text">@string/confirm</item>
        <item name="android:textColor">?attr/color_cebffffff_c1e1e1e</item>
    </style>

    <style name="DialogCenterRightButtonStyle" parent="DialogBottomRightButtonStyle">
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">10dp</item>
    </style>

    <style name="DialogBottomLinkStyle" parent="gilroy_600">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">14dp</item>
    </style>

    <style name="DialogCenterLinkStyle" parent="gilroy_500">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">14dp</item>
    </style>

    <style name="DialogIconStyle">
        <item name="android:layout_width">80dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:adjustViewBounds">true</item>
    </style>

    <style name="DialogAgreeStyle" parent="gilroy_400">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:drawableStart">
            @drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14
        </item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">14dp</item>
    </style>


    <style name="PopupAnimStyleTop">
        <item name="android:windowEnterAnimation">@anim/popup_top_in</item>
        <item name="android:windowExitAnimation">@anim/popup_top_out</item>
    </style>


</resources>