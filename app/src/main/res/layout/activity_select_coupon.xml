<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/select_coupon"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etCouponExchange"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        android:layout_marginEnd="4dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r100"
        android:ellipsize="end"
        android:hint="@string/promotion_code"
        android:paddingHorizontal="16dp"
        android:singleLine="true"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@+id/tvExchangeRule"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar" />

    <TextView
        android:id="@+id/tvCouponExchange"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center"
        android:minWidth="46dp"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/submit"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/etCouponExchange"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/etCouponExchange" />

    <ImageView
        android:id="@+id/tvExchangeRule"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/draw_bitmap2_info14x14_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etCouponExchange"
        app:layout_constraintEnd_toStartOf="@+id/tvCouponExchange"
        app:layout_constraintTop_toTopOf="@+id/etCouponExchange" />

    <TextView
        android:id="@+id/tvNotUseCoupon"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        android:paddingTop="8dp"
        android:paddingEnd="0dp"
        android:paddingBottom="8dp"
        android:text="@string/do_not_select"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toEndOf="@id/ivNotUseCoupon"
        app:layout_constraintTop_toBottomOf="@+id/etCouponExchange" />

    <CheckBox
        android:id="@+id/ivNotUseCoupon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:background="@null"
        android:button="@drawable/select_checkbox_agreement"
        android:checked="false"
        android:contentDescription="@string/app_name"
        android:enabled="false"
        app:layout_constraintBottom_toBottomOf="@+id/tvNotUseCoupon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvNotUseCoupon" />

    <cn.com.vau.common.view.system.MyRecyclerView
        android:id="@+id/rcyCouponManager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/margin_vertical_base"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNotUseCoupon" />

    <ViewStub
        android:id="@+id/mVsNoDataScroll"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/vs_layout_no_data_scroll"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNotUseCoupon" />

</androidx.constraintlayout.widget.ConstraintLayout>