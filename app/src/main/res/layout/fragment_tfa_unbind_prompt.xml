<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUnBindPrompt"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:background="@color/c1fe35728"
        android:padding="@dimen/padding_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="To ensure the security of your account, withdrawals are prohibited for %s hours after Authenticator App has been disabled." />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:padding="18dp"
        android:src="@drawable/img_source_2fa_lock"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUnBindPrompt" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/_2fa_enabled"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPrompt1"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/priority_will_be_account_asset_management"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPrompt2"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/are_you_sure_you_want_to_disable"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPrompt1" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/disable_2fa"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>