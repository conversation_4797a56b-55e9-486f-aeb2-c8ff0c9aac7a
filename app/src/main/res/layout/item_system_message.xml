<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 图片应顶部(左右)圆角4 -->
    <cn.com.vau.common.view.system.ScaleImageView
        android:id="@+id/ivImg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:siv_scale="17:8"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/llTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivImg">

        <TextView
            android:id="@+id/tvTradeType"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/ce35728"
            android:textSize="14dp"
            android:visibility="gone"
            tools:text="Trade"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            tools:text="Success" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvSubTitle"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llTitle"
        tools:text="Buy     0.10Lots     AUDJPY" />

    <TextView
        android:id="@+id/tvContent"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSubTitle"
        app:layout_goneMarginTop="8dp"
        tools:text="Order number: 222343813" />

    <TextView
        android:id="@+id/tvDate"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvContent"
        tools:text="31/01/2024 10:39:20" />

    <TextView
        android:id="@+id/tvViewDetail"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="6dp"
        android:padding="6dp"
        android:text="@string/view_detail"
        android:textColor="@color/ce35728"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvDate"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvDate"
        tools:visibility="visible" />

    <!-- placeholder -->
    <View
        android:layout_width="0dp"
        android:layout_height="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDate" />
</androidx.constraintlayout.widget.ConstraintLayout>