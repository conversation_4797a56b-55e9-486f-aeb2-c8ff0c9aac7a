<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    app:behavior_hideable="false"
    app:layout_behavior="android.support.design.widget.BottomSheetBehavior">

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:layout_constraintEnd_toEndOf="@id/llCreditTransfer"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/llCreditTransfer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tvCreditTransferTitle"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="34dp"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="@string/please_choose_to_for_account_x"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ctlTransferCredit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/draw_main_card">

            <TextView
                android:id="@+id/tvTransferCreditTitle"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_horizontal_base"
                android:gravity="center"
                android:text="@string/transfer_credit"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTTransferAmount"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="4dp"
                android:gravity="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintEnd_toStartOf="@+id/tvTTransferCredit"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTransferCreditTitle"
                tools:text="50" />

            <TextView
                android:id="@+id/tvTTransferAmountTitle"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="12dp"
                android:gravity="center"
                android:text="@string/transfer_amount"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvTTransferCreditTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTTransferAmount" />

            <TextView
                android:id="@+id/tvTTransferCredit"
                style="@style/bold_semi_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTTransferAmount"
                app:layout_constraintEnd_toStartOf="@+id/tvTDeductCredit"
                app:layout_constraintStart_toEndOf="@+id/tvTTransferAmount"
                tools:text="5" />

            <TextView
                android:id="@+id/tvTTransferCreditTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:gravity="center"
                android:text="@string/transfer_credit"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTTransferAmountTitle"
                app:layout_constraintEnd_toStartOf="@+id/tvTDeductCreditTitle"
                app:layout_constraintStart_toEndOf="@+id/tvTTransferAmountTitle" />

            <TextView
                android:id="@+id/tvTDeductCredit"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTTransferCredit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvTTransferCredit"
                tools:text="50" />

            <TextView
                android:id="@+id/tvTDeductCreditTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/deduct_credit"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTTransferCreditTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvTTransferCreditTitle" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ctlGiveUpCredit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_horizontal_base"
            android:background="@drawable/draw_main_card">

            <TextView
                android:id="@+id/tvGiveUpTitle"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_horizontal_base"
                android:gravity="center"
                android:text="@string/give_up_credit"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvGTransferAmount"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="4dp"
                android:gravity="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintEnd_toStartOf="@+id/tvGTransferCredit"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvGiveUpTitle"
                tools:text="50" />

            <TextView
                android:id="@+id/tvGTransferAmountTitle"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="12dp"
                android:gravity="center"
                android:text="@string/transfer_amount"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvGTransferCreditTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvGTransferAmount" />

            <TextView
                android:id="@+id/tvGTransferCredit"
                style="@style/bold_semi_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvGTransferAmount"
                app:layout_constraintEnd_toStartOf="@+id/tvGDeductCredit"
                app:layout_constraintStart_toEndOf="@+id/tvGTransferAmount"
                tools:text="5" />

            <TextView
                android:id="@+id/tvGTransferCreditTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:gravity="center"
                android:text="@string/transfer_credit"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvGTransferAmountTitle"
                app:layout_constraintEnd_toStartOf="@+id/tvGDeductCreditTitle"
                app:layout_constraintStart_toEndOf="@+id/tvGTransferAmountTitle" />

            <TextView
                android:id="@+id/tvGDeductCredit"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvGTransferCredit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvGTransferCredit"
                tools:text="50" />

            <TextView
                android:id="@+id/tvGDeductCreditTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/deduct_credit"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvGTransferCreditTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvGTransferCreditTitle" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/tvTransfer"
                style="@style/bold_semi_font"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                android:gravity="center"
                android:text="@string/transfer"
                android:textAlignment="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvGiveUp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvGiveUp"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
                android:gravity="center"
                android:text="@string/give_up"
                android:textAlignment="center"
                android:textColor="?attr/color_cebffffff_c1e1e1e"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvTransfer"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
