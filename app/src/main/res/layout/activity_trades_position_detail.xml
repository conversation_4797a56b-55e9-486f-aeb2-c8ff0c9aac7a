<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/mainLayoutBg"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        app:hb_isShowBack="true"
        app:hb_titleText="@string/position_details"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/mScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvOrderDirection"
                style="@style/gilroy_500"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/shape_c00c79c_r2"
                android:gravity="center"
                android:textColor="@color/cebffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvProdName"
                tools:ignore="SpUsage"
                tools:text="B" />

            <TextView
                android:id="@+id/tvProdName"
                style="@style/gilroy_700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginTop="24dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="16dp"
                app:layout_constraintStart_toEndOf="@+id/tvOrderDirection"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage"
                tools:text="VAU-TEST" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivShare"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="12dp"
                app:layout_constraintBottom_toBottomOf="@id/tvProdName"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvProdName"
                app:srcCompat="@drawable/draw_bitmap2_order_share_c1e1e1e_cebffffff" />

            <cn.com.vau.util.widget.DashLineTextView
                android:id="@+id/tvPnlTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="24dp"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textDirection="ltr"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvProdName"
                tools:ignore="SpUsage"
                tools:text="@string/pnl" />

            <TextView
                android:id="@+id/tvPnl"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:textColor="@color/ce35728"
                android:textDirection="ltr"
                android:textSize="18dp"
                app:layout_constraintStart_toStartOf="@+id/tvPnlTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvPnlTitle"
                tools:ignore="SpUsage"
                tools:text="-0.24" />

            <cn.com.vau.util.widget.DashLineTextView
                android:id="@+id/tvNetPnlTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textDirection="ltr"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvPnlTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvPnlTitle"
                tools:ignore="SpUsage"
                tools:text="@string/net_pnl" />

            <TextView
                android:id="@+id/tvNetPnl"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:textColor="@color/ce35728"
                android:textDirection="ltr"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="@+id/tvNetPnlTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvNetPnlTitle"
                tools:ignore="SpUsage"
                tools:text="-0.30" />

            <View
                android:id="@+id/line1"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginStart="12dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="12dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvPnl"
                app:layout_goneMarginTop="16dp" />

            <TextView
                android:id="@+id/tvVolumeTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="17dp"
                android:text="@string/volume"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/line1"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvVolume"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvVolumeTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvVolumeTitle"
                tools:ignore="SpUsage"
                tools:text="0.01 Lots" />

            <TextView
                android:id="@+id/tvOpenPriceTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="18dp"
                android:text="@string/open_price"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvVolumeTitle"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvOpenPrice"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvOpenPriceTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvOpenPriceTitle"
                tools:ignore="SpUsage"
                tools:text="0.1059" />

            <TextView
                android:id="@+id/tvCurrentPriceTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="18dp"
                android:text="@string/current_price"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOpenPriceTitle"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvCurrentPrice"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvCurrentPriceTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvCurrentPriceTitle"
                tools:ignore="SpUsage"
                tools:text="0.1035" />

            <TextView
                android:id="@+id/tvTpSlTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="18dp"
                android:text="@string/tp_sl"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCurrentPriceTitle"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvTpPrice"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvTpSlTitle"
                app:layout_constraintEnd_toStartOf="@+id/tvSlash"
                app:layout_constraintTop_toTopOf="@+id/tvTpSlTitle"
                tools:ignore="SpUsage,HardcodedText" />

            <TextView
                android:id="@+id/tvSlash"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="/"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvTpSlTitle"
                app:layout_constraintEnd_toStartOf="@+id/tvSlPrice"
                app:layout_constraintTop_toTopOf="@+id/tvTpSlTitle"
                tools:ignore="SpUsage,HardcodedText" />

            <TextView
                android:id="@+id/tvSlPrice"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:text="--"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvTpSlTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvTpSlTitle"
                tools:ignore="SpUsage,HardcodedText" />

            <View
                android:id="@+id/line2"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginStart="12dp"
                android:layout_marginTop="17dp"
                android:layout_marginEnd="12dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTpSlTitle"
                app:layout_goneMarginTop="16dp" />

            <cn.com.vau.util.widget.DashLineTextView
                android:id="@+id/tvChargesTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="17dp"
                android:text="@string/charges"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/line2"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvCharges"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvChargesTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvChargesTitle"
                tools:ignore="SpUsage"
                tools:text="-0.06" />

            <cn.com.vau.util.widget.DashLineTextView
                android:id="@+id/tvSwapTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="18dp"
                android:text="@string/swap"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvChargesTitle"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvSwap"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvSwapTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvSwapTitle"
                tools:ignore="SpUsage"
                tools:text="0.00" />

            <TextView
                android:id="@+id/tvOpenTimeTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="18dp"
                android:text="@string/open_time"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvSwapTitle"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvOpenTime"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvOpenTimeTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvOpenTimeTitle"
                tools:ignore="SpUsage"
                tools:text="04/06/2024 09:59:24" />

            <TextView
                android:id="@+id/tvOrderNoTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="18dp"
                android:text="@string/order_number"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOpenTimeTitle"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvOrderId"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="8dp"
                android:paddingHorizontal="12dp"
                android:paddingVertical="8dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:drawableEndCompat="?attr/icon2EditStrategyFunds"
                app:layout_constraintBottom_toBottomOf="@+id/tvOrderNoTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvOrderNoTitle"
                tools:ignore="SpUsage"
                tools:text="#45634575" />

            <cn.com.vau.util.widget.LoadingButtonView
                android:id="@+id/lbvQuickClose"
                android:layout_width="match_parent"
                android:layout_height="32dp"
                app:lbv_button_text="@string/close__position"
                app:lbv_button_text_color="@color/ce35728"
                app:lbv_button_text_size="12dp"
                app:lbv_icon_start="@drawable/icon2_quick_close"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="25dp"
                app:lbv_icon_start_height="16dp"
                app:lbv_icon_start_width="16dp"
                app:lbv_icon_start_padding="2dp"
                app:lbv_button_background_color="@color/c1fe35728"
                app:layout_constraintTop_toBottomOf="@+id/tvOrderNoTitle" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>