<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/bold_semi_font"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintStart_toStartOf="@id/tabRV"
        app:layout_constraintTop_toBottomOf="@id/tabRV"
        tools:text="EMA1" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountDown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_sub_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle1"
        app:layout_constraintStart_toEndOf="@+id/tvTitle1"
        app:layout_constraintTop_toTopOf="@+id/tvTitle1" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue"
        style="@style/bold_semi_font"
        android:layout_width="100dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center"
        android:imeOptions="flagNoExtractUi"
        android:inputType="number"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountDown1"
        app:layout_constraintStart_toEndOf="@+id/ivHandCountDown1"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountDown1"
        tools:ignore="HardcodedText"
        tools:text="0.01" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountUp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_add_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue1"
        app:layout_constraintStart_toEndOf="@+id/etValue1"
        app:layout_constraintTop_toTopOf="@+id/etValue1" />

</LinearLayout>