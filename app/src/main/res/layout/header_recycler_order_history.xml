<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvDate"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:drawablePadding="4dp"
        android:paddingEnd="@dimen/margin_horizontal_base"
        android:paddingBottom="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="01/03/2024 - 31/03/2024" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDepositTitle"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:text="@string/deposit"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvDeposit"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvDepositTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvDepositTitle"
            tools:text="234234.00" />

        <TextView
            android:id="@+id/tvWithdrawalTitle"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base_new"
            android:text="@string/withdrawal"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@+id/tvDepositTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvDepositTitle" />

        <TextView
            android:id="@+id/tvWithdrawal"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvWithdrawalTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvWithdrawalTitle"
            tools:text="123321.00" />

        <TextView
            android:id="@+id/tvCreditTitle"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base_new"
            android:text="@string/credit"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@+id/tvDepositTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvWithdrawalTitle" />

        <TextView
            android:id="@+id/tvCredit"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvCreditTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvCreditTitle"
            tools:text="123321.00" />

        <TextView
            android:id="@+id/tvClosedNetPnlTitle"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base_new"
            android:text="@string/closed_net_pnl"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@+id/tvDepositTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvCreditTitle" />

        <TextView
            android:id="@+id/tvClosedNetPnl"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvClosedNetPnlTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvClosedNetPnlTitle"
            tools:text="123321.00" />

        <TextView
            android:id="@+id/tvClosedPnlTitle"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base_new"
            android:layout_marginBottom="@dimen/margin_horizontal_base"
            android:text="@string/closed_pnl"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvDepositTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvClosedNetPnlTitle" />

        <TextView
            android:id="@+id/tvClosedPnl"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvClosedPnlTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvClosedPnlTitle"
            tools:text="123321.00" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/orderInfoGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tvDepositTitle,tvDeposit,tvWithdrawalTitle,tvWithdrawal,tvCreditTitle,tvCredit,tvClosedPnlTitle,tvClosedPnl" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        android:visibility="gone" />

</LinearLayout>