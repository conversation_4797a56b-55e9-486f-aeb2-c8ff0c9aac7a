<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="@dimen/margin_horizontal_base"
    android:paddingEnd="@dimen/margin_horizontal_base">

    <!-- 样式1 -->
    <LinearLayout
        android:id="@+id/llStyleX1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/margin_vertical_button">

        <TextView
            android:id="@+id/tvTitleStyleX1"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            tools:text="Replies" />

        <TextView
            android:id="@+id/tvContentStyleX1"
            style="@style/gilroy_400"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:lineSpacingExtra="@dimen/line_spacing_extra"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            tools:text="Guangdong - win-win partnership for Sino-Africa Likes Your Mood/Opinion" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="10dp"
            android:background="?attr/color_c0a1e1e1e_c262930" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvNameStyleX1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.25"
                android:paddingEnd="5dp"
                android:singleLine="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                android:visibility="gone"
                tools:text="Amy" />

            <TextView
                android:id="@+id/tvReplyStyleX1"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.75"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                tools:text="win-win partnership for Sino-Africa cooperation" />

        </LinearLayout>

    </LinearLayout>

    <!-- 样式2 -->
    <LinearLayout
        android:id="@+id/llStyleX2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/margin_vertical_button">

        <TextView
            android:id="@+id/tvTitleStyleX2"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:text="Follower"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvNameStyleX2"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.25"
                android:paddingEnd="5dp"
                android:singleLine="true"
                android:text="Aaron"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/tvContentStyleX2"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.75"
                android:gravity="end"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="Follows You"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp" />
        </LinearLayout>

    </LinearLayout>
    <!-- 共用样式日期 -->
    <TextView
        android:id="@+id/tvTime"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        tools:text="2017.7.26 20:38:37" />
</LinearLayout>