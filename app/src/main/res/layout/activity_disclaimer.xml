<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/disclaimer"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/margin_horizontal_base">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="Third-Party Authenticator Software Disclaimer"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                tools:ignore="HardcodedText" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text"
                style="@style/gilroy_400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                tools:ignore="HardcodedText" />
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>