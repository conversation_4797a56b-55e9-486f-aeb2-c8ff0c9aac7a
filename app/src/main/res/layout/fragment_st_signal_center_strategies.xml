<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/mTabLayout"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        android:layout_marginTop="8dp" />

    <cn.com.vau.common.view.NestedScrollableHost
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager2"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </cn.com.vau.common.view.NestedScrollableHost>
</LinearLayout>