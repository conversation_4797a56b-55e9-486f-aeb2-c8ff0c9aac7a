<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    android:maxWidth="280dp"
    android:minHeight="172dp"
    android:paddingBottom="20dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:lineSpacingExtra="8dp"
        android:text="@string/selecting_auto_reject_are_auto_reject"
        android:textAlignment="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAgree"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:drawableStart="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
        android:drawablePadding="8dp"
        android:gravity="center"
        android:text="@string/don_t_show_this_again"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@id/tvDetail"
        app:layout_constraintStart_toStartOf="@id/tvDetail"
        app:layout_constraintTop_toBottomOf="@+id/tvDetail" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCancel"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="30dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/cancel"
        android:textAlignment="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/tvConfirm"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvAgree" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvConfirm"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="30dp"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/confirm"
        android:textAlignment="center"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvCancel"
        app:layout_constraintTop_toTopOf="@+id/tvCancel" />

</androidx.constraintlayout.widget.ConstraintLayout>