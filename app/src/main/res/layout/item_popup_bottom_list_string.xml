<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:id="@+id/tv"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center"
        android:lineSpacingExtra="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>