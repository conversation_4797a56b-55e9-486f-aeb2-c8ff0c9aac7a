<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="@drawable/ripple_c0a1e1e1e_c0ab2b2b2"
    android:paddingVertical="@dimen/margin_vertical_button">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNum"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="32dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:shapeAppearanceOverlay="@style/roundImageStyle10" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@id/tvType"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        tools:text="Strategy Nickname XX" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvType"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/ivIcon"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        tools:text="ROI(3M)" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTypeNum"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvType"
        app:layout_constraintStart_toEndOf="@id/tvType"
        app:layout_constraintTop_toTopOf="@+id/tvType"
        tools:text="-100.00%" />
</androidx.constraintlayout.widget.ConstraintLayout>