<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/trades"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSell"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintStart_toEndOf="@+id/guideline_v31"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        app:layout_constraintBottom_toBottomOf="@+id/clBuy"
        app:layout_constraintEnd_toStartOf="@+id/clBuy"
        app:layout_constraintTop_toTopOf="@+id/clBuy">

        <TextView
            android:id="@+id/tvSellType"
            android:text="Sell"
            android:textSize="14dp"
            style="@style/bold_semi_font"
            app:layout_constraintVertical_chainStyle="packed"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvOrderSellRate"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/tvOrderSellRate"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingExtra="0dp"
            android:lineSpacingMultiplier="0"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="18dp"
            app:layout_constraintTop_toBottomOf="@+id/tvSellType"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="SpUsage"
            tools:text="0.15285" />

<!--        <ImageView-->
<!--            android:id="@+id/ivSellSelect"-->
<!--            android:layout_width="16dp"-->
<!--            android:layout_height="16dp"-->
<!--            android:contentDescription="@string/app_name"-->
<!--            android:src="@drawable/z_right_icon_trade_type_select_dark"-->
<!--            android:visibility="gone"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBuy"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginVertical="16dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        app:layout_constraintStart_toEndOf="@+id/clSell"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvBuyType"
            android:text="Buy"
            android:textSize="14dp"
            style="@style/bold_semi_font"
            app:layout_constraintVertical_chainStyle="packed"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvOrderBuyRate"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/tvOrderBuyRate"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:lineSpacingExtra="0dp"
            android:lineSpacingMultiplier="0"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="18dp"
            app:layout_constraintTop_toBottomOf="@+id/tvBuyType"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="SpUsage"
            tools:text="0.15285" />

<!--        <ImageView-->
<!--            android:id="@+id/ivBuySelect"-->
<!--            android:layout_width="16dp"-->
<!--            android:layout_height="16dp"-->
<!--            android:contentDescription="@string/app_name"-->
<!--            android:src="@drawable/z_right_icon_trade_type_select_dark"-->
<!--            android:visibility="gone"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v31"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.31" />

</androidx.constraintlayout.widget.ConstraintLayout>