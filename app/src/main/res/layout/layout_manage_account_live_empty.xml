<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/draw_main_card"
    android:layout_marginTop="16dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivOpenLive"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_account_mana"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvGuideOpenLiveTitle"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivOpenLive"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="@string/start_live_trading_vantage"/>

    <TextView
        android:id="@+id/tvGuideInfo1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:layout_marginHorizontal="50dp"
        android:drawableStart="?attr/imgOpenLiveGuide1"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:text="@string/more_than_1000_assets"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvGuideOpenLiveTitle"
        app:layout_constraintStart_toStartOf="@id/tvGuideOpenLiveTitle"/>

    <TextView
        android:id="@+id/tvGuideInfo2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="50dp"
        android:drawableStart="?attr/imgOpenLiveGuide2"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:text="@string/spreads_as_low_0"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvGuideInfo1"
        app:layout_constraintStart_toStartOf="@id/tvGuideOpenLiveTitle"/>

    <TextView
        android:id="@+id/tvGuideInfo3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="50dp"
        android:drawableStart="?attr/imgOpenLiveGuide3"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:text="@string/deposit_fee_0"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvGuideInfo2"
        app:layout_constraintStart_toStartOf="@id/tvGuideOpenLiveTitle"/>

    <TextView
        android:id="@+id/tvGuideInfo4"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="50dp"
        android:drawableStart="?attr/imgOpenLiveGuide4"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:text="@string/award_winning_customer_support"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvGuideInfo3"
        app:layout_constraintStart_toStartOf="@id/tvGuideOpenLiveTitle"/>
    
    <TextView
        android:id="@+id/tvOpenLiveAccount"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="18dp"
        android:layout_marginBottom="30dp"
        android:layout_marginHorizontal="52dp"
        android:gravity="center"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:text="@string/open_live_account"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        android:textSize="16dp"
        app:layout_constraintTop_toBottomOf="@id/tvGuideInfo4"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>