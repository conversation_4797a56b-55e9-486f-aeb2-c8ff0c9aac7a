<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="true"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/log_in"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/mTabLayout"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <View
        style="@style/TabLayoutBottomLineStyle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTabLayout" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/mViewPager2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTabLayout" />
</androidx.constraintlayout.widget.ConstraintLayout>