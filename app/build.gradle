apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: "kotlin-parcelize"
apply plugin: 'org.greenrobot.greendao'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.sensorsdata.analytics.android' //神策插件
apply plugin: 'com.google.firebase.firebase-perf' //Performance Monitoring Gradle 插件（搜集网络）

def static packageTime() {
    return new Date().format("yyyy-MM-dd_HHmmss")
}

android {

    // 关闭 PNG 合法性检查， 暂解决错误（ AAPT: error: file failed to compile.）
    aaptOptions.cruncherEnabled = false

    compileSdk 34

    defaultConfig {
        applicationId "cn.com.vau"
        minSdkVersion 23
        targetSdk 34
        versionCode 86
        versionName "3.72.0"
        multiDexEnabled true
        flavorDimensions "versionCode"
        vectorDrawables.useSupportLibrary = true
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        ndk {
            abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a'
        }
        buildConfigField "boolean", "OFFICE", "${parseLocalOfficeProperties("OFFICE")}"
        buildConfigField "boolean", "KIT_DEBUG", "${getLocalProperty('KIT_DEBUG', 'false')}"
    }

    bundle {
        language {
            /* Specifies that the app bundle should not support
             configuration APKs for language resources. These
             resources are instead packaged with each base and
             dynamic feature APK. */
            enableSplit = false
        }
    }

    signingConfigs {
        release {
            keyAlias 'vau'
            keyPassword '123456'
            storeFile file('vau.jks')
            storePassword '123456'
        }
        debug {
            keyAlias 'vau'
            keyPassword '123456'
            storeFile file('vau.jks')
            storePassword '123456'
        }
    }

    buildFeatures {
        buildConfig true
    }

    buildTypes {
        release {
            manifestPlaceholders = [Sensors_Schema: '"saed50685d"']
            debuggable true
            minifyEnabled true
            zipAlignEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }
        debug {
            manifestPlaceholders = [Sensors_Schema: '"saf1d3eb15"']
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }

    // 屏蔽 task (uploadCrashlyticsMappingFileRelease)
//    gradle.taskGraph.whenReady {
//        tasks.each { task ->
//            if (task.name.contains("uploadCrashlyticsMappingFileRelease")) {
//                task.enabled = false
//            }
//        }
//    }

    android.applicationVariants.configureEach { variant ->
        variant.outputs.configureEach { output ->
            def fileName
            if (variant.buildType.isDebuggable()) {
                fileName = "vau_v${defaultConfig.versionName}_${packageTime()}_beta.apk"
            } else {
                fileName = "vau_v${defaultConfig.versionName}_${packageTime()}_release.apk"
            }
            outputFileName = fileName
        }
    }

    dataBinding {
        enabled = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    buildFeatures {
        viewBinding true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    kotlin {
        jvmToolchain(17)
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
    }

    namespace 'cn.com.vau'

}

def parseLocalOfficeProperties(String key) {
    File file = rootProject.file('local.properties')
    String office = true
    if (file.exists()) {
        InputStream inputStream = rootProject.file('local.properties').newDataInputStream();
        Properties properties = new Properties()
        properties.load(inputStream)
        if (properties.containsKey(key)) {
            office = properties.getProperty(key)
        }
    }
    return office
}

def getLocalProperty(String key, String defaultValue) {
    File file = rootProject.file('local.properties')
    String value = defaultValue
    if (file.exists()) {
        InputStream inputStream = rootProject.file('local.properties').newDataInputStream();
        Properties properties = new Properties()
        properties.load(inputStream)
        if (properties.containsKey(key)) {
            value = properties.getProperty(key, defaultValue)
        }
    }
    return value
}


sensorsAnalytics {
    exclude = ['com.sumsub.sns']
}

repositories {
    maven { url 'https://zendesk.jfrog.io/zendesk/repo' }
    google()
    mavenCentral()
}

greendao {
    schemaVersion 22
    daoPackage 'cn.com.vau.common.greendao.common'
    targetGenDir 'src/main/java'
}

tasks.configureEach { task ->
    if (task.name.matches("compile\\w*Kotlin")) {
        task.dependsOn('greendao')
    }
}

dependencies {
    implementation project(':kline')
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    implementation 'androidx.annotation:annotation:1.8.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.12.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0-RC'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0-RC'
    // 下拉刷新
    implementation 'io.github.scwang90:refresh-layout-kernel:2.1.0'
    implementation 'io.github.scwang90:refresh-header-classics:2.1.0'
    // Rxjava
    implementation 'io.reactivex.rxjava2:rxjava:2.0.7'
    implementation 'io.reactivex.rxjava2:rxandroid:2.0.1'
    // retrofit
    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation 'com.squareup.retrofit2:adapter-rxjava2:2.4.0'
    // okhttp3
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    implementation "org.jetbrains.kotlin:kotlin-stdlib:2.0.0"
    implementation 'com.neovisionaries:nv-websocket-client:2.10'
    implementation 'org.greenrobot:greendao:3.3.0'
    implementation 'com.github.yuweiguocn:GreenDaoUpgradeHelper:v2.1.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'org.greenrobot:eventbus:3.3.1'
//    implementation 'com.github.LidongWen:MultiTypeAdapter:0.1.3'
    implementation 'com.github.penfeizhou.android.animation:glide-plugin:2.24.0'
    implementation 'com.github.penfeizhou.android.animation:gif:2.24.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'com.github.bumptech.glide:okhttp3-integration:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation 'com.github.chrisbanes:PhotoView:2.1.4'
//    implementation 'com.youth.banner:banner:1.4.10'
    // 轮播图：https://github.com/youth5201314/banner
    implementation 'io.github.youth5201314:banner:2.2.3'
    implementation 'cn.jzvd:jiaozivideoplayer:6.3.1'
    implementation 'com.github.lzyzsd:jsbridge:1.0.4'
    implementation 'com.appsflyer:af-android-sdk:6.14.2'
    implementation group: 'com.zendesk', name: 'chat', version: '3.4.0'
    implementation group: 'com.zendesk', name: 'messaging', version: '5.4.0'
    implementation("zendesk.messaging:messaging-android:2.23.0") {
        exclude group: 'androidx.activity'
    }
    implementation group: 'com.zendesk', name: 'support', version: '5.2.0'
    implementation group: 'com.zendesk', name: 'answerbot', version: '3.2.0'
    implementation 'com.facebook.android:facebook-android-sdk:17.0.0'
    //lifecycle
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.4'
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.8.4"
    // FCM
    implementation platform('com.google.firebase:firebase-bom:33.5.0')
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
//    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-config'
    implementation("com.google.firebase:firebase-perf")

    // https://firebase.google.com/support/release-notes/android
    // BREAKING CHANGE: With this release, the BoM no longer contains the following deprecated libraries:
    // firebase-appindexing, firebase-core, and firebase-iid. Use the following alternatives instead:
    // Instead of firebase-core, use firebase-analytics or firebase-analytics-ktx.
    // implementation 'com.google.firebase:firebase-core:21.1.1'

    implementation('com.github.rupertoL:SpecialView:1.2') {
        exclude group: 'com.github.bumptech.glide'
    }
    implementation 'com.adjust.sdk:adjust-android:4.24.1'
    implementation 'com.android.installreferrer:installreferrer:2.2'
    implementation "androidx.startup:startup-runtime:1.1.1"
    implementation "me.leolin:ShortcutBadger:1.1.22@aar"
    implementation "androidx.navigation:navigation-fragment-ktx:2.7.7"
    implementation "androidx.navigation:navigation-ui-ktx:2.7.7"
    implementation 'androidx.navigation:navigation-runtime-ktx:2.7.7'
    // mmkv 1.3.5 移除了armv7的so，所以暂不升级到1.3.5
    implementation 'com.tencent:mmkv-static:1.3.9'
    implementation "com.launchdarkly:okhttp-eventsource:2.6.1"
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'com.github.barteksc:android-pdf-viewer:3.2.0-beta.1'
    // 易盾短信验证
    implementation 'io.github.yidun:captcha:3.4.3'
    // Amazon
    implementation 'com.amazonaws:ivs-player:1.8.0'
    // exoplayer
    implementation 'com.google.android.exoplayer:exoplayer:2.14.1'
    implementation 'com.google.android.exoplayer:extension-rtmp:2.14.1'
//    implementation(name: 'meglive_still', ext: 'aar')
    // JsBridge
    implementation 'com.github.lzyzsd:jsbridge:1.0.4'
    // 全局异常捕获日志需要
    implementation 'com.jakewharton.timber:timber:4.7.1'
    // 解决一加手机Targeting S+ (version 31 and above) requires that one of FLAG_IMMUTABLE or FLAG_MUTABLE be specified when creating a PendingIntent.的问题
    implementation 'com.google.android.gms:play-services-base:18.4.0'
    // rv适配器：https://github.com/CymChad/BaseRecyclerViewAdapterHelper
    implementation "io.github.cymchad:BaseRecyclerViewAdapterHelper:3.0.14"
    // 检测模拟器
    implementation 'io.github.happylishang:antifake:1.7.1'
    // debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.10'
    // google flexbox
    // implementation 'com.google.android.flexbox:flexbox:3.0.0'
    //implementation 'com.github.li-xiaojun:XPopup:2.10.0'
    implementation 'com.airbnb.android:lottie:6.2.0'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'
    // 神策
    implementation 'com.sensorsdata.analytics.android:SensorsAnalyticsSDK:6.8.0'
    implementation 'com.sensorsdata.analytics.android:ThirdParty:0.0.4'
    // SumSub core (人脸识别SDK)
    implementation "com.sumsub.sns:idensic-mobile-sdk:$sumsub_version"
    // Video Identification module
//    implementation "com.sumsub.sns:idensic-mobile-sdk-videoident:$sumsub_version"
    // 华为 appsflyer sdk
    implementation 'com.huawei.hms:componentverifysdk:13.3.1.301'
    //passkey
    implementation("androidx.credentials:credentials:1.3.0")
    implementation("androidx.credentials:credentials-play-services-auth:1.3.0")
    // JWT库：https://github.com/jwtk/jjwt
    implementation("io.jsonwebtoken:jjwt-api:0.12.6")
    runtimeOnly("io.jsonwebtoken:jjwt-impl:0.12.6")
    runtimeOnly("io.jsonwebtoken:jjwt-jackson:0.12.6")

    debugApi project(':lib_kit')

    api project(':lib_xpopup')
}
