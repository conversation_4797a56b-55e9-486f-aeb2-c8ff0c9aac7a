package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
@Keep
public class CciIndexEntity extends BaseIndexEntity {
    public float cci;

    @Override
    public void reset() {
        super.reset();
        cci = 0f;
    }

    public float getCci() {
        return cci;
    }

    public void setCci(float cci) {
        this.cci = cci;
        setComputed(true);
    }

    @NonNull
    @Override
    public String toString() {
        return "CCIIndexEntity{" +
                "cci=" + cci +
                '}';
    }
}