package cn.com.vau.kit.constant;

import android.content.Context;

import cn.com.vau.kit.util.KitSpUtil;


public class FloatIconConfig {

    public static int getLastPosX(Context context) {
        return KitSpUtil.getInt(SharedPrefsKey.FLOAT_ICON_POS_X, 0);
    }

    public static int getLastPosY(Context context) {
        return KitSpUtil.getInt(SharedPrefsKey.FLOAT_ICON_POS_Y, 0);
    }

    public static void saveLastPosY(Context context, int val) {
        KitSpUtil.putInt(SharedPrefsKey.FLOAT_ICON_POS_Y, val);
    }

    public static void saveLastPosX(Context context, int val) {
        KitSpUtil.putInt(SharedPrefsKey.FLOAT_ICON_POS_X, val);
    }
}
