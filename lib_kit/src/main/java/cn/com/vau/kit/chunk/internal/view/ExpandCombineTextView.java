package cn.com.vau.kit.chunk.internal.view;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;


@Keep
public class ExpandCombineTextView extends TextView {


    private static final String EXPAND = " + ";

    private static final String COMBINE = " -...  ";
    //当前状态 展开还是折叠
    private boolean isExpand = false;
    private LinearLayout mContainer;
    private TextView mEndView;


    public ExpandCombineTextView(Context context) {
        super(context);
    }

    public ExpandCombineTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ExpandCombineTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    //获得纯文字 过滤掉【+】【-】
    private String getTextValue() {
        String value = getText().toString();
        int index = value.indexOf(EXPAND);
        if (index != -1) {
            return value.substring(index + EXPAND.length());
        }
        index = value.indexOf(COMBINE);
        if (index != -1) {
            return value.substring(index + COMBINE.length());
        }
        return value;
    }

    public void setExpand(boolean expand) {
        isExpand = expand;
    }

    //初始化View
    public void init() {
        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                isExpand = !isExpand;
                refreshVisible();
            }
        });
        refreshVisible();
    }

    //刷新折叠状态
    private void refreshVisible() {
        if (isExpand) {
            expandView();
        } else {
            combineView();
        }
    }

    public void onExpand() {
        isExpand = true;
        refreshVisible();
    }

    //渲染key:Value 时刻,key字段高亮
    private SpannableString createKeyHighLight(String text) {
        if (text == null) return null;
        int index = text.indexOf(":");
        if (index == -1) return new SpannableString(text);
        final SpannableString spannableString = new SpannableString(text);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#0D47A1")), 0, index, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    //展开View
    private void expandView() {
        mContainer.setVisibility(VISIBLE);
        mEndView.setVisibility(VISIBLE);
        setText(createKeyHighLight(COMBINE + getTextValue()));
    }

    //合并view
    private void combineView() {
        mContainer.setVisibility(GONE);
        mEndView.setVisibility(GONE);
        setText(createKeyHighLight(EXPAND + getTextValue()));
    }

    public void initView(LinearLayout container, TextView endView) {
        mContainer = container;
        mEndView = endView;
    }

}
